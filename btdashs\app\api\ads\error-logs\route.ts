import { NextRequest, NextResponse } from "next/server";

interface AdErrorLog {
  timestamp: string;
  slotId: number;
  errorType: 'paid_ad_failure' | 'adsense_failure' | 'complete_failure' | 'timeout' | 'network_error';
  errorMessage: string;
  userAgent?: string;
  url?: string;
  retryCount?: number;
  fallbackUsed?: 'google' | 'placeholder' | 'none';
  sessionId?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { errors } = body;

    if (!Array.isArray(errors)) {
      return NextResponse.json(
        { success: false, message: "Invalid request format" },
        { status: 400 }
      );
    }

    // Validate error logs
    const validErrors = errors.filter((error: any) => {
      return (
        error.timestamp &&
        typeof error.slotId === 'number' &&
        error.errorType &&
        error.errorMessage
      );
    });

    if (validErrors.length === 0) {
      return NextResponse.json(
        { success: false, message: "No valid error logs provided" },
        { status: 400 }
      );
    }

    // In production, you would send these to your logging service
    // For now, we'll log them to console and optionally store in database
    console.error('Ad Error Logs Received:', {
      count: validErrors.length,
      errors: validErrors,
      timestamp: new Date().toISOString(),
    });

    // Optional: Send to external logging service
    if (process.env.NODE_ENV === 'production') {
      await sendToLoggingService(validErrors);
    }

    // Optional: Store in database for analysis
    if (process.env.STORE_AD_LOGS === 'true') {
      await storeErrorLogs(validErrors);
    }

    return NextResponse.json({
      success: true,
      message: `Logged ${validErrors.length} error(s)`,
      processed: validErrors.length,
    });

  } catch (error) {
    console.error('Error processing ad error logs:', error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

async function sendToLoggingService(errors: AdErrorLog[]): Promise<void> {
  // Example integration with external logging service
  // Replace with your actual logging service (e.g., Sentry, LogRocket, etc.)
  
  try {
    // Example: Send to webhook or logging API
    if (process.env.LOGGING_WEBHOOK_URL) {
      await fetch(process.env.LOGGING_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.LOGGING_API_KEY}`,
        },
        body: JSON.stringify({
          source: 'btdash-ad-serving',
          level: 'error',
          errors,
          timestamp: new Date().toISOString(),
        }),
      });
    }
  } catch (error) {
    console.error('Failed to send to external logging service:', error);
    // Don't throw - we don't want to fail the request if external logging fails
  }
}

async function storeErrorLogs(errors: AdErrorLog[]): Promise<void> {
  // Example database storage
  // Replace with your actual database implementation
  
  try {
    // Example: Store in database for analysis
    const API_BASE = process.env.API_BASE_URL;
    const INTERNAL_API_KEY = process.env.INTERNAL_API_KEY;

    if (API_BASE && INTERNAL_API_KEY) {
      await fetch(`${API_BASE}/ad-error-logs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-internal-api-key': INTERNAL_API_KEY,
        },
        body: JSON.stringify({ errors }),
      });
    }
  } catch (error) {
    console.error('Failed to store error logs in database:', error);
    // Don't throw - we don't want to fail the request if database storage fails
  }
}

// Helper function to analyze error patterns
function analyzeErrorPatterns(errors: AdErrorLog[]): any {
  const analysis = {
    totalErrors: errors.length,
    errorsByType: {} as Record<string, number>,
    errorsBySlot: {} as Record<number, number>,
    timeRange: {
      start: errors[0]?.timestamp,
      end: errors[errors.length - 1]?.timestamp,
    },
    criticalErrors: errors.filter(e => e.errorType === 'complete_failure').length,
    retryPatterns: {} as Record<string, number>,
  };

  errors.forEach(error => {
    // Count by error type
    analysis.errorsByType[error.errorType] = (analysis.errorsByType[error.errorType] || 0) + 1;
    
    // Count by slot
    analysis.errorsBySlot[error.slotId] = (analysis.errorsBySlot[error.slotId] || 0) + 1;
    
    // Count retry patterns
    const retryKey = `${error.errorType}_${error.retryCount || 0}`;
    analysis.retryPatterns[retryKey] = (analysis.retryPatterns[retryKey] || 0) + 1;
  });

  return analysis;
}
