// app/api/user/campaigns/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const { token } = await auth0.getAccessToken();

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		console.error("Campaigns fetch error:", error);

		return NextResponse.json(
			{
				success: false,
				message: error.message || "Internal server error",
				error: error.code || "unknown_error",
			},
			{ status: 500 }
		);
	}
}

export async function POST(req: Request) {
	try {
		const { token } = await auth0.getAccessToken();
		const body = await req.json();

		console.log("Received campaign data:", body);

		// Get user info and company info
		const userResponse = await fetch(`${process.env.API_BASE_URL}/user/me`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!userResponse.ok) {
			throw new Error("Failed to get user information");
		}

		const userResult = await userResponse.json();
		const userId = userResult.success ? userResult.data.id : null;
		console.log("User lookup result:", userResult);

		if (!userId) {
			throw new Error("User ID not found");
		}

		// Get user's company info for advertiser_id
		const companyResponse = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		console.log("Company response status:", companyResponse.status);

		if (!companyResponse.ok) {
			const errorText = await companyResponse.text();
			console.error("Company lookup failed:", companyResponse.status, errorText);
			throw new Error(`Failed to get company information: ${errorText}`);
		}

		const companyResult = await companyResponse.json();
		console.log("Company lookup result:", companyResult);
		const companyId = companyResult.success ? companyResult.data.id : null;

		if (!companyId) {
			throw new Error("Company ID not found - user must have a company to create campaigns");
		}

		console.log("Using company ID:", companyId, "and user ID:", userId);

		// 1. First create the Campaign
		const campaignResponse = await fetch(`${process.env.API_BASE_URL}/campaigns`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${token}`,
			},
			body: JSON.stringify({
				advertiser_id: body.advertiser_id || companyId, // Use company ID as advertiser_id
				manager_id: userId, // Use user ID as manager_id
				name: body.name,
				start_date: body.start_date,
				end_date: body.end_date,
				total_budget: body.total_budget || null,
				budget_cpc: body.budget_cpc || null,
				budget_cpm: body.budget_cpm || null,
				// Note: targeting is handled at campaign level via ad_campaigns.targeting JSON column
			}),
		});

		console.log("Campaign response status:", campaignResponse.status);

		if (!campaignResponse.ok) {
			const errorText = await campaignResponse.text();
			console.error("Campaign creation failed:", campaignResponse.status, errorText);
			throw new Error(`Campaign creation failed: ${errorText}`);
		}

		const campaignResult = await campaignResponse.json();
		console.log("Campaign creation result:", campaignResult);

		// Handle campaign creation response
		if (!campaignResult.success) {
			console.error("Campaign creation unsuccessful:", campaignResult);
			return NextResponse.json(
				{ success: false, message: campaignResult.message, errors: campaignResult.errors },
				{ status: 400 }
			);
		}

		// 2. Create ads for each selected placement
		const createdAds = [];
		const placements = body.placements || [];

		if (placements.length === 0) {
			// Fallback for old single placement format
			const adData = {
				campaign_id: campaignResult.data.id,
				slot_id: body.slot_id || 1,
				title: body.title || body.name,
				image_url: body.image_url || "",
				target_url: body.target_url || body.destination_url,
				max_impressions: body.max_impressions || null,
				max_clicks: body.max_clicks || null,
				weight: body.weight || 1,
			};

			const adResponse = await fetch(`${process.env.API_BASE_URL}/ads`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${token}`,
				},
				body: JSON.stringify(adData),
			});

			if (!adResponse.ok) {
				const errorText = await adResponse.text();
				throw new Error(`Ad creation failed: ${errorText}`);
			}

			const adResult = await adResponse.json();
			if (!adResult.success) {
				return NextResponse.json(
					{ success: false, message: adResult.message, errors: adResult.errors },
					{ status: 400 }
				);
			}

			createdAds.push(adResult.data);
		} else {
			// Create multiple ads for multiple placements
			for (const placement of placements) {
				const adData = {
					campaign_id: campaignResult.data.id,
					slot_id: placement.slot_id,
					title: placement.title,
					image_url: placement.image_url || "",
					target_url: placement.target_url,
					max_impressions: placement.max_impressions || null,
					max_clicks: placement.max_clicks || null,
					weight: placement.weight || 1,
				};

				const adResponse = await fetch(`${process.env.API_BASE_URL}/ads`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						Authorization: `Bearer ${token}`,
					},
					body: JSON.stringify(adData),
				});

				if (!adResponse.ok) {
					const errorText = await adResponse.text();
					console.error(`Ad creation failed for slot ${placement.slot_id}:`, errorText);
					// Continue with other ads even if one fails
					continue;
				}

				const adResult = await adResponse.json();
				if (adResult.success && adResult.data) {
					createdAds.push(adResult.data);
				}
			}

			if (createdAds.length === 0) {
				return NextResponse.json(
					{ success: false, message: "Failed to create any ads for the campaign" },
					{ status: 400 }
				);
			}
		}

		// 3. Create targeting rules if provided (campaign-level targeting)
		if (body.targeting) {
			try {
				const targetingResponse = await fetch(
					`${process.env.API_BASE_URL}/campaigns/${campaignResult.data.id}/targeting`,
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							Authorization: `Bearer ${token}`,
						},
						body: JSON.stringify(body.targeting),
					}
				);

				console.log(`Campaign targeting response status:`, targetingResponse.status);

				if (!targetingResponse.ok) {
					const errorText = await targetingResponse.text();
					console.warn(
						`Campaign targeting creation failed (non-critical):`,
						targetingResponse.status,
						errorText
					);
					// Don't fail the entire campaign creation if targeting fails
				} else {
					const targetingResult = await targetingResponse.json();
					console.log(`Campaign targeting creation result:`, targetingResult);
				}
			} catch (error) {
				console.warn(`Campaign targeting creation error (non-critical):`, error);
				// Don't fail the entire campaign creation if targeting fails
			}
		}

		return NextResponse.json({
			success: true,
			data: {
				campaign: campaignResult.data,
				ads: createdAds,
			},
			message: `Campaign created successfully with ${createdAds.length} ad${createdAds.length !== 1 ? "s" : ""}`,
		});
	} catch (error: any) {
		return NextResponse.json(
			{
				success: false,
				message: error.message,
				details: error.stack,
			},
			{ status: 400 }
		);
	}
}
