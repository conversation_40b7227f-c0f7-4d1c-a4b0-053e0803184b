import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useMemo, useState } from "react";
import useSWR from "swr";

interface PlacementAdCreative {
	ad_title: string;
	ad_description: string;
	image_url: string;
	destination_url: string;
}

interface CampaignFormData {
	name: string;
	description: string;
	total_budget: number | null;
	budget_cpc: number | null;
	budget_cpm: number | null;
	budget_type: "cpc" | "cpm" | "total";
	start_date: Date | null;
	end_date: Date | null;
	// Multiple placements support
	selected_placements: number[];
	placement_creatives: Record<number, PlacementAdCreative>;
	targeting: {
		countries: string[];
		devices: string[];
		languages: string[];
		interests: string[];
		age_ranges: string[];
	};
}

export function AdCreativeStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	// Fetcher function for SWR to get slot details
	const fetcher = (url: string) => fetch(url).then((res) => res.json());
	const { data: slotsData } = useSWR("/api/placements", fetcher);
	const slots = slotsData?.data || [];

	// Get slot details for selected placements
	const selectedSlots = slots.filter((slot: any) => formData.selected_placements.includes(slot.id));

	// Update creative for a specific placement
	const updatePlacementCreative = (placementId: number, field: keyof PlacementAdCreative, value: string) => {
		const currentCreatives = formData.placement_creatives;
		const updatedCreatives = {
			...currentCreatives,
			[placementId]: {
				...currentCreatives[placementId],
				[field]: value,
			},
		};

		updateFormData({ placement_creatives: updatedCreatives });
	};

	if (formData.selected_placements.length === 0) {
		return (
			<div className="text-center py-8">
				<p className="text-sm text-muted-foreground">
					Please select at least one placement in the previous step to create ad creatives.
				</p>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div>
				<Label className="text-base font-medium">Create Ad Creatives</Label>
				<p className="text-sm text-muted-foreground mb-4">
					Create separate ad creatives for each selected placement. Each placement may have different
					dimensions and requirements.
				</p>
			</div>

			{selectedSlots.map((slot: any, index: number) => {
				const creative = formData.placement_creatives[slot.id] || {
					ad_title: "",
					ad_description: "",
					image_url: "",
					destination_url: "",
				};

				return (
					<div key={slot.id} className="border rounded-lg p-4 space-y-4">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="font-medium">{slot.name}</h3>
								<div className="flex items-center gap-2 mt-1">
									<Badge variant="secondary" className="text-xs">
										{slot.page === "all"
											? "All Pages"
											: slot.page === "home"
											? "Home"
											: slot.page === "subnets"
											? "Subnets"
											: slot.page === "companies"
											? "Companies"
											: slot.page}
									</Badge>
									<Badge variant="outline" className="text-xs">
										{slot.width} × {slot.height}px
									</Badge>
								</div>
							</div>
						</div>

						<div className="grid gap-4">
							<div>
								<Label htmlFor={`ad-title-${slot.id}`}>Ad Title *</Label>
								<Input
									id={`ad-title-${slot.id}`}
									value={creative.ad_title}
									onChange={(e) => updatePlacementCreative(slot.id, "ad_title", e.target.value)}
									placeholder="Enter ad title"
									maxLength={60}
								/>
								<p className="text-xs text-muted-foreground mt-1">
									{creative.ad_title.length}/60 characters
								</p>
							</div>

							<div>
								<Label htmlFor={`ad-description-${slot.id}`}>Ad Description</Label>
								<Textarea
									id={`ad-description-${slot.id}`}
									value={creative.ad_description}
									onChange={(e) => updatePlacementCreative(slot.id, "ad_description", e.target.value)}
									placeholder="Enter ad description"
									rows={3}
									maxLength={150}
								/>
								<p className="text-xs text-muted-foreground mt-1">
									{creative.ad_description.length}/150 characters
								</p>
							</div>

							<div>
								<Label htmlFor={`image-url-${slot.id}`}>Image URL *</Label>
								<Input
									id={`image-url-${slot.id}`}
									value={creative.image_url}
									onChange={(e) => updatePlacementCreative(slot.id, "image_url", e.target.value)}
									placeholder="https://example.com/image.jpg"
									type="url"
								/>
								<p className="text-xs text-muted-foreground mt-1">
									Recommended size: {slot.width} × {slot.height}px
								</p>
							</div>

							<div>
								<Label htmlFor={`destination-url-${slot.id}`}>Destination URL *</Label>
								<Input
									id={`destination-url-${slot.id}`}
									value={creative.destination_url}
									onChange={(e) =>
										updatePlacementCreative(slot.id, "destination_url", e.target.value)
									}
									placeholder="https://example.com"
									type="url"
								/>
							</div>

							{creative.image_url && (
								<div>
									<Label>Preview</Label>
									<div className="border rounded-lg p-4 max-w-sm">
										<img
											src={creative.image_url}
											alt="Ad preview"
											className="w-full h-32 object-cover rounded mb-2"
											onError={(e) => {
												e.currentTarget.style.display = "none";
											}}
										/>
										<h4 className="font-medium text-sm">{creative.ad_title}</h4>
										<p className="text-xs text-muted-foreground">{creative.ad_description}</p>
									</div>
								</div>
							)}
						</div>
					</div>
				);
			})}
		</div>
	);
}

export function TargetingStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	const countries = ["US", "CA", "GB", "AU", "DE", "FR", "JP", "BR", "IN", "CN"];
	const devices = ["desktop", "mobile", "tablet"];
	const languages = ["en", "es", "fr", "de", "pt", "ja", "zh", "hi", "ar", "ru"];
	const interests = [
		"technology",
		"finance",
		"health",
		"education",
		"entertainment",
		"sports",
		"travel",
		"food",
		"fashion",
		"gaming",
	];
	const ageRanges = ["18-24", "25-34", "35-44", "45-54", "55-64", "65+"];

	const toggleArrayItem = (array: string[], item: string) => {
		return array.includes(item) ? array.filter((i) => i !== item) : [...array, item];
	};

	return (
		<div className="space-y-4">
			<div>
				<Label className="text-sm font-medium">Countries</Label>
				<p className="text-xs text-muted-foreground mb-2">Select target countries (all selected by default)</p>
				<div className="grid grid-cols-3 md:grid-cols-6 gap-2">
					{countries.map((country) => (
						<div key={country} className="flex items-center space-x-2">
							<Checkbox
								id={`country-${country}`}
								checked={formData.targeting.countries.includes(country)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											countries: toggleArrayItem(formData.targeting.countries, country),
										},
									});
								}}
							/>
							<Label htmlFor={`country-${country}`} className="text-sm">
								{country}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-sm font-medium">Devices</Label>
				<p className="text-xs text-muted-foreground mb-2">Select target devices (all selected by default)</p>
				<div className="flex gap-4">
					{devices.map((device) => (
						<div key={device} className="flex items-center space-x-2">
							<Checkbox
								id={`device-${device}`}
								checked={formData.targeting.devices.includes(device)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											devices: toggleArrayItem(formData.targeting.devices, device),
										},
									});
								}}
							/>
							<Label htmlFor={`device-${device}`} className="text-sm capitalize">
								{device}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-sm font-medium">Languages</Label>
				<p className="text-xs text-muted-foreground mb-2">Select target languages (all selected by default)</p>
				<div className="grid grid-cols-3 md:grid-cols-6 gap-2">
					{languages.map((language) => (
						<div key={language} className="flex items-center space-x-2">
							<Checkbox
								id={`language-${language}`}
								checked={formData.targeting.languages.includes(language)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											languages: toggleArrayItem(formData.targeting.languages, language),
										},
									});
								}}
							/>
							<Label htmlFor={`language-${language}`} className="text-sm uppercase">
								{language}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-base font-medium">Interests</Label>
				<p className="text-sm text-muted-foreground mb-3">
					Select target interests (leave empty for all interests)
				</p>
				<div className="grid grid-cols-2 md:grid-cols-3 gap-2">
					{interests.map((interest) => (
						<div key={interest} className="flex items-center space-x-2">
							<Checkbox
								id={`interest-${interest}`}
								checked={formData.targeting.interests.includes(interest)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											interests: toggleArrayItem(formData.targeting.interests, interest),
										},
									});
								}}
							/>
							<Label htmlFor={`interest-${interest}`} className="text-sm capitalize">
								{interest}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-base font-medium">Age Ranges</Label>
				<p className="text-sm text-muted-foreground mb-3">
					Select target age ranges (leave empty for all ages)
				</p>
				<div className="flex flex-wrap gap-2">
					{ageRanges.map((ageRange) => (
						<div key={ageRange} className="flex items-center space-x-2">
							<Checkbox
								id={`age-${ageRange}`}
								checked={formData.targeting.age_ranges.includes(ageRange)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											age_ranges: toggleArrayItem(formData.targeting.age_ranges, ageRange),
										},
									});
								}}
							/>
							<Label htmlFor={`age-${ageRange}`} className="text-sm">
								{ageRange}
							</Label>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}

export function ReviewStep({ formData }: { formData: CampaignFormData }) {
	// Fetcher function for SWR to get slot details
	const fetcher = (url: string) => fetch(url).then((res) => res.json());
	const { data: slotsData } = useSWR("/api/placements", fetcher);
	const slots = slotsData?.data || [];

	// Get slot details for selected placements
	const selectedSlots = slots.filter((slot: any) => formData.selected_placements.includes(slot.id));

	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-medium mb-4">Campaign Summary</h3>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label className="text-sm font-medium">Campaign Name</Label>
						<p className="text-sm">{formData.name}</p>
					</div>
					<div>
						<Label className="text-sm font-medium">Budget</Label>
						<p className="text-sm">
							{formData.budget_type === "total" && `$${formData.total_budget} total`}
							{formData.budget_type === "cpc" && `$${formData.budget_cpc} per click`}
							{formData.budget_type === "cpm" && `$${formData.budget_cpm} per 1000 impressions`}
						</p>
					</div>
					<div>
						<Label className="text-sm font-medium">Start Date</Label>
						<p className="text-sm">{formData.start_date?.toLocaleDateString()}</p>
					</div>
					<div>
						<Label className="text-sm font-medium">End Date</Label>
						<p className="text-sm">{formData.end_date?.toLocaleDateString()}</p>
					</div>
					<div>
						<Label className="text-sm font-medium">Selected Placements</Label>
						<p className="text-sm">
							{formData.selected_placements.length} placement
							{formData.selected_placements.length !== 1 ? "s" : ""} selected
						</p>
					</div>
				</div>
			</div>

			<div>
				<h3 className="text-lg font-medium mb-4">Ad Placements & Creatives</h3>
				<div className="space-y-4">
					{selectedSlots.map((slot: any) => {
						const creative = formData.placement_creatives[slot.id];

						return (
							<div key={slot.id} className="border rounded-lg p-4">
								<div className="flex items-start gap-4">
									<div className="flex-1">
										<div className="flex items-center gap-2 mb-2">
											<h4 className="font-medium">{slot.name}</h4>
											<Badge variant="secondary" className="text-xs">
												{slot.page === "all"
													? "All Pages"
													: slot.page === "home"
													? "Home"
													: slot.page === "subnets"
													? "Subnets"
													: slot.page === "companies"
													? "Companies"
													: slot.page}
											</Badge>
											<Badge variant="outline" className="text-xs">
												{slot.width} × {slot.height}px
											</Badge>
										</div>

										{creative && (
											<div className="space-y-2">
												<div>
													<Label className="text-xs font-medium">Title:</Label>
													<p className="text-sm">{creative.ad_title}</p>
												</div>
												{creative.ad_description && (
													<div>
														<Label className="text-xs font-medium">Description:</Label>
														<p className="text-sm text-muted-foreground">
															{creative.ad_description}
														</p>
													</div>
												)}
												<div>
													<Label className="text-xs font-medium">Destination URL:</Label>
													<p className="text-xs text-muted-foreground">
														{creative.destination_url}
													</p>
												</div>
											</div>
										)}
									</div>

									{creative?.image_url && (
										<div className="flex-shrink-0">
											<img
												src={creative.image_url}
												alt="Ad preview"
												className="w-24 h-24 object-cover rounded border"
												onError={(e) => {
													e.currentTarget.style.display = "none";
												}}
											/>
										</div>
									)}
								</div>
							</div>
						);
					})}
				</div>
			</div>

			<div>
				<h3 className="text-lg font-medium mb-4">Targeting</h3>
				<div className="space-y-2">
					{formData.targeting.countries.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Countries:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.countries.map((country) => (
									<Badge key={country} variant="secondary">
										{country}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.devices.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Devices:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.devices.map((device) => (
									<Badge key={device} variant="secondary">
										{device}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.languages.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Languages:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.languages.map((language) => (
									<Badge key={language} variant="secondary">
										{language}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.interests.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Interests:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.interests.map((interest) => (
									<Badge key={interest} variant="secondary">
										{interest}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.age_ranges.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Age Ranges:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.age_ranges.map((ageRange) => (
									<Badge key={ageRange} variant="secondary">
										{ageRange}
									</Badge>
								))}
							</div>
						</div>
					)}
					{Object.values(formData.targeting).every((arr) => arr.length === 0) && (
						<p className="text-sm text-muted-foreground">
							No targeting restrictions - will show to all users
						</p>
					)}
				</div>
			</div>
		</div>
	);
}

export function PlacementStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	// Fetcher function for SWR
	const fetcher = (url: string) => fetch(url).then((res) => res.json());

	// Fetch available ad slots
	const { data: slotsData, error, isLoading } = useSWR("/api/placements", fetcher);

	// Local state for filtering and sorting
	const [pageFilter, setPageFilter] = useState<string>("all");
	const [sortBy, setSortBy] = useState<string>("name");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

	const slots = slotsData?.data || [];

	// Filtered and sorted slots
	const filteredAndSortedSlots = useMemo(() => {
		let filtered = slots;

		// Apply page filter
		if (pageFilter !== "all") {
			filtered = slots.filter((slot: any) => slot.page === pageFilter);
		}

		// Apply sorting
		filtered.sort((a: any, b: any) => {
			let aValue, bValue;

			switch (sortBy) {
				case "price_cpc":
					aValue = a.price_cpc || 0;
					bValue = b.price_cpc || 0;
					break;
				case "price_cpm":
					aValue = a.price_cpm || 0;
					bValue = b.price_cpm || 0;
					break;
				case "estimated_views":
					aValue = a.estimated_views || 0;
					bValue = b.estimated_views || 0;
					break;
				case "name":
				default:
					aValue = a.name.toLowerCase();
					bValue = b.name.toLowerCase();
					break;
			}

			if (aValue < bValue) return sortOrder === "asc" ? -1 : 1;
			if (aValue > bValue) return sortOrder === "asc" ? 1 : -1;
			return 0;
		});

		return filtered;
	}, [slots, pageFilter, sortBy, sortOrder]);

	// Handle placement selection
	const handlePlacementToggle = (slotId: number) => {
		const currentSelections = formData.selected_placements;
		const isSelected = currentSelections.includes(slotId);

		if (isSelected) {
			// Remove from selection
			const newSelections = currentSelections.filter((id) => id !== slotId);
			const newCreatives = { ...formData.placement_creatives };
			delete newCreatives[slotId];

			updateFormData({
				selected_placements: newSelections,
				placement_creatives: newCreatives,
			});
		} else {
			// Add to selection
			const newSelections = [...currentSelections, slotId];
			const newCreatives = {
				...formData.placement_creatives,
				[slotId]: {
					ad_title: "",
					ad_description: "",
					image_url: "",
					destination_url: "",
				},
			};

			updateFormData({
				selected_placements: newSelections,
				placement_creatives: newCreatives,
			});
		}
	};

	// Get unique page types for filter
	const pageTypes = useMemo(() => {
		const types = [...new Set(slots.map((slot: any) => slot.page))];
		return types.filter((type) => type !== "all");
	}, [slots]);

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading ad placements...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-8">
				<p className="text-sm text-destructive">Failed to load ad placements. Please try again.</p>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div>
				<Label className="text-base font-medium">Choose Ad Placements</Label>
				<p className="text-sm text-muted-foreground mb-4">
					Select one or more placements where you want your ads to appear. You'll create separate ad creatives
					for each selected placement in the next step.
				</p>
			</div>

			{/* Filtering and Sorting Controls */}
			<div className="flex flex-col sm:flex-row gap-4 p-4 bg-muted/50 rounded-lg">
				<div className="flex-1">
					<Label htmlFor="page-filter" className="text-sm font-medium">
						Filter by Page Type
					</Label>
					<Select value={pageFilter} onValueChange={setPageFilter}>
						<SelectTrigger className="mt-1">
							<SelectValue placeholder="All pages" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Pages</SelectItem>
							{pageTypes.map((type) => (
								<SelectItem key={type as string} value={type as string}>
									{type === "home"
										? "Home"
										: type === "subnets"
										? "Subnets"
										: type === "companies"
										? "Companies"
										: String(type)}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="flex-1">
					<Label htmlFor="sort-by" className="text-sm font-medium">
						Sort by
					</Label>
					<Select value={sortBy} onValueChange={setSortBy}>
						<SelectTrigger className="mt-1">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="name">Name</SelectItem>
							<SelectItem value="price_cpc">CPC Price</SelectItem>
							<SelectItem value="price_cpm">CPM Price</SelectItem>
							<SelectItem value="estimated_views">Estimated Views</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="flex-1">
					<Label htmlFor="sort-order" className="text-sm font-medium">
						Order
					</Label>
					<Select value={sortOrder} onValueChange={(value: "asc" | "desc") => setSortOrder(value)}>
						<SelectTrigger className="mt-1">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="asc">Ascending</SelectItem>
							<SelectItem value="desc">Descending</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Selection Summary */}
			{formData.selected_placements.length > 0 && (
				<div className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
					<p className="text-sm font-medium text-primary">
						{formData.selected_placements.length} placement
						{formData.selected_placements.length !== 1 ? "s" : ""} selected
					</p>
				</div>
			)}

			{/* Placement Cards */}
			<div className="grid gap-4">
				{filteredAndSortedSlots.map((slot: any) => {
					const isSelected = formData.selected_placements.includes(slot.id);

					return (
						<div
							key={slot.id}
							className={`border rounded-lg p-4 cursor-pointer transition-colors ${
								isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"
							}`}
							onClick={() => handlePlacementToggle(slot.id)}
						>
							<div className="flex items-start justify-between">
								<div className="flex-1">
									<div className="flex items-center gap-2 mb-2">
										<Checkbox
											checked={isSelected}
											onChange={() => handlePlacementToggle(slot.id)}
											className="text-primary"
										/>
										<h3 className="font-medium">{slot.name}</h3>
										<Badge variant="secondary" className="text-xs">
											{slot.page === "all"
												? "All Pages"
												: slot.page === "home"
												? "Home"
												: slot.page === "subnets"
												? "Subnets"
												: slot.page === "companies"
												? "Companies"
												: slot.page}
										</Badge>
									</div>
									<p className="text-sm text-muted-foreground mb-3">
										{slot.description || `Ad slot for ${slot.page} page`}
									</p>
									<div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
										<div>
											<span className="font-medium">Size:</span>
											<br />
											{slot.width} × {slot.height}px
										</div>
										<div>
											<span className="font-medium">Est. Views:</span>
											<br />
											{slot.estimated_views?.toLocaleString() || "N/A"}
										</div>
										<div>
											<span className="font-medium">CPC:</span>
											<br />${slot.price_cpc || "N/A"}
										</div>
										<div>
											<span className="font-medium">CPM:</span>
											<br />${slot.price_cpm || "N/A"}
										</div>
									</div>
								</div>
							</div>
						</div>
					);
				})}
			</div>

			{filteredAndSortedSlots.length === 0 && slots.length > 0 && (
				<div className="text-center py-8">
					<p className="text-sm text-muted-foreground">No placements match the current filters.</p>
					<Button
						variant="outline"
						size="sm"
						className="mt-2"
						onClick={() => {
							setPageFilter("all");
							setSortBy("name");
							setSortOrder("asc");
						}}
					>
						Clear Filters
					</Button>
				</div>
			)}

			{slots.length === 0 && (
				<div className="text-center py-8">
					<p className="text-sm text-muted-foreground">No ad placements available at the moment.</p>
				</div>
			)}
		</div>
	);
}
