"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/ads/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/ads/create/client-wrapper.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/ads/create/client-wrapper.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateAdClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n// app/dashboard/ads/create/client-wrapper.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateAdClientWrapper(param) {\n    let { placementId, campaignId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)({\n        \"CreateAdClientWrapper.useState\": ()=>{\n            if (campaignId) return \"existing\";\n            if (campaigns.length === 0) return \"new\";\n            return null;\n        }\n    }[\"CreateAdClientWrapper.useState\"]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)({\n        title: \"\",\n        image_url: \"\",\n        target_url: \"\",\n        max_impressions: \"\",\n        max_clicks: \"\",\n        weight: \"1\",\n        campaign_id: campaignId || \"\",\n        // New campaign fields\n        campaign_name: \"\",\n        total_budget: \"\",\n        budget_cpc: \"\",\n        budget_cpm: \"\",\n        start_date: \"\",\n        end_date: \"\"\n    });\n    // Filter campaigns to only show pending ones\n    const activeCampaigns = campaigns.filter((c)=>c.status === \"pending\");\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedOption) {\n            toast({\n                title: \"Selection Required\",\n                description: \"Please choose to add to existing campaign or create new campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            if (selectedOption === \"new\") {\n                // Create new campaign with ad\n                const campaignData = {\n                    name: formData.campaign_name,\n                    total_budget: formData.total_budget ? parseFloat(formData.total_budget) : null,\n                    budget_cpc: formData.budget_cpc ? parseFloat(formData.budget_cpc) : null,\n                    budget_cpm: formData.budget_cpm ? parseFloat(formData.budget_cpm) : null,\n                    start_date: formData.start_date,\n                    end_date: formData.end_date,\n                    // Ad data\n                    ad_title: formData.title,\n                    image_url: formData.image_url,\n                    target_url: formData.target_url,\n                    max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,\n                    max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,\n                    weight: parseInt(formData.weight),\n                    slot_id: placementId ? parseInt(placementId) : null\n                };\n                const response = await fetch(\"/api/user/campaigns\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(campaignData)\n                });\n                if (!response.ok) {\n                    let errorMessage = \"Failed to create campaign\";\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (jsonError) {\n                        // If response is not JSON, use status text\n                        errorMessage = \"\".concat(response.status, \": \").concat(response.statusText);\n                    }\n                    throw new Error(errorMessage);\n                }\n                const result = await response.json();\n                if (result.success) {\n                    toast({\n                        title: \"Campaign Created\",\n                        description: \"Your campaign and ad have been created successfully.\"\n                    });\n                    router.push(\"/dashboard/campaigns/\".concat(result.data.campaign.id));\n                }\n            } else {\n                // Add ad to existing campaign\n                const adData = {\n                    campaign_id: parseInt(formData.campaign_id),\n                    slot_id: placementId ? parseInt(placementId) : null,\n                    title: formData.title,\n                    image_url: formData.image_url,\n                    target_url: formData.target_url,\n                    max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,\n                    max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,\n                    weight: parseInt(formData.weight)\n                };\n                const response = await fetch(\"/api/user/ads\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(adData)\n                });\n                if (!response.ok) {\n                    let errorMessage = \"Failed to create ad\";\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (jsonError) {\n                        // If response is not JSON, use status text\n                        errorMessage = \"\".concat(response.status, \": \").concat(response.statusText);\n                    }\n                    throw new Error(errorMessage);\n                }\n                const result = await response.json();\n                if (result.success) {\n                    toast({\n                        title: \"Ad Created\",\n                        description: \"Your ad has been created successfully.\"\n                    });\n                    router.push(\"/dashboard/campaigns/\".concat(formData.campaign_id));\n                }\n            }\n        } catch (error) {\n            console.error(\"Error creating ad:\", error);\n            toast({\n                title: \"Creation Failed\",\n                description: error instanceof Error ? error.message : \"Failed to create ad\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                            href: \"/dashboard/placements\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 8\n                                    }, this),\n                                    \"Back to Placements\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Create New Ad\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Add an ad to an existing campaign or create a new campaign\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                !campaignId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Campaign Selection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Choose whether to add this ad to an existing campaign or create a new one\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        activeCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    id: \"existing\",\n                                                                    name: \"campaign-option\",\n                                                                    checked: selectedOption === \"existing\",\n                                                                    onChange: ()=>setSelectedOption(\"existing\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"existing\",\n                                                                    children: \"Add to existing campaign\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    id: \"new\",\n                                                                    name: \"campaign-option\",\n                                                                    checked: selectedOption === \"new\",\n                                                                    onChange: ()=>setSelectedOption(\"new\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"new\",\n                                                                    children: \"Create new campaign\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 10\n                                                }, this),\n                                                selectedOption === \"existing\" && activeCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"campaign\",\n                                                            children: \"Select Campaign\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: formData.campaign_id,\n                                                            onValueChange: (value)=>handleInputChange(\"campaign_id\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Choose a campaign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: activeCampaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: campaign.id.toString(),\n                                                                            children: [\n                                                                                campaign.name,\n                                                                                \" (\",\n                                                                                campaign.status,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, campaign.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 11\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Ad Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: placement ? \"Creating ad for \".concat(placement.name) : \"Enter your ad information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    selectedOption === \"new\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-medium\",\n                                                                        children: \"Campaign Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"campaign_name\",\n                                                                                        children: \"Campaign Name *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 276,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"campaign_name\",\n                                                                                        value: formData.campaign_name,\n                                                                                        onChange: (e)=>handleInputChange(\"campaign_name\", e.target.value),\n                                                                                        placeholder: \"Enter campaign name\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 277,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"total_budget\",\n                                                                                        children: \"Total Budget ($)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 288,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"total_budget\",\n                                                                                        type: \"number\",\n                                                                                        step: \"0.01\",\n                                                                                        value: formData.total_budget,\n                                                                                        onChange: (e)=>handleInputChange(\"total_budget\", e.target.value),\n                                                                                        placeholder: \"0.00\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 289,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"start_date\",\n                                                                                        children: \"Start Date *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 303,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"start_date\",\n                                                                                        type: \"date\",\n                                                                                        value: formData.start_date,\n                                                                                        onChange: (e)=>handleInputChange(\"start_date\", e.target.value),\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 304,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"end_date\",\n                                                                                        children: \"End Date *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 315,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"end_date\",\n                                                                                        type: \"date\",\n                                                                                        value: formData.end_date,\n                                                                                        onChange: (e)=>handleInputChange(\"end_date\", e.target.value),\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 316,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Ad Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"title\",\n                                                                        children: \"Ad Title *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"title\",\n                                                                        value: formData.title,\n                                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                                        placeholder: \"Enter ad title\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"image_url\",\n                                                                        children: \"Image URL *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"image_url\",\n                                                                        type: \"url\",\n                                                                        value: formData.image_url,\n                                                                        onChange: (e)=>handleInputChange(\"image_url\", e.target.value),\n                                                                        placeholder: \"https://example.com/image.jpg\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground mt-1\",\n                                                                        children: [\n                                                                            \"Required dimensions: \",\n                                                                            placement.width,\n                                                                            \"x\",\n                                                                            placement.height,\n                                                                            \"px\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    formData.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: \"Preview:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 relative w-full max-w-md border rounded-lg overflow-hidden bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: formData.image_url,\n                                                                                        alt: \"Ad preview\",\n                                                                                        className: \"w-full h-auto object-cover\",\n                                                                                        onError: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.style.display = \"none\";\n                                                                                            const errorDiv = target.nextElementSibling;\n                                                                                            if (errorDiv) errorDiv.style.display = \"block\";\n                                                                                        },\n                                                                                        onLoad: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.style.display = \"block\";\n                                                                                            const errorDiv = target.nextElementSibling;\n                                                                                            if (errorDiv) errorDiv.style.display = \"none\";\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 365,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"hidden p-4 text-center text-sm text-red-600\",\n                                                                                        style: {\n                                                                                            display: \"none\"\n                                                                                        },\n                                                                                        children: \"Failed to load image. Please check the URL.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 384,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: [\n                                                                                    \"Target: \",\n                                                                                    placement.width,\n                                                                                    \"x\",\n                                                                                    placement.height,\n                                                                                    \"px\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"target_url\",\n                                                                        children: \"Target URL *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"target_url\",\n                                                                        type: \"url\",\n                                                                        value: formData.target_url,\n                                                                        onChange: (e)=>handleInputChange(\"target_url\", e.target.value),\n                                                                        placeholder: \"https://example.com\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"max_impressions\",\n                                                                                children: \"Max Impressions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"max_impressions\",\n                                                                                type: \"number\",\n                                                                                value: formData.max_impressions,\n                                                                                onChange: (e)=>handleInputChange(\"max_impressions\", e.target.value),\n                                                                                placeholder: \"Unlimited\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"max_clicks\",\n                                                                                children: \"Max Clicks\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"max_clicks\",\n                                                                                type: \"number\",\n                                                                                value: formData.max_clicks,\n                                                                                onChange: (e)=>handleInputChange(\"max_clicks\", e.target.value),\n                                                                                placeholder: \"Unlimited\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"weight\",\n                                                                                children: \"Weight\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 434,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"weight\",\n                                                                                type: \"number\",\n                                                                                min: \"1\",\n                                                                                max: \"1000\",\n                                                                                value: formData.weight,\n                                                                                onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        disabled: submitting,\n                                                        children: submitting ? \"Creating...\" : selectedOption === \"new\" ? \"Create Campaign & Ad\" : \"Create Ad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Selected Placement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Name:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Page:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.page\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Dimensions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: [\n                                                                    placement.width,\n                                                                    \"x\",\n                                                                    placement.height,\n                                                                    \"px\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    placement.allowed_ad_types && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Formats:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 13\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.allowed_ad_types.join(\", \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 13\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Tips\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Use high-quality images that match the placement dimensions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Write compelling ad titles that grab attention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Set reasonable impression and click limits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Higher weight gives your ad more visibility\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n            lineNumber: 186,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n        lineNumber: 185,\n        columnNumber: 3\n    }, this);\n}\n_s(CreateAdClientWrapper, \"pVUI3SjRxLWe53IRfQRN0Tckl3A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CreateAdClientWrapper;\nvar _c;\n$RefreshReg$(_c, \"CreateAdClientWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/ads/create/client-wrapper.tsx\n"));

/***/ })

});