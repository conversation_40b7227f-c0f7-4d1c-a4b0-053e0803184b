const PaymentMonitoringService = require("../../application/services/PaymentMonitoringService");
const AlertNotificationService = require("../../application/services/AlertNotificationService");
const { sendSuccess, sendError, sendUnauthorized, sendForbidden } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");
const { isAdminUser } = require("../../application/services/AdminService");
const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

const paymentMonitoring = new PaymentMonitoringService();

/**
 * Get current system health status
 * Admin only endpoint
 */
const getSystemHealth = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const systemHealth = await paymentMonitoring.checkSystemHealth();
		return sendSuccess(res, systemHealth, "System health retrieved");
	} catch (error) {
		return sendError(res, "Failed to get system health", error.message, 500);
	}
});

/**
 * Get payment monitoring statistics
 * Admin only endpoint
 */
const getPaymentStats = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const { timeframe = "24h" } = req.query;

		let startTime,
			endTime = new Date();

		switch (timeframe) {
			case "1h":
				startTime = new Date(endTime.getTime() - 60 * 60 * 1000);
				break;
			case "24h":
				startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000);
				break;
			case "7d":
				startTime = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000);
				break;
			case "30d":
				startTime = new Date(endTime.getTime() - 30 * 24 * 60 * 60 * 1000);
				break;
			default:
				startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000);
		}

		const stats = await paymentMonitoring.getPaymentStats(startTime, endTime);
		return sendSuccess(
			res,
			{
				...stats,
				timeframe,
				requestedAt: endTime.toISOString(),
			},
			"Payment statistics retrieved"
		);
	} catch (error) {
		return sendError(res, "Failed to get payment statistics", error.message, 500);
	}
});

/**
 * Get recent system alerts
 * Admin only endpoint
 */
const getSystemAlerts = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const { limit = 50, severity, type } = req.query;
		const db = require("../../infrastructure/database/knex");

		let query = db("dtm_ads.system_alerts").orderBy("created_at", "desc").limit(parseInt(limit));

		if (severity) {
			query = query.where("severity", severity);
		}

		if (type) {
			query = query.where("type", type);
		}

		const alerts = await query.select("*");

		// Get alert summary
		const summary = await db("dtm_ads.system_alerts")
			.where("created_at", ">", new Date(Date.now() - 24 * 60 * 60 * 1000))
			.select("severity")
			.count("* as count")
			.groupBy("severity");

		return sendSuccess(
			res,
			{
				alerts,
				summary: summary.reduce((acc, item) => {
					acc[item.severity] = parseInt(item.count);
					return acc;
				}, {}),
				total: alerts.length,
			},
			"System alerts retrieved"
		);
	} catch (error) {
		return sendError(res, "Failed to get system alerts", error.message, 500);
	}
});

/**
 * Get monitoring dashboard data
 * Admin only endpoint
 */
const getMonitoringDashboard = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const now = new Date();
		const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
		const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

		// Get current system health
		const systemHealth = await paymentMonitoring.checkSystemHealth();

		// Get payment stats for different timeframes
		const hourlyStats = await paymentMonitoring.getPaymentStats(oneHourAgo, now);
		const dailyStats = await paymentMonitoring.getPaymentStats(oneDayAgo, now);

		// Get recent alerts
		const db = require("../../infrastructure/database/knex");
		const recentAlerts = await db("dtm_ads.system_alerts")
			.where("created_at", ">", oneDayAgo)
			.orderBy("created_at", "desc")
			.limit(10)
			.select("*");

		// Get health metrics trend
		const healthTrend = await db("dtm_ads.health_metrics")
			.where("created_at", ">", oneDayAgo)
			.orderBy("created_at", "asc")
			.select("timestamp", "healthy", "error_rate", "database_response_time");

		// Calculate key metrics
		const keyMetrics = {
			systemUptime: systemHealth.healthy ? "UP" : "DOWN",
			paymentSuccessRate: (dailyStats.successRate * 100).toFixed(1),
			errorRate: (systemHealth.errorRate * 100).toFixed(2),
			activeAlerts: recentAlerts.filter((a) => a.severity === "critical").length,
			totalTransactions: dailyStats.totalAttempts,
			failedTransactions: dailyStats.failures,
		};

		return sendSuccess(
			res,
			{
				keyMetrics,
				systemHealth,
				paymentStats: {
					hourly: hourlyStats,
					daily: dailyStats,
				},
				recentAlerts,
				healthTrend,
				lastUpdated: now.toISOString(),
			},
			"Monitoring dashboard data retrieved"
		);
	} catch (error) {
		return sendError(res, "Failed to get monitoring dashboard", error.message, 500);
	}
});

/**
 * Trigger manual monitoring check
 * Admin only endpoint
 */
const triggerMonitoringCheck = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const result = await paymentMonitoring.monitorPaymentFailures();
		return sendSuccess(res, result, "Monitoring check completed");
	} catch (error) {
		return sendError(res, "Failed to run monitoring check", error.message, 500);
	}
});

/**
 * Get monitoring configuration
 * Admin only endpoint
 */
const getMonitoringConfig = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const config = {
			alertThresholds: paymentMonitoring.alertThresholds,
			alertCooldown: paymentMonitoring.alertCooldown,
			webhooksConfigured: {
				alerts: !!process.env.ALERT_WEBHOOK_URL,
				dailySummary: !!process.env.DAILY_SUMMARY_WEBHOOK_URL,
			},
			emailAlertsEnabled: process.env.ALERT_EMAIL_ENABLED === "true",
			monitoringEnabled: true,
		};

		return sendSuccess(res, config, "Monitoring configuration retrieved");
	} catch (error) {
		return sendError(res, "Failed to get monitoring configuration", error.message, 500);
	}
});

/**
 * Update monitoring configuration
 * Admin only endpoint
 */
const updateMonitoringConfig = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const { alertThresholds, alertCooldown } = req.body;

		if (alertThresholds) {
			// Validate thresholds
			if (alertThresholds.paymentFailureRate < 0 || alertThresholds.paymentFailureRate > 1) {
				return sendError(res, "Invalid payment failure rate threshold", null, 400);
			}

			Object.assign(paymentMonitoring.alertThresholds, alertThresholds);
		}

		if (alertCooldown && alertCooldown > 0) {
			paymentMonitoring.alertCooldown = alertCooldown;
		}

		return sendSuccess(
			res,
			{
				alertThresholds: paymentMonitoring.alertThresholds,
				alertCooldown: paymentMonitoring.alertCooldown,
			},
			"Monitoring configuration updated"
		);
	} catch (error) {
		return sendError(res, "Failed to update monitoring configuration", error.message, 500);
	}
});

/**
 * Get monitoring alerts
 */
const getMonitoringAlerts = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { severity, resolved = false, limit = 50, offset = 0, startDate, endDate } = req.query;

	let query = db("dtm_ads.monitoring_alerts")
		.orderBy("created_at", "desc")
		.limit(parseInt(limit))
		.offset(parseInt(offset));

	if (severity) {
		query = query.where({ severity });
	}

	if (resolved !== undefined) {
		query = query.where({ resolved: resolved === "true" });
	}

	if (startDate && endDate) {
		query = query.whereBetween("created_at", [new Date(startDate), new Date(endDate)]);
	}

	const alerts = await query;

	return sendSuccess(res, { alerts }, "Monitoring alerts retrieved successfully");
});

/**
 * Resolve monitoring alert
 */
const resolveAlert = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { alertId } = req.params;
	const { resolvedBy } = req.body;

	const userId = admin.user_id || resolvedBy || "system";

	const [updatedAlert] = await db("dtm_ads.monitoring_alerts")
		.where({ id: alertId })
		.update({
			resolved: true,
			resolved_at: new Date(),
			resolved_by: userId,
		})
		.returning("*");

	if (!updatedAlert) {
		return sendError(res, "Alert not found", null, 404);
	}

	logger.info("Monitoring alert resolved", {
		alertId,
		resolvedBy: userId,
		alertType: updatedAlert.alert_type,
	});

	return sendSuccess(res, { alert: updatedAlert }, "Alert resolved successfully");
});

/**
 * Test notification channels
 */
const testNotifications = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const notificationService = new AlertNotificationService();
	const results = await notificationService.testNotificationChannels();

	return sendSuccess(res, results, "Notification channels tested");
});

module.exports = {
	getSystemHealth,
	getPaymentStats,
	getSystemAlerts,
	getMonitoringDashboard,
	triggerMonitoringCheck,
	getMonitoringConfig,
	updateMonitoringConfig,
	getMonitoringAlerts,
	resolveAlert,
	testNotifications,
};
