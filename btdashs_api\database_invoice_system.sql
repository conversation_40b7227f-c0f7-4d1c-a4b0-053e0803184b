-- ============================================================================
-- BTDash API - Invoice System Database Schema
-- ============================================================================
-- This script creates the proper invoice system tables to replace the current
-- system that uses payment intent IDs as invoice IDs
-- ============================================================================

-- Create dtm_ads schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dtm_ads;

-- ============================================================================
-- INVOICE SYSTEM TABLES
-- ============================================================================

-- Invoices table for proper invoice management
CREATE TABLE IF NOT EXISTS dtm_ads.invoices (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL, -- Human-readable invoice number (INV-2024-001)
    stripe_invoice_id VARCHAR(100) UNIQUE, -- Stripe invoice ID (in_...)
    manager_id INTEGER NOT NULL, -- User ID (campaign manager)
    advertiser_id INTEGER, -- Company ID (advertiser company)
    customer_id VARCHAR(100), -- Stripe customer ID
    campaign_id INTEGER,
    
    -- Invoice details
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Invoice status
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    -- draft, open, paid, void, uncollectible
    
    -- Payment information
    payment_intent_id VARCHAR(100), -- Associated payment intent
    payment_method VARCHAR(50),
    paid_at TIMESTAMP,
    
    -- Invoice metadata
    description TEXT,
    notes TEXT,
    due_date TIMESTAMP,
    
    -- PDF generation
    pdf_url TEXT, -- URL to generated PDF
    pdf_generated_at TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_invoices_manager_id
        FOREIGN KEY (manager_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT fk_invoices_advertiser_id
        FOREIGN KEY (advertiser_id) REFERENCES dtm_base.companies(id) ON DELETE SET NULL,
    CONSTRAINT fk_invoices_campaign_id
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_invoices_status 
        CHECK (status IN ('draft', 'open', 'paid', 'void', 'uncollectible')),
    CONSTRAINT chk_invoices_amounts 
        CHECK (subtotal >= 0 AND tax_amount >= 0 AND total_amount >= 0)
);

-- Invoice line items for detailed billing
CREATE TABLE IF NOT EXISTS dtm_ads.invoice_line_items (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL,
    
    -- Line item details
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL, -- quantity * unit_price
    
    -- Metadata
    campaign_id INTEGER,
    ad_slot_id INTEGER,
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_invoice_line_items_invoice_id 
        FOREIGN KEY (invoice_id) REFERENCES dtm_ads.invoices(id) ON DELETE CASCADE,
    CONSTRAINT fk_invoice_line_items_campaign_id 
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_invoice_line_items_amounts 
        CHECK (quantity > 0 AND unit_price >= 0 AND amount >= 0)
);

-- ============================================================================
-- UPDATE EXISTING BILLING_TRANSACTIONS TABLE
-- ============================================================================

-- Add invoice_id reference to billing_transactions
DO $$ 
BEGIN
    -- Add new invoice_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'dtm_ads' 
                   AND table_name = 'billing_transactions' 
                   AND column_name = 'invoice_reference_id') THEN
        ALTER TABLE dtm_ads.billing_transactions 
        ADD COLUMN invoice_reference_id INTEGER;
        
        -- Add foreign key constraint
        ALTER TABLE dtm_ads.billing_transactions 
        ADD CONSTRAINT fk_billing_transactions_invoice_id 
        FOREIGN KEY (invoice_reference_id) REFERENCES dtm_ads.invoices(id) ON DELETE SET NULL;
    END IF;
    
    -- Rename the old invoice_id column to payment_intent_id for clarity
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'dtm_ads' 
               AND table_name = 'billing_transactions' 
               AND column_name = 'invoice_id') THEN
        ALTER TABLE dtm_ads.billing_transactions 
        RENAME COLUMN invoice_id TO payment_intent_reference;
    END IF;
END $$;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Invoices indexes
CREATE INDEX IF NOT EXISTS idx_invoices_manager_id
    ON dtm_ads.invoices(manager_id);
CREATE INDEX IF NOT EXISTS idx_invoices_advertiser_id
    ON dtm_ads.invoices(advertiser_id);
CREATE INDEX IF NOT EXISTS idx_invoices_campaign_id
    ON dtm_ads.invoices(campaign_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status 
    ON dtm_ads.invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at 
    ON dtm_ads.invoices(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_invoices_stripe_invoice_id 
    ON dtm_ads.invoices(stripe_invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoices_payment_intent_id 
    ON dtm_ads.invoices(payment_intent_id);

-- Invoice line items indexes
CREATE INDEX IF NOT EXISTS idx_invoice_line_items_invoice_id 
    ON dtm_ads.invoice_line_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_line_items_campaign_id 
    ON dtm_ads.invoice_line_items(campaign_id);

-- ============================================================================
-- INVOICE NUMBER SEQUENCE
-- ============================================================================

-- Create sequence for invoice numbers
CREATE SEQUENCE IF NOT EXISTS dtm_ads.invoice_number_seq 
    START WITH 1 
    INCREMENT BY 1 
    NO MAXVALUE 
    NO CYCLE;

-- Function to generate invoice numbers
CREATE OR REPLACE FUNCTION dtm_ads.generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
BEGIN
    year_part := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    sequence_part := LPAD(nextval('dtm_ads.invoice_number_seq')::TEXT, 4, '0');
    RETURN 'INV-' || year_part || '-' || sequence_part;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- MIGRATION NOTES
-- ============================================================================

-- This migration:
-- 1. Creates proper invoice tables with Stripe integration
-- 2. Maintains backward compatibility with existing billing_transactions
-- 3. Adds proper invoice numbering system
-- 4. Supports detailed line items for complex billing
-- 5. Includes PDF generation tracking
-- 6. Provides proper indexes for performance

-- To migrate existing data:
-- 1. Run this migration
-- 2. Create invoices for existing completed billing transactions
-- 3. Update billing_transactions.invoice_reference_id to link to new invoices
-- 4. Generate PDFs for historical invoices if needed
