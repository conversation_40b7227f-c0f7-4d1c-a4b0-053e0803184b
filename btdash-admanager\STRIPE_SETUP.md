# Quick Stripe Setup Guide

## 🚀 Quick Start

1. **Copy environment template:**
   ```bash
   cp .env.example .env.local
   ```

2. **Get your Stripe keys:**
   - Visit [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
   - Copy your publishable key (starts with `pk_test_`)
   - Copy your secret key (starts with `sk_test_`)

3. **Set up webhook:**
   - Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
   - Add endpoint: `https://your-domain.com/api/stripe/webhook`
   - Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
   - Copy webhook secret (starts with `whsec_`)

4. **Update .env.local:**
   ```env
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY='pk_test_...'
   STRIPE_SECRET_KEY='sk_test_...'
   STRIPE_WEBHOOK_SECRET='whsec_...'
   ```

5. **Validate configuration:**
   ```bash
   npm run validate:stripe
   ```

6. **Start development:**
   ```bash
   npm run dev
   ```

## ✅ Configuration Validation

The system automatically validates your Stripe configuration and will show:

- ✅ **Success**: Configuration is valid
- ❌ **Errors**: Must be fixed before payments work
- ⚠️ **Warnings**: Non-critical issues to be aware of

### Manual Validation

Run the validation script anytime:

```bash
npm run validate:stripe
```

## 🧪 Testing

Use these test card numbers:

- **Success**: `****************`
- **Declined**: `****************`
- **Insufficient funds**: `****************`

## 📚 Full Documentation

For complete setup instructions, see:
- [docs/stripe-setup.md](docs/stripe-setup.md) - Detailed setup guide
- [.env.example](.env.example) - All environment variables

## 🔧 Troubleshooting

**Common Issues:**

1. **"Missing Stripe publishable key"**
   - Check `.env.local` has `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
   - Restart dev server after adding env vars

2. **Webhook not working**
   - Use ngrok for local development: `ngrok http 3000`
   - Update webhook URL in Stripe dashboard

3. **Key mismatch errors**
   - Ensure both keys are test keys (`pk_test_` and `sk_test_`) or both live keys

Run `npm run validate:stripe` for detailed diagnostics.

## 🏗️ Architecture

The Stripe integration includes:

- **Payment Processing**: Campaign-based billing
- **Admin Workflow**: Charge on approval, not creation
- **Error Handling**: Comprehensive failure recovery
- **Webhooks**: Automatic payment status updates
- **Validation**: Real-time configuration checking

## 🔐 Security

- Never commit `.env.local` to git
- Use test keys for development
- Use live keys only in production
- Regularly rotate API keys
