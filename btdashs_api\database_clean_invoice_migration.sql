-- ============================================================================
-- BTDash API - Clean Invoice System Migration (No Backward Compatibility)
-- ============================================================================
-- This migration removes all backward compatibility and implements clean schema
-- WARNING: This will delete existing test data in ad-related tables
-- ============================================================================

-- Create dtm_ads schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dtm_ads;

-- ============================================================================
-- CLEAN UP EXISTING DATA (Development Only)
-- ============================================================================

-- Drop existing invoice-related tables if they exist
DROP TABLE IF EXISTS dtm_ads.invoice_line_items CASCADE;
DROP TABLE IF EXISTS dtm_ads.invoices CASCADE;

-- Clean up existing billing transactions (development data only)
DELETE FROM dtm_ads.billing_transactions;

-- ============================================================================
-- CLEAN BILLING TRANSACTIONS TABLE
-- ============================================================================

-- Drop old columns and add new clean structure
ALTER TABLE dtm_ads.billing_transactions 
DROP COLUMN IF EXISTS advertiser_id,
DROP COLUMN IF EXISTS invoice_id,
DROP COLUMN IF EXISTS payment_intent_reference,
DROP COLUMN IF EXISTS invoice_reference_id;

-- Add clean columns
ALTER TABLE dtm_ads.billing_transactions 
ADD COLUMN IF NOT EXISTS user_id INTEGER NOT NULL,
ADD COLUMN IF NOT EXISTS payment_intent_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS invoice_id INTEGER;

-- Update foreign key constraints
ALTER TABLE dtm_ads.billing_transactions 
DROP CONSTRAINT IF EXISTS fk_billing_transactions_advertiser_id,
DROP CONSTRAINT IF EXISTS fk_billing_transactions_invoice_id,
DROP CONSTRAINT IF EXISTS fk_billing_transactions_invoice_reference_id;

ALTER TABLE dtm_ads.billing_transactions 
ADD CONSTRAINT fk_billing_transactions_user_id 
    FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_billing_transactions_campaign_id 
    FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL;

-- ============================================================================
-- CLEAN INVOICE SYSTEM TABLES
-- ============================================================================

-- Create clean invoices table
CREATE TABLE dtm_ads.invoices (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL, -- Human-readable invoice number (INV-2024-001)
    stripe_invoice_id VARCHAR(100) UNIQUE, -- Stripe invoice ID (in_...)
    manager_id INTEGER NOT NULL, -- User ID (campaign manager who pays)
    advertiser_id INTEGER, -- Company ID (advertiser company)
    customer_id VARCHAR(100), -- Stripe customer ID
    campaign_id INTEGER,
    
    -- Invoice details
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Invoice status
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    -- draft, open, paid, void, uncollectible
    
    -- Payment information
    payment_intent_id VARCHAR(100), -- Associated payment intent
    payment_method VARCHAR(50),
    paid_at TIMESTAMP,
    
    -- Invoice metadata
    description TEXT,
    notes TEXT,
    due_date TIMESTAMP,
    
    -- PDF generation
    pdf_url TEXT, -- URL to generated PDF
    pdf_generated_at TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Clean constraints
    CONSTRAINT fk_invoices_manager_id 
        FOREIGN KEY (manager_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT fk_invoices_advertiser_id 
        FOREIGN KEY (advertiser_id) REFERENCES dtm_base.companies(id) ON DELETE SET NULL,
    CONSTRAINT fk_invoices_campaign_id 
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_invoices_status 
        CHECK (status IN ('draft', 'open', 'paid', 'void', 'uncollectible')),
    CONSTRAINT chk_invoices_amounts 
        CHECK (subtotal >= 0 AND tax_amount >= 0 AND total_amount >= 0)
);

-- Create invoice line items table
CREATE TABLE dtm_ads.invoice_line_items (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL,
    
    -- Line item details
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL, -- quantity * unit_price
    
    -- Metadata
    campaign_id INTEGER,
    ad_slot_id INTEGER,
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_invoice_line_items_invoice_id 
        FOREIGN KEY (invoice_id) REFERENCES dtm_ads.invoices(id) ON DELETE CASCADE,
    CONSTRAINT fk_invoice_line_items_campaign_id 
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_invoice_line_items_amounts 
        CHECK (quantity > 0 AND unit_price >= 0 AND amount >= 0)
);

-- Add invoice reference to billing transactions
ALTER TABLE dtm_ads.billing_transactions 
ADD CONSTRAINT fk_billing_transactions_invoice_id 
    FOREIGN KEY (invoice_id) REFERENCES dtm_ads.invoices(id) ON DELETE SET NULL;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Billing transactions indexes
CREATE INDEX idx_billing_transactions_user_id ON dtm_ads.billing_transactions(user_id);
CREATE INDEX idx_billing_transactions_campaign_id ON dtm_ads.billing_transactions(campaign_id);
CREATE INDEX idx_billing_transactions_payment_intent_id ON dtm_ads.billing_transactions(payment_intent_id);
CREATE INDEX idx_billing_transactions_status ON dtm_ads.billing_transactions(status);
CREATE INDEX idx_billing_transactions_created_at ON dtm_ads.billing_transactions(created_at DESC);

-- Invoices indexes
CREATE INDEX idx_invoices_manager_id ON dtm_ads.invoices(manager_id);
CREATE INDEX idx_invoices_advertiser_id ON dtm_ads.invoices(advertiser_id);
CREATE INDEX idx_invoices_campaign_id ON dtm_ads.invoices(campaign_id);
CREATE INDEX idx_invoices_status ON dtm_ads.invoices(status);
CREATE INDEX idx_invoices_created_at ON dtm_ads.invoices(created_at DESC);
CREATE INDEX idx_invoices_stripe_invoice_id ON dtm_ads.invoices(stripe_invoice_id);
CREATE INDEX idx_invoices_payment_intent_id ON dtm_ads.invoices(payment_intent_id);

-- Invoice line items indexes
CREATE INDEX idx_invoice_line_items_invoice_id ON dtm_ads.invoice_line_items(invoice_id);
CREATE INDEX idx_invoice_line_items_campaign_id ON dtm_ads.invoice_line_items(campaign_id);

-- ============================================================================
-- INVOICE NUMBER SEQUENCE
-- ============================================================================

-- Create sequence for invoice numbers
DROP SEQUENCE IF EXISTS dtm_ads.invoice_number_seq CASCADE;
CREATE SEQUENCE dtm_ads.invoice_number_seq 
    START WITH 1 
    INCREMENT BY 1 
    NO MAXVALUE 
    NO CYCLE;

-- Function to generate invoice numbers
CREATE OR REPLACE FUNCTION dtm_ads.generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
BEGIN
    year_part := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    sequence_part := LPAD(nextval('dtm_ads.invoice_number_seq')::TEXT, 4, '0');
    RETURN 'INV-' || year_part || '-' || sequence_part;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- CLEAN UP VIEWS AND FUNCTIONS
-- ============================================================================

-- Drop any views that might reference old columns
DROP VIEW IF EXISTS dtm_ads.v_billing_summary CASCADE;
DROP VIEW IF EXISTS dtm_ads.v_invoice_summary CASCADE;

-- Create clean billing summary view
CREATE VIEW dtm_ads.v_billing_summary AS
SELECT 
    bt.user_id,
    bt.campaign_id,
    c.name as campaign_name,
    c.advertiser_id,
    comp.name as company_name,
    SUM(bt.amount) as total_amount,
    COUNT(bt.id) as transaction_count,
    MAX(bt.created_at) as last_transaction_date
FROM dtm_ads.billing_transactions bt
LEFT JOIN dtm_ads.ad_campaigns c ON bt.campaign_id = c.id
LEFT JOIN dtm_base.companies comp ON c.advertiser_id = comp.id
WHERE bt.status = 'completed'
GROUP BY bt.user_id, bt.campaign_id, c.name, c.advertiser_id, comp.name;

-- Create invoice summary view
CREATE VIEW dtm_ads.v_invoice_summary AS
SELECT 
    i.id,
    i.invoice_number,
    i.manager_id,
    i.advertiser_id,
    comp.name as company_name,
    i.campaign_id,
    c.name as campaign_name,
    i.total_amount,
    i.status,
    i.created_at,
    i.paid_at
FROM dtm_ads.invoices i
LEFT JOIN dtm_ads.ad_campaigns c ON i.campaign_id = c.id
LEFT JOIN dtm_base.companies comp ON i.advertiser_id = comp.id;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log migration completion
DO $$ 
BEGIN
    RAISE NOTICE 'Clean invoice system migration completed successfully';
    RAISE NOTICE 'All backward compatibility removed';
    RAISE NOTICE 'Test data cleared from ad-related tables';
END $$;
