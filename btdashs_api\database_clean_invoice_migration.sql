CREATE SCHEMA IF NOT EXISTS dtm_ads;

DROP VIEW IF EXISTS dtm_ads.v_user_balance_summary CASCADE;
DROP VIEW IF EXISTS dtm_ads.v_billing_summary CASCADE;
DROP VIEW IF EXISTS dtm_ads.v_invoice_summary CASCADE;

DROP TABLE IF EXISTS dtm_ads.invoice_line_items CASCADE;
DROP TABLE IF EXISTS dtm_ads.invoices CASCADE;
DELETE FROM dtm_ads.billing_transactions;

ALTER TABLE dtm_ads.billing_transactions
DROP COLUMN IF EXISTS advertiser_id CASCADE,
DROP COLUMN IF EXISTS invoice_id CASCADE,
DROP COLUMN IF EXISTS payment_intent_reference CASCADE,
DROP COLUMN IF EXISTS invoice_reference_id CASCADE;

ALTER TABLE dtm_ads.billing_transactions
ADD COLUMN IF NOT EXISTS user_id INTEGER NOT NULL,
ADD COLUMN IF NOT EXISTS payment_intent_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS invoice_id INTEGER;

ALTER TABLE dtm_ads.billing_transactions
DROP CONSTRAINT IF EXISTS fk_billing_transactions_advertiser_id,
DROP CONSTRAINT IF EXISTS fk_billing_transactions_invoice_id,
DROP CONSTRAINT IF EXISTS fk_billing_transactions_invoice_reference_id;

ALTER TABLE dtm_ads.billing_transactions
ADD CONSTRAINT fk_billing_transactions_user_id
    FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_billing_transactions_campaign_id
    FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL;

CREATE TABLE dtm_ads.invoices (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    stripe_invoice_id VARCHAR(100) UNIQUE,
    manager_id INTEGER NOT NULL,
    advertiser_id INTEGER,
    customer_id VARCHAR(100),
    campaign_id INTEGER,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    payment_intent_id VARCHAR(100),
    payment_method VARCHAR(50),
    paid_at TIMESTAMP,
    description TEXT,
    notes TEXT,
    due_date TIMESTAMP,
    pdf_url TEXT,
    pdf_generated_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_invoices_manager_id
        FOREIGN KEY (manager_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT fk_invoices_advertiser_id
        FOREIGN KEY (advertiser_id) REFERENCES dtm_base.companies(id) ON DELETE SET NULL,
    CONSTRAINT fk_invoices_campaign_id
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_invoices_status
        CHECK (status IN ('draft', 'open', 'paid', 'void', 'uncollectible')),
    CONSTRAINT chk_invoices_amounts
        CHECK (subtotal >= 0 AND tax_amount >= 0 AND total_amount >= 0)
);

CREATE TABLE dtm_ads.invoice_line_items (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    campaign_id INTEGER,
    ad_slot_id INTEGER,
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_invoice_line_items_invoice_id
        FOREIGN KEY (invoice_id) REFERENCES dtm_ads.invoices(id) ON DELETE CASCADE,
    CONSTRAINT fk_invoice_line_items_campaign_id
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_invoice_line_items_amounts
        CHECK (quantity > 0 AND unit_price >= 0 AND amount >= 0)
);

ALTER TABLE dtm_ads.billing_transactions
ADD CONSTRAINT fk_billing_transactions_invoice_id
    FOREIGN KEY (invoice_id) REFERENCES dtm_ads.invoices(id) ON DELETE SET NULL;

CREATE TABLE dtm_ads.webhook_logs (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(100) UNIQUE NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'processing',
    attempts INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,
    last_error_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_webhook_status
        CHECK (status IN ('processing', 'success', 'failed', 'retrying'))
);

CREATE TABLE dtm_ads.payment_failures (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(100),
    event_type VARCHAR(50),
    payment_intent_id VARCHAR(100),
    campaign_id INTEGER,
    user_id INTEGER,
    failure_code VARCHAR(50),
    failure_reason TEXT,
    amount DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50),
    metadata JSONB,
    event_data JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_payment_failures_campaign_id
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT fk_payment_failures_user_id
        FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE SET NULL
);

CREATE INDEX idx_billing_transactions_user_id ON dtm_ads.billing_transactions(user_id);
CREATE INDEX idx_billing_transactions_campaign_id ON dtm_ads.billing_transactions(campaign_id);
CREATE INDEX idx_billing_transactions_payment_intent_id ON dtm_ads.billing_transactions(payment_intent_id);
CREATE INDEX idx_billing_transactions_status ON dtm_ads.billing_transactions(status);
CREATE INDEX idx_billing_transactions_created_at ON dtm_ads.billing_transactions(created_at DESC);

CREATE INDEX idx_invoices_manager_id ON dtm_ads.invoices(manager_id);
CREATE INDEX idx_invoices_advertiser_id ON dtm_ads.invoices(advertiser_id);
CREATE INDEX idx_invoices_campaign_id ON dtm_ads.invoices(campaign_id);
CREATE INDEX idx_invoices_status ON dtm_ads.invoices(status);
CREATE INDEX idx_invoices_created_at ON dtm_ads.invoices(created_at DESC);
CREATE INDEX idx_invoices_stripe_invoice_id ON dtm_ads.invoices(stripe_invoice_id);
CREATE INDEX idx_invoices_payment_intent_id ON dtm_ads.invoices(payment_intent_id);

CREATE INDEX idx_invoice_line_items_invoice_id ON dtm_ads.invoice_line_items(invoice_id);
CREATE INDEX idx_invoice_line_items_campaign_id ON dtm_ads.invoice_line_items(campaign_id);

CREATE INDEX idx_webhook_logs_event_id ON dtm_ads.webhook_logs(event_id);
CREATE INDEX idx_webhook_logs_event_type ON dtm_ads.webhook_logs(event_type);
CREATE INDEX idx_webhook_logs_status ON dtm_ads.webhook_logs(status);
CREATE INDEX idx_webhook_logs_created_at ON dtm_ads.webhook_logs(created_at DESC);

CREATE INDEX idx_payment_failures_payment_intent_id ON dtm_ads.payment_failures(payment_intent_id);
CREATE INDEX idx_payment_failures_campaign_id ON dtm_ads.payment_failures(campaign_id);
CREATE INDEX idx_payment_failures_user_id ON dtm_ads.payment_failures(user_id);
CREATE INDEX idx_payment_failures_failure_code ON dtm_ads.payment_failures(failure_code);
CREATE INDEX idx_payment_failures_created_at ON dtm_ads.payment_failures(created_at DESC);

CREATE TABLE dtm_ads.monitoring_alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    alert_data JSONB,
    context_data JSONB,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP,
    resolved_by VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_monitoring_alerts_severity
        CHECK (severity IN ('info', 'warning', 'critical'))
);

CREATE INDEX idx_monitoring_alerts_type ON dtm_ads.monitoring_alerts(alert_type);
CREATE INDEX idx_monitoring_alerts_severity ON dtm_ads.monitoring_alerts(severity);
CREATE INDEX idx_monitoring_alerts_created_at ON dtm_ads.monitoring_alerts(created_at DESC);
CREATE INDEX idx_monitoring_alerts_resolved ON dtm_ads.monitoring_alerts(resolved);

CREATE TABLE dtm_ads.reconciliation_reports (
    id SERIAL PRIMARY KEY,
    reconciliation_id VARCHAR(100) UNIQUE NOT NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    total_stripe_payments INTEGER NOT NULL DEFAULT 0,
    total_db_payments INTEGER NOT NULL DEFAULT 0,
    total_matches INTEGER NOT NULL DEFAULT 0,
    total_discrepancies INTEGER NOT NULL DEFAULT 0,
    match_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    report_data JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_reconciliation_reports_id ON dtm_ads.reconciliation_reports(reconciliation_id);
CREATE INDEX idx_reconciliation_reports_period ON dtm_ads.reconciliation_reports(period_start, period_end);
CREATE INDEX idx_reconciliation_reports_created_at ON dtm_ads.reconciliation_reports(created_at DESC);

DROP SEQUENCE IF EXISTS dtm_ads.invoice_number_seq CASCADE;
CREATE SEQUENCE dtm_ads.invoice_number_seq
    START WITH 1
    INCREMENT BY 1
    NO MAXVALUE
    NO CYCLE;

CREATE OR REPLACE FUNCTION dtm_ads.generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
BEGIN
    year_part := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    sequence_part := LPAD(nextval('dtm_ads.invoice_number_seq')::TEXT, 4, '0');
    RETURN 'INV-' || year_part || '-' || sequence_part;
END;
$$ LANGUAGE plpgsql;



CREATE VIEW dtm_ads.v_billing_summary AS
SELECT
    bt.user_id,
    bt.campaign_id,
    c.name as campaign_name,
    c.advertiser_id,
    comp.name as company_name,
    SUM(bt.amount) as total_amount,
    COUNT(bt.id) as transaction_count,
    MAX(bt.created_at) as last_transaction_date
FROM dtm_ads.billing_transactions bt
LEFT JOIN dtm_ads.ad_campaigns c ON bt.campaign_id = c.id
LEFT JOIN dtm_base.companies comp ON c.advertiser_id = comp.id
WHERE bt.status = 'completed'
GROUP BY bt.user_id, bt.campaign_id, c.name, c.advertiser_id, comp.name;

CREATE VIEW dtm_ads.v_invoice_summary AS
SELECT
    i.id,
    i.invoice_number,
    i.manager_id,
    i.advertiser_id,
    comp.name as company_name,
    i.campaign_id,
    c.name as campaign_name,
    i.total_amount,
    i.status,
    i.created_at,
    i.paid_at
FROM dtm_ads.invoices i
LEFT JOIN dtm_ads.ad_campaigns c ON i.campaign_id = c.id
LEFT JOIN dtm_base.companies comp ON i.advertiser_id = comp.id;
