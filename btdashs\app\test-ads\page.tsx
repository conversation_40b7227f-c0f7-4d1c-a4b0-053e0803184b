"use client";

import { SmartAdBanner } from "@/components/ads-placements/smart-ad-banner";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";

export default function TestAdsPage() {
	const [enablePaidAds, setEnablePaidAds] = useState(true);
	const [refreshKey, setRefreshKey] = useState(0);

	const handleRefresh = () => {
		setRefreshKey((prev) => prev + 1);
	};

	const handleTogglePaidAds = () => {
		setEnablePaidAds((prev) => !prev);
		setRefreshKey((prev) => prev + 1);
	};

	return (
		<div className="container mx-auto py-8 px-4">
			<div className="max-w-6xl mx-auto space-y-8">
				<div className="text-center">
					<h1 className="text-3xl font-bold mb-4">Ad Serving Test Page</h1>
					<p className="text-muted-foreground mb-6">
						Test the smart ad banner component with different variants and configurations
					</p>

					<div className="flex gap-4 justify-center">
						<Button onClick={handleRefresh} variant="outline">
							Refresh All Ads
						</Button>
						<Button onClick={handleTogglePaidAds} variant={enablePaidAds ? "default" : "secondary"}>
							{enablePaidAds ? "Disable" : "Enable"} Paid Ads
						</Button>
					</div>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
					{/* Horizontal Ad Test */}
					<Card>
						<CardHeader>
							<CardTitle>Horizontal Ad (728x90)</CardTitle>
							<p className="text-sm text-muted-foreground">
								Leaderboard format - typically used at top/bottom of pages
							</p>
						</CardHeader>
						<CardContent>
							<div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
								<SmartAdBanner
									key={`horizontal-${refreshKey}`}
									slotId={1}
									googleAdSlot="8814794983"
									enablePaidAds={enablePaidAds}
									className="mx-auto"
								/>
							</div>
						</CardContent>
					</Card>

					{/* Square Ad Test */}
					<Card>
						<CardHeader>
							<CardTitle>Square Ad (300x250)</CardTitle>
							<p className="text-sm text-muted-foreground">
								Medium rectangle format - typically used in sidebars
							</p>
						</CardHeader>
						<CardContent>
							<div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
								<SmartAdBanner
									key={`square-${refreshKey}`}
									slotId={3}
									googleAdSlot="7844510005"
									enablePaidAds={enablePaidAds}
									className="mx-auto"
								/>
							</div>
						</CardContent>
					</Card>

					{/* Vertical Ad Test */}
					<Card>
						<CardHeader>
							<CardTitle>Vertical Ad (300x600)</CardTitle>
							<p className="text-sm text-muted-foreground">
								Skyscraper format - typically used in sidebars
							</p>
						</CardHeader>
						<CardContent>
							<div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
								<SmartAdBanner
									key={`vertical-${refreshKey}`}
									slotId={4}
									googleAdSlot="7964747747"
									enablePaidAds={enablePaidAds}
									className="mx-auto"
								/>
							</div>
						</CardContent>
					</Card>

					{/* Billboard Ad Test */}
					<Card>
						<CardHeader>
							<CardTitle>Billboard Ad (970x250)</CardTitle>
							<p className="text-sm text-muted-foreground">
								Large banner format - typically used on homepage
							</p>
						</CardHeader>
						<CardContent>
							<div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
								<SmartAdBanner
									key={`billboard-${refreshKey}`}
									slotId={2}
									googleAdSlot="7230250579"
									enablePaidAds={enablePaidAds}
									className="mx-auto"
								/>
							</div>
						</CardContent>
					</Card>
				</div>

				<Card>
					<CardHeader>
						<CardTitle>Test Instructions</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<h4 className="font-semibold mb-2">Expected Behavior:</h4>
							<ul className="list-disc list-inside space-y-1 text-sm">
								<li>
									<strong>With Paid Ads Enabled:</strong> Should attempt to load paid ads from
									backend, fall back to Google AdSense if none available
								</li>
								<li>
									<strong>With Paid Ads Disabled:</strong> Should only show Google AdSense ads
								</li>
								<li>
									<strong>Loading State:</strong> Should show "Loading ads..." message while fetching
								</li>
								<li>
									<strong>Error Handling:</strong> Should gracefully fall back to Google ads on API
									errors
								</li>
								<li>
									<strong>Tracking:</strong> Should track impressions when ads become visible and
									clicks when clicked
								</li>
							</ul>
						</div>

						<div>
							<h4 className="font-semibold mb-2">Testing Scenarios:</h4>
							<ul className="list-disc list-inside space-y-1 text-sm">
								<li>Toggle paid ads on/off to test fallback behavior</li>
								<li>Refresh to test loading states and retry logic</li>
								<li>Check browser console for any errors or tracking events</li>
								<li>Test on different screen sizes to verify responsive behavior</li>
								<li>Click on ads to verify tracking and redirect functionality</li>
							</ul>
						</div>

						<div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
							<h4 className="font-semibold mb-2 text-yellow-800 dark:text-yellow-200">Note:</h4>
							<p className="text-sm text-yellow-700 dark:text-yellow-300">
								This test page is for development purposes only. Remove or restrict access before
								production deployment.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
