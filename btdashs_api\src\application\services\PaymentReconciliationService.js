const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class PaymentReconciliationService {
	constructor() {
		this.reconciliationTypes = {
			MATCH: 'match',
			MISSING_IN_DB: 'missing_in_db',
			MISSING_IN_STRIPE: 'missing_in_stripe',
			AMOUNT_MISMATCH: 'amount_mismatch',
			STATUS_MISMATCH: 'status_mismatch',
		};
	}

	/**
	 * Run payment reconciliation for a specific date range
	 * @param {Object} options - Reconciliation options
	 * @returns {Promise<Object>} Reconciliation report
	 */
	async runReconciliation(options = {}) {
		const {
			startDate = new Date(Date.now() - 24 * 60 * 60 * 1000), // Default: last 24 hours
			endDate = new Date(),
			includeSuccessful = true,
			includeFailed = true,
		} = options;

		logger.info("Starting payment reconciliation", {
			startDate: startDate.toISOString(),
			endDate: endDate.toISOString(),
			includeSuccessful,
			includeFailed,
		});

		try {
			const [stripePayments, dbPayments] = await Promise.all([
				this.getStripePayments(startDate, endDate, includeSuccessful, includeFailed),
				this.getDatabasePayments(startDate, endDate, includeSuccessful, includeFailed),
			]);

			const reconciliationResults = this.comparePayments(stripePayments, dbPayments);
			const summary = this.generateSummary(reconciliationResults);

			const report = {
				reconciliation_id: this.generateReconciliationId(),
				timestamp: new Date().toISOString(),
				period: {
					start_date: startDate.toISOString(),
					end_date: endDate.toISOString(),
				},
				summary,
				discrepancies: reconciliationResults.filter(result => result.type !== this.reconciliationTypes.MATCH),
				total_stripe_payments: stripePayments.length,
				total_db_payments: dbPayments.length,
				options,
			};

			// Store reconciliation report
			await this.storeReconciliationReport(report);

			logger.info("Payment reconciliation completed", {
				reconciliationId: report.reconciliation_id,
				totalDiscrepancies: report.discrepancies.length,
				matchRate: summary.match_rate,
			});

			return report;

		} catch (error) {
			logger.error("Payment reconciliation failed", {
				error: error.message,
				stack: error.stack,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			});
			throw error;
		}
	}

	/**
	 * Get payments from Stripe for the specified period
	 * @param {Date} startDate - Start date
	 * @param {Date} endDate - End date
	 * @param {boolean} includeSuccessful - Include successful payments
	 * @param {boolean} includeFailed - Include failed payments
	 * @returns {Promise<Array>} Stripe payments
	 */
	async getStripePayments(startDate, endDate, includeSuccessful, includeFailed) {
		const payments = [];
		let hasMore = true;
		let startingAfter = null;

		while (hasMore) {
			const params = {
				limit: 100,
				created: {
					gte: Math.floor(startDate.getTime() / 1000),
					lte: Math.floor(endDate.getTime() / 1000),
				},
			};

			if (startingAfter) {
				params.starting_after = startingAfter;
			}

			const paymentIntents = await stripe.paymentIntents.list(params);

			for (const payment of paymentIntents.data) {
				const shouldInclude = 
					(includeSuccessful && payment.status === 'succeeded') ||
					(includeFailed && ['failed', 'canceled'].includes(payment.status));

				if (shouldInclude) {
					payments.push({
						id: payment.id,
						amount: payment.amount,
						currency: payment.currency,
						status: payment.status,
						created: new Date(payment.created * 1000),
						metadata: payment.metadata,
						source: 'stripe',
					});
				}
			}

			hasMore = paymentIntents.has_more;
			if (hasMore && paymentIntents.data.length > 0) {
				startingAfter = paymentIntents.data[paymentIntents.data.length - 1].id;
			}
		}

		return payments;
	}

	/**
	 * Get payments from database for the specified period
	 * @param {Date} startDate - Start date
	 * @param {Date} endDate - End date
	 * @param {boolean} includeSuccessful - Include successful payments
	 * @param {boolean} includeFailed - Include failed payments
	 * @returns {Promise<Array>} Database payments
	 */
	async getDatabasePayments(startDate, endDate, includeSuccessful, includeFailed) {
		let query = db("dtm_ads.billing_transactions")
			.whereBetween("created_at", [startDate, endDate])
			.whereNotNull("payment_intent_id");

		const statusConditions = [];
		if (includeSuccessful) {
			statusConditions.push('completed');
		}
		if (includeFailed) {
			statusConditions.push('failed', 'processing_error');
		}

		if (statusConditions.length > 0) {
			query = query.whereIn("status", statusConditions);
		}

		const payments = await query.select("*");

		return payments.map(payment => ({
			id: payment.payment_intent_id,
			amount: Math.round(parseFloat(payment.amount) * 100), // Convert to cents
			currency: payment.currency || 'usd',
			status: this.mapDbStatusToStripe(payment.status),
			created: new Date(payment.created_at),
			campaign_id: payment.campaign_id,
			user_id: payment.user_id,
			source: 'database',
			db_record: payment,
		}));
	}

	/**
	 * Compare Stripe and database payments
	 * @param {Array} stripePayments - Payments from Stripe
	 * @param {Array} dbPayments - Payments from database
	 * @returns {Array} Comparison results
	 */
	comparePayments(stripePayments, dbPayments) {
		const results = [];
		const stripeMap = new Map(stripePayments.map(p => [p.id, p]));
		const dbMap = new Map(dbPayments.map(p => [p.id, p]));

		// Check each Stripe payment
		for (const stripePayment of stripePayments) {
			const dbPayment = dbMap.get(stripePayment.id);

			if (!dbPayment) {
				results.push({
					type: this.reconciliationTypes.MISSING_IN_DB,
					payment_intent_id: stripePayment.id,
					stripe_payment: stripePayment,
					db_payment: null,
					details: 'Payment exists in Stripe but not in database',
				});
			} else {
				const comparison = this.comparePaymentDetails(stripePayment, dbPayment);
				results.push(comparison);
			}
		}

		// Check for payments in database but not in Stripe
		for (const dbPayment of dbPayments) {
			if (!stripeMap.has(dbPayment.id)) {
				results.push({
					type: this.reconciliationTypes.MISSING_IN_STRIPE,
					payment_intent_id: dbPayment.id,
					stripe_payment: null,
					db_payment: dbPayment,
					details: 'Payment exists in database but not in Stripe',
				});
			}
		}

		return results;
	}

	/**
	 * Compare details between Stripe and database payment
	 * @param {Object} stripePayment - Stripe payment
	 * @param {Object} dbPayment - Database payment
	 * @returns {Object} Comparison result
	 */
	comparePaymentDetails(stripePayment, dbPayment) {
		const discrepancies = [];

		// Check amount
		if (stripePayment.amount !== dbPayment.amount) {
			discrepancies.push({
				field: 'amount',
				stripe_value: stripePayment.amount,
				db_value: dbPayment.amount,
			});
		}

		// Check currency
		if (stripePayment.currency !== dbPayment.currency) {
			discrepancies.push({
				field: 'currency',
				stripe_value: stripePayment.currency,
				db_value: dbPayment.currency,
			});
		}

		// Check status
		if (stripePayment.status !== dbPayment.status) {
			discrepancies.push({
				field: 'status',
				stripe_value: stripePayment.status,
				db_value: dbPayment.status,
			});
		}

		if (discrepancies.length === 0) {
			return {
				type: this.reconciliationTypes.MATCH,
				payment_intent_id: stripePayment.id,
				stripe_payment: stripePayment,
				db_payment: dbPayment,
				details: 'Payment records match',
			};
		} else {
			const primaryDiscrepancy = discrepancies.find(d => d.field === 'amount') ? 'amount' : 'status';
			return {
				type: primaryDiscrepancy === 'amount' ? this.reconciliationTypes.AMOUNT_MISMATCH : this.reconciliationTypes.STATUS_MISMATCH,
				payment_intent_id: stripePayment.id,
				stripe_payment: stripePayment,
				db_payment: dbPayment,
				discrepancies,
				details: `Mismatch in ${discrepancies.map(d => d.field).join(', ')}`,
			};
		}
	}

	/**
	 * Generate reconciliation summary
	 * @param {Array} results - Reconciliation results
	 * @returns {Object} Summary
	 */
	generateSummary(results) {
		const summary = {
			total_comparisons: results.length,
			matches: 0,
			missing_in_db: 0,
			missing_in_stripe: 0,
			amount_mismatches: 0,
			status_mismatches: 0,
		};

		for (const result of results) {
			switch (result.type) {
				case this.reconciliationTypes.MATCH:
					summary.matches++;
					break;
				case this.reconciliationTypes.MISSING_IN_DB:
					summary.missing_in_db++;
					break;
				case this.reconciliationTypes.MISSING_IN_STRIPE:
					summary.missing_in_stripe++;
					break;
				case this.reconciliationTypes.AMOUNT_MISMATCH:
					summary.amount_mismatches++;
					break;
				case this.reconciliationTypes.STATUS_MISMATCH:
					summary.status_mismatches++;
					break;
			}
		}

		summary.match_rate = summary.total_comparisons > 0 
			? ((summary.matches / summary.total_comparisons) * 100).toFixed(2) + '%'
			: '0%';

		summary.total_discrepancies = summary.total_comparisons - summary.matches;

		return summary;
	}

	/**
	 * Store reconciliation report in database
	 * @param {Object} report - Reconciliation report
	 */
	async storeReconciliationReport(report) {
		try {
			await db("dtm_ads.reconciliation_reports").insert({
				reconciliation_id: report.reconciliation_id,
				period_start: report.period.start_date,
				period_end: report.period.end_date,
				total_stripe_payments: report.total_stripe_payments,
				total_db_payments: report.total_db_payments,
				total_matches: report.summary.matches,
				total_discrepancies: report.summary.total_discrepancies,
				match_rate: parseFloat(report.summary.match_rate),
				report_data: JSON.stringify(report),
				created_at: new Date(),
			});
		} catch (error) {
			logger.error("Failed to store reconciliation report", {
				error: error.message,
				reconciliationId: report.reconciliation_id,
			});
		}
	}

	/**
	 * Map database status to Stripe status
	 * @param {string} dbStatus - Database status
	 * @returns {string} Stripe status
	 */
	mapDbStatusToStripe(dbStatus) {
		const statusMap = {
			'completed': 'succeeded',
			'failed': 'failed',
			'processing_error': 'failed',
			'pending': 'processing',
		};
		return statusMap[dbStatus] || dbStatus;
	}

	/**
	 * Generate unique reconciliation ID
	 * @returns {string} Reconciliation ID
	 */
	generateReconciliationId() {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 8);
		return `recon_${timestamp}_${random}`;
	}

	/**
	 * Get reconciliation history
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Reconciliation reports
	 */
	async getReconciliationHistory(options = {}) {
		const { limit = 20, offset = 0, startDate, endDate } = options;

		let query = db("dtm_ads.reconciliation_reports")
			.orderBy("created_at", "desc")
			.limit(limit)
			.offset(offset);

		if (startDate && endDate) {
			query = query.whereBetween("created_at", [startDate, endDate]);
		}

		return await query.select("*");
	}

	/**
	 * Get specific reconciliation report
	 * @param {string} reconciliationId - Reconciliation ID
	 * @returns {Promise<Object>} Reconciliation report
	 */
	async getReconciliationReport(reconciliationId) {
		const report = await db("dtm_ads.reconciliation_reports")
			.where({ reconciliation_id: reconciliationId })
			.first();

		if (!report) {
			throw new Error(`Reconciliation report not found: ${reconciliationId}`);
		}

		return {
			...report,
			report_data: JSON.parse(report.report_data),
		};
	}
}

module.exports = PaymentReconciliationService;
