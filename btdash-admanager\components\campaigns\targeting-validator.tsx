"use client";

import { useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle, CheckCircle, Info, TrendingUp, Users } from "lucide-react";

interface TargetingRules {
  countries: {
    mode: "all" | "include" | "exclude";
    include: string[];
    exclude: string[];
  };
  pageTypes: {
    types: string[];
    categories: Record<string, string[] | "all">;
  };
  devices: string[];
  languages: string[];
  interests: string[];
  age: string[];
}

interface ValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
  warnings: string[];
  effectiveness?: {
    score: number;
    issues: string[];
    recommendations: string[];
    estimatedReach: string;
  };
}

interface TargetingValidatorProps {
  targetingRules: TargetingRules;
  onValidationChange?: (result: ValidationResult) => void;
  showEffectiveness?: boolean;
}

export function TargetingValidator({ 
  targetingRules, 
  onValidationChange, 
  showEffectiveness = true 
}: TargetingValidatorProps) {
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    const validateTargeting = async () => {
      if (!targetingRules) return;

      setIsValidating(true);
      try {
        const response = await fetch("/api/targeting/validate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(targetingRules),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setValidation(result.data);
            onValidationChange?.(result.data);
          }
        }
      } catch (error) {
        console.error("Error validating targeting:", error);
      } finally {
        setIsValidating(false);
      }
    };

    // Debounce validation
    const timeoutId = setTimeout(validateTargeting, 500);
    return () => clearTimeout(timeoutId);
  }, [targetingRules, onValidationChange]);

  if (!validation && !isValidating) {
    return null;
  }

  if (isValidating) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span className="text-sm text-muted-foreground">Validating targeting rules...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!validation) return null;

  const getEffectivenessColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getReachColor = (reach: string) => {
    switch (reach.toLowerCase()) {
      case "very high":
        return "bg-green-100 text-green-800";
      case "high":
        return "bg-blue-100 text-blue-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-4">
      {/* Validation Errors */}
      {validation.errors && validation.errors.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Targeting validation errors:</div>
              <ul className="list-disc list-inside space-y-1">
                {validation.errors.map((error, index) => (
                  <li key={index} className="text-sm">
                    <span className="font-medium">{error.field}:</span> {error.message}
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Validation Warnings */}
      {validation.warnings && validation.warnings.length > 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Targeting recommendations:</div>
              <ul className="list-disc list-inside space-y-1">
                {validation.warnings.map((warning, index) => (
                  <li key={index} className="text-sm">{warning}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {validation.valid && validation.errors.length === 0 && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Targeting rules are valid and ready to use.
          </AlertDescription>
        </Alert>
      )}

      {/* Effectiveness Analysis */}
      {showEffectiveness && validation.effectiveness && validation.valid && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Targeting Effectiveness</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Effectiveness Score */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Effectiveness Score</span>
                <span className={`text-sm font-bold ${getEffectivenessColor(validation.effectiveness.score)}`}>
                  {validation.effectiveness.score}/100
                </span>
              </div>
              <Progress 
                value={validation.effectiveness.score} 
                className="h-2"
              />
            </div>

            {/* Estimated Reach */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Estimated Reach</span>
              </div>
              <Badge className={getReachColor(validation.effectiveness.estimatedReach)}>
                {validation.effectiveness.estimatedReach}
              </Badge>
            </div>

            {/* Issues */}
            {validation.effectiveness.issues.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-yellow-700">Potential Issues:</div>
                <ul className="list-disc list-inside space-y-1">
                  {validation.effectiveness.issues.map((issue, index) => (
                    <li key={index} className="text-sm text-yellow-600">{issue}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Recommendations */}
            {validation.effectiveness.recommendations.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-blue-700">Recommendations:</div>
                <ul className="list-disc list-inside space-y-1">
                  {validation.effectiveness.recommendations.map((rec, index) => (
                    <li key={index} className="text-sm text-blue-600">{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Hook for using targeting validation
export function useTargetingValidation(targetingRules: TargetingRules) {
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const validateTargeting = async (rules: TargetingRules) => {
    setIsValidating(true);
    try {
      const response = await fetch("/api/targeting/validate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(rules),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setValidation(result.data);
          return result.data;
        }
      }
      return null;
    } catch (error) {
      console.error("Error validating targeting:", error);
      return null;
    } finally {
      setIsValidating(false);
    }
  };

  useEffect(() => {
    if (targetingRules) {
      const timeoutId = setTimeout(() => validateTargeting(targetingRules), 500);
      return () => clearTimeout(timeoutId);
    }
  }, [targetingRules]);

  return {
    validation,
    isValidating,
    validateTargeting,
    isValid: validation?.valid ?? false,
    hasErrors: validation?.errors && validation.errors.length > 0,
    hasWarnings: validation?.warnings && validation.warnings.length > 0,
  };
}
