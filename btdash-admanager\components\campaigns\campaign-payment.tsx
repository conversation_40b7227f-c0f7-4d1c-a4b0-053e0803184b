"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { loadStripe } from "@stripe/stripe-js";
import { AlertCircle, CheckCircle, CreditCard, Loader2 } from "lucide-react";
import { useState } from "react";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface CampaignPaymentProps {
	campaign: {
		id: number;
		name: string;
		status: string;
		total_budget: number;
		start_date: string;
		end_date: string;
		placement?: {
			name: string;
			page_type: string;
		};
		targeting?: any;
	};
	onPaymentSuccess?: () => void;
}

export function CampaignPayment({ campaign, onPaymentSuccess }: CampaignPaymentProps) {
	const [isProcessing, setIsProcessing] = useState(false);
	const [paymentError, setPaymentError] = useState<string | null>(null);
	const { toast } = useToast();

	const handlePayment = async () => {
		if (campaign.status !== "approved") {
			toast({
				title: "Payment Not Available",
				description: "Only approved campaigns can be paid for.",
				variant: "destructive",
			});
			return;
		}

		setIsProcessing(true);
		setPaymentError(null);

		try {
			// Create checkout session
			const response = await fetch("/api/stripe/create-checkout-session", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					campaignId: campaign.id,
					amount: campaign.total_budget, // Amount in dollars
					campaignName: campaign.name,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to create checkout session");
			}

			// Redirect to Stripe Checkout
			const stripe = await stripePromise;
			if (!stripe) {
				throw new Error("Stripe failed to load");
			}

			const { error } = await stripe.redirectToCheckout({
				sessionId: data.sessionId,
			});

			if (error) {
				throw new Error(error.message);
			}
		} catch (error: any) {
			console.error("Payment error:", error);
			setPaymentError(error.message || "Payment failed. Please try again.");
			toast({
				title: "Payment Failed",
				description: error.message || "Unable to process payment. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsProcessing(false);
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	if (campaign.status !== "approved") {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<CreditCard className="h-5 w-5" />
						Payment
					</CardTitle>
					<CardDescription>Payment will be available once your campaign is approved.</CardDescription>
				</CardHeader>
				<CardContent>
					<Alert>
						<AlertCircle className="h-4 w-4" />
						<AlertDescription>
							Your campaign is currently under review. You'll be able to complete payment once it's
							approved.
						</AlertDescription>
					</Alert>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<CreditCard className="h-5 w-5" />
					Complete Payment
				</CardTitle>
				<CardDescription>Your campaign has been approved. Complete payment to make it live.</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Campaign Summary */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<span className="font-medium">Campaign</span>
						<span>{campaign.name}</span>
					</div>

					<div className="flex items-center justify-between">
						<span className="font-medium">Placement</span>
						<span>{campaign.placement?.name || "Multiple placements"}</span>
					</div>

					<div className="flex items-center justify-between">
						<span className="font-medium">Duration</span>
						<span>
							{formatDate(campaign.start_date)} - {formatDate(campaign.end_date)}
						</span>
					</div>

					<Separator />

					<div className="flex items-center justify-between text-lg font-semibold">
						<span>Total Budget</span>
						<span>{formatCurrency(campaign.total_budget)}</span>
					</div>
				</div>

				{/* Payment Error */}
				{paymentError && (
					<Alert className="border-red-200 bg-red-50">
						<AlertCircle className="h-4 w-4 text-red-600" />
						<AlertDescription className="text-red-800">{paymentError}</AlertDescription>
					</Alert>
				)}

				{/* Payment Button */}
				<div className="space-y-3">
					<Button
						onClick={handlePayment}
						disabled={isProcessing}
						className="w-full bg-green-600 hover:bg-green-700"
						size="lg"
					>
						{isProcessing ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Processing...
							</>
						) : (
							<>
								<CreditCard className="mr-2 h-4 w-4" />
								Pay {formatCurrency(campaign.total_budget)}
							</>
						)}
					</Button>

					<p className="text-xs text-muted-foreground text-center">
						Secure payment powered by Stripe. Your campaign will go live immediately after payment.
					</p>
				</div>

				{/* Payment Info */}
				<Alert className="border-blue-200 bg-blue-50">
					<CheckCircle className="h-4 w-4 text-blue-600" />
					<AlertDescription className="text-blue-800">
						<div className="space-y-1">
							<p className="font-medium">What happens next?</p>
							<ul className="text-sm space-y-1">
								<li>• Payment will be processed securely via Stripe</li>
								<li>• Your campaign will go live immediately</li>
								<li>• You'll receive a confirmation email</li>
								<li>• Track performance in the Analytics section</li>
							</ul>
						</div>
					</AlertDescription>
				</Alert>
			</CardContent>
		</Card>
	);
}
