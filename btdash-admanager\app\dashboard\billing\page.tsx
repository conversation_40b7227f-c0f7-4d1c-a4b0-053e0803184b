"use client";

import { PaymentMethodForm } from "@/components/stripe/payment-method-form";
import { PaymentMethodList } from "@/components/stripe/payment-method-list";
import { StripeProvider } from "@/components/stripe/stripe-provider";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { CheckCircle, DollarSign, Download, Target, TrendingUp } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import useSWR from "swr";

const fetcher = (url: string) => fetch(url).then((res) => res.json());

interface BillingTransaction {
	id: number;
	amount: number;
	currency: string;
	description: string;
	status: string;
	payment_method?: string;
	campaign_id?: number;
	created_at: string;
}

interface CampaignSpending {
	id: number;
	name: string;
	total_budget: number;
	spent: number;
	status: string;
	start_date: string;
	end_date: string;
}

export default function BillingPage() {
	const searchParams = useSearchParams();
	const defaultTab = searchParams.get("tab") || "overview";

	const { data: user, error: userError, isLoading } = useSWR("/api/user/me", fetcher);
	const [loading, setLoading] = useState(false);
	const [transactions, setTransactions] = useState<BillingTransaction[]>([]);
	const [campaigns, setCampaigns] = useState<CampaignSpending[]>([]);
	const [invoices, setInvoices] = useState<any[]>([]);
	const [customerId, setCustomerId] = useState<string | null>(null);
	const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);

	// Fetch billing data on component mount
	useEffect(() => {
		if (user) {
			fetchBillingData();
			fetchCustomerId();
		}
	}, [user]);

	const fetchBillingData = async () => {
		try {
			// Fetch transactions
			const transactionsResponse = await fetch("/api/user/billing/transactions");
			if (transactionsResponse.ok) {
				const transactionsData = await transactionsResponse.json();
				setTransactions(transactionsData.transactions || []);
			}

			// Fetch campaign spending
			const campaignsResponse = await fetch("/api/user/billing/campaigns");
			if (campaignsResponse.ok) {
				const campaignsData = await campaignsResponse.json();
				setCampaigns(campaignsData.campaigns || []);
			}

			// Fetch invoices
			const invoicesResponse = await fetch("/api/user/invoices");
			if (invoicesResponse.ok) {
				const invoicesData = await invoicesResponse.json();
				setInvoices(invoicesData.invoices || []);
			}
		} catch (error) {
			console.error("Error fetching billing data:", error);
		}
	};

	const fetchCustomerId = async () => {
		try {
			const response = await fetch("/api/user/stripe-customer");
			if (response.ok) {
				const data = await response.json();
				setCustomerId(data.customerId);
			}
		} catch (error) {
			console.error("Error fetching customer ID:", error);
		}
	};

	const handlePaymentMethodAdded = () => {
		setShowAddPaymentMethod(false);
		toast({
			title: "Payment method added",
			description: "Your payment method has been saved successfully.",
		});
	};

	const calculateTotalSpent = () => {
		return transactions.filter((t) => t.status === "completed").reduce((sum, t) => sum + t.amount, 0);
	};

	const calculateActiveSpending = () => {
		return campaigns
			.filter((c) => c.status === "active" || c.status === "running")
			.reduce((sum, c) => sum + c.spent, 0);
	};

	if (isLoading) {
		return <div>Loading...</div>;
	}

	if (userError || !user) {
		return <div>Error loading user data</div>;
	}

	return (
		<StripeProvider>
			<div className="flex flex-col gap-6">
				<div className="flex items-center justify-between">
					<h1 className="text-3xl font-bold tracking-tight">Billing & Payments</h1>
					<Button variant="outline">
						<Download className="mr-2 h-4 w-4" />
						Export Transactions
					</Button>
				</div>

				<Tabs defaultValue={defaultTab} className="space-y-4">
					<TabsList>
						<TabsTrigger value="overview">Overview</TabsTrigger>
						<TabsTrigger value="transactions">Payment History</TabsTrigger>
						<TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
						<TabsTrigger value="invoices">Invoices</TabsTrigger>
						<TabsTrigger value="campaigns">Campaign Costs</TabsTrigger>
					</TabsList>

					<TabsContent value="overview" className="space-y-4">
						<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">Total Spent</CardTitle>
									<DollarSign className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">${calculateTotalSpent().toFixed(2)}</div>
									<p className="text-xs text-muted-foreground">All time</p>
								</CardContent>
							</Card>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">Active Spending</CardTitle>
									<TrendingUp className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">${calculateActiveSpending().toFixed(2)}</div>
									<p className="text-xs text-muted-foreground">Current campaigns</p>
								</CardContent>
							</Card>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
									<Target className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">
										{
											campaigns.filter((c) => c.status === "active" || c.status === "running")
												.length
										}
									</div>
									<p className="text-xs text-muted-foreground">Currently running</p>
								</CardContent>
							</Card>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">Payment Status</CardTitle>
									<CheckCircle className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">{customerId ? "Active" : "Setup Required"}</div>
									<p className="text-xs text-muted-foreground">
										{customerId ? "Payment methods configured" : "Add payment method"}
									</p>
								</CardContent>
							</Card>
						</div>

						<Card>
							<CardHeader>
								<CardTitle>Recent Transactions</CardTitle>
								<CardDescription>Your latest campaign payments and billing activity</CardDescription>
							</CardHeader>
							<CardContent>
								{transactions.length === 0 ? (
									<div className="text-center py-8">
										<DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
										<h3 className="text-lg font-medium mb-2">No transactions yet</h3>
										<p className="text-muted-foreground">
											Your campaign payments will appear here once you start running ads.
										</p>
									</div>
								) : (
									<div className="space-y-4">
										{transactions.slice(0, 5).map((transaction) => (
											<div
												key={transaction.id}
												className="flex items-center justify-between p-4 border rounded-lg"
											>
												<div className="flex items-center gap-3">
													<div className="rounded-full p-2 bg-muted">
														<DollarSign className="h-4 w-4" />
													</div>
													<div>
														<p className="font-medium">{transaction.description}</p>
														<p className="text-sm text-muted-foreground">
															{new Date(transaction.created_at).toLocaleDateString()}
														</p>
													</div>
												</div>
												<div className="text-right">
													<p className="font-medium">${transaction.amount.toFixed(2)}</p>
													<Badge
														variant={
															transaction.status === "completed" ? "default" : "secondary"
														}
													>
														{transaction.status}
													</Badge>
												</div>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="payment-methods" className="space-y-4">
						<div className="grid gap-6 md:grid-cols-2">
							<div>
								<PaymentMethodList
									customerId={customerId || undefined}
									onPaymentMethodDeleted={() => {
										// Refresh data if needed
									}}
									showAddButton={true}
									onAddClick={() => setShowAddPaymentMethod(true)}
								/>
							</div>
							<div>
								{showAddPaymentMethod && (
									<PaymentMethodForm
										customerId={customerId || undefined}
										onSuccess={handlePaymentMethodAdded}
										onError={(error) => {
											toast({
												title: "Error",
												description: error,
												variant: "destructive",
											});
										}}
									/>
								)}
							</div>
						</div>
					</TabsContent>

					<TabsContent value="transactions" className="space-y-4">
						<Card>
							<CardHeader>
								<CardTitle>Payment History</CardTitle>
								<CardDescription>
									Complete history of all campaign payments and transactions
								</CardDescription>
							</CardHeader>
							<CardContent>
								{transactions.length === 0 ? (
									<div className="text-center py-12">
										<DollarSign className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
										<h3 className="text-xl font-medium mb-2">No transactions yet</h3>
										<p className="text-muted-foreground">
											Your payment history will appear here once you start running campaigns.
										</p>
									</div>
								) : (
									<div className="space-y-4">
										<div className="grid grid-cols-6 gap-4 p-4 font-medium border-b">
											<div>Date</div>
											<div>Description</div>
											<div>Campaign</div>
											<div>Amount</div>
											<div>Status</div>
											<div>Actions</div>
										</div>
										{transactions.map((transaction) => (
											<div
												key={transaction.id}
												className="grid grid-cols-6 gap-4 p-4 items-center border-b"
											>
												<div className="text-sm">
													{new Date(transaction.created_at).toLocaleDateString()}
												</div>
												<div className="font-medium">{transaction.description}</div>
												<div className="text-sm text-muted-foreground">
													{transaction.campaign_id
														? `Campaign #${transaction.campaign_id}`
														: "-"}
												</div>
												<div className="font-medium">${transaction.amount.toFixed(2)}</div>
												<div>
													<Badge
														variant={
															transaction.status === "completed" ? "default" : "secondary"
														}
													>
														{transaction.status}
													</Badge>
												</div>
												<div>
													<Button variant="ghost" size="sm">
														<Download className="h-4 w-4" />
													</Button>
												</div>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="invoices" className="space-y-4">
						<Card>
							<CardHeader>
								<CardTitle>Invoice History</CardTitle>
								<CardDescription>View and download your campaign invoices</CardDescription>
							</CardHeader>
							<CardContent>
								{invoices.length === 0 ? (
									<div className="text-center py-12">
										<Download className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
										<h3 className="text-xl font-medium mb-2">No invoices yet</h3>
										<p className="text-muted-foreground">
											Invoices will be generated automatically when campaigns are billed.
										</p>
									</div>
								) : (
									<div className="space-y-4">
										<div className="grid grid-cols-6 gap-4 p-4 font-medium border-b">
											<div>Invoice #</div>
											<div>Date</div>
											<div>Campaign</div>
											<div>Amount</div>
											<div>Status</div>
											<div>Actions</div>
										</div>
										{invoices.map((invoice) => (
											<div
												key={invoice.id}
												className="grid grid-cols-6 gap-4 p-4 items-center border-b"
											>
												<div className="font-medium">{invoice.invoice_number}</div>
												<div className="text-sm">
													{new Date(invoice.created_at).toLocaleDateString()}
												</div>
												<div className="text-sm text-muted-foreground">
													{invoice.campaign_id
														? `Campaign #${invoice.campaign_id}`
														: "General"}
												</div>
												<div className="font-medium">
													${parseFloat(invoice.total_amount).toFixed(2)}
												</div>
												<div>
													<Badge
														variant={invoice.status === "paid" ? "default" : "secondary"}
													>
														{invoice.status.charAt(0).toUpperCase() +
															invoice.status.slice(1)}
													</Badge>
												</div>
												<div className="flex gap-2">
													<Button
														variant="ghost"
														size="sm"
														onClick={() =>
															window.open(
																`/api/user/invoices/${invoice.id}/download`,
																"_blank"
															)
														}
													>
														<Download className="h-4 w-4" />
														<span className="sr-only">Download Invoice</span>
													</Button>
												</div>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="campaigns" className="space-y-4">
						<Card>
							<CardHeader>
								<CardTitle>Campaign Costs</CardTitle>
								<CardDescription>
									Track spending and budget usage for all your campaigns
								</CardDescription>
							</CardHeader>
							<CardContent>
								{campaigns.length === 0 ? (
									<div className="text-center py-12">
										<Target className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
										<h3 className="text-xl font-medium mb-2">No campaigns yet</h3>
										<p className="text-muted-foreground">
											Create your first campaign to start tracking costs and spending.
										</p>
									</div>
								) : (
									<div className="space-y-4">
										<div className="grid grid-cols-6 gap-4 p-4 font-medium border-b">
											<div>Campaign</div>
											<div>Budget</div>
											<div>Spent</div>
											<div>Remaining</div>
											<div>Status</div>
											<div>Duration</div>
										</div>
										{campaigns.map((campaign) => (
											<div
												key={campaign.id}
												className="grid grid-cols-6 gap-4 p-4 items-center border-b"
											>
												<div className="font-medium">{campaign.name}</div>
												<div className="text-sm">${campaign.total_budget.toFixed(2)}</div>
												<div className="text-sm">${campaign.spent.toFixed(2)}</div>
												<div className="text-sm">
													${(campaign.total_budget - campaign.spent).toFixed(2)}
												</div>
												<div>
													<Badge
														variant={
															campaign.status === "active" ||
															campaign.status === "running"
																? "default"
																: "secondary"
														}
													>
														{campaign.status}
													</Badge>
												</div>
												<div className="text-sm text-muted-foreground">
													{new Date(campaign.start_date).toLocaleDateString()} -{" "}
													{new Date(campaign.end_date).toLocaleDateString()}
												</div>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>
					</TabsContent>
				</Tabs>
			</div>
		</StripeProvider>
	);
}
