{"dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-jwt": "^8.5.1", "ioredis": "^5.6.1", "joi": "^17.13.3", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "knex": "^3.1.0", "module-alias": "^2.2.3", "multer": "^2.0.1", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "pdfkit": "^0.15.0", "pg": "^8.13.3", "sharp": "^0.34.2", "stripe": "^18.2.1", "winston": "^3.17.0", "winston-papertrail-transport": "^1.0.9"}, "devDependencies": {"babel-plugin-module-resolver": "^5.0.2", "supertest": "^7.1.1"}, "name": "btdashs_api", "version": "1.0.0", "description": "This is a private RESTful API built with Node.js to manage and store subnet data fetched from [TaoStats API](https://docs.taostats.io/reference/get-subnets-1). The API processes and stores subnet data periodically for later retrieval and analysis.", "main": "app.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test:endpoints": "node test-api-endpoints-comprehensive.js", "test:all": "npm run test:endpoints", "webhooks:setup": "node scripts/setup-webhooks.js", "webhooks:test": "node scripts/setup-webhooks.js --test", "webhooks:list": "node scripts/setup-webhooks.js --list", "webhooks:ngrok": "node scripts/setup-webhooks.js --ngrok"}, "_moduleAliases": {"@": "./src"}, "keywords": [], "author": "", "license": "ISC"}