const express = require("express");
const router = express.Router();
const webhookController = require("../controllers/webhookController");
const { checkJwt, checkInternalKey } = require("../../middleware/auth");
const rateLimits = require("../../middleware/rateLimits");

// Public health check endpoint (no auth required)
router.get("/health", rateLimits.webhookPublic, webhookController.webhookHealth);

// Webhook management endpoints (require authentication)
router.get("/status", checkJwt, rateLimits.userGeneral, webhookController.getWebhookStatus);
router.get("/config", checkJwt, rateLimits.userGeneral, webhookController.getWebhookConfig);

// Stripe webhook management (admin/internal only)
router.post("/stripe/setup", checkInternalKey, rateLimits.ipStrict, webhookController.setupStripeWebhook);
router.get("/stripe/list", checkInternalKey, rateLimits.ipStrict, webhookController.listStripeWebhooks);
router.delete("/stripe/:endpointId", checkInternalKey, rateLimits.ipStrict, webhookController.deleteStripeWebhook);

// Webhook testing
router.post("/test", checkInternalKey, rateLimits.ipStrict, webhookController.testWebhookEndpoint);

// Webhook retry and monitoring
router.get("/retry-stats", checkJwt, rateLimits.userGeneral, webhookController.getWebhookRetryStats);
router.get("/failures", checkJwt, rateLimits.userGeneral, webhookController.getRecentWebhookFailures);
router.post("/retry/:webhookLogId", checkInternalKey, rateLimits.ipStrict, webhookController.retryFailedWebhook);

// Initialize webhooks for current environment
router.post("/initialize", checkInternalKey, rateLimits.ipStrict, webhookController.initializeWebhooks);

module.exports = router;
