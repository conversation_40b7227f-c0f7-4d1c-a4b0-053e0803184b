/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/create-checkout-session/route";
exports.ids = ["app/api/stripe/create-checkout-session/route"];
exports.modules = {

/***/ "(rsc)/./app/api/stripe/create-checkout-session/route.ts":
/*!*********************************************************!*\
  !*** ./app/api/stripe/create-checkout-session/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _lib_stripe_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-server */ \"(rsc)/./lib/stripe-server.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)();\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        const { amount, campaignId, campaignName } = await request.json();\n        // Validate input\n        if (!amount || amount <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid amount is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!campaignId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Campaign ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Create Stripe Checkout Session\n        const checkoutSession = await _lib_stripe_server__WEBPACK_IMPORTED_MODULE_2__[\"default\"].checkout.sessions.create({\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price_data: {\n                        currency: 'usd',\n                        product_data: {\n                            name: `Campaign Payment: ${campaignName || `Campaign #${campaignId}`}`,\n                            description: `Payment for advertising campaign`\n                        },\n                        unit_amount: Math.round(amount * 100)\n                    },\n                    quantity: 1\n                }\n            ],\n            mode: 'payment',\n            success_url: `${process.env.NEXTAUTH_URL || process.env.AUTH0_BASE_URL}/dashboard/campaigns/${campaignId}?payment=success`,\n            cancel_url: `${process.env.NEXTAUTH_URL || process.env.AUTH0_BASE_URL}/dashboard/campaigns/${campaignId}?payment=cancelled`,\n            metadata: {\n                campaignId: campaignId.toString(),\n                userId: session.user.sub,\n                type: 'campaign_payment'\n            },\n            customer_email: session.user.email\n        });\n        // Store payment session in database for tracking\n        try {\n            const response = await fetch(`${process.env.API_BASE_URL}/billing/payment-intent`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${session.accessToken}`,\n                    'X-Internal-Key': process.env.INTERNAL_API_KEY || ''\n                },\n                body: JSON.stringify({\n                    payment_intent_id: checkoutSession.payment_intent,\n                    campaign_id: campaignId,\n                    amount: amount,\n                    status: 'pending',\n                    currency: 'usd',\n                    session_id: checkoutSession.id\n                })\n            });\n            if (!response.ok) {\n                console.error('Failed to store payment session in database');\n            // Continue anyway, as the checkout session was created successfully\n            }\n        } catch (dbError) {\n            console.error('Database error:', dbError);\n        // Continue anyway\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            sessionId: checkoutSession.id,\n            url: checkoutSession.url\n        });\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        const errorMessage = error instanceof Error ? error.message : 'Failed to create checkout session';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/stripe/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/stripe-server.ts":
/*!******************************!*\
  !*** ./lib/stripe-server.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   stripe: () => (/* binding */ stripe),\n/* harmony export */   stripeHelpers: () => (/* binding */ stripeHelpers)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stripe */ \"(rsc)/./lib/stripe.ts\");\n\n\n// Validate configuration on import and log results\n(0,_stripe__WEBPACK_IMPORTED_MODULE_1__.logStripeConfigValidation)();\n// Create Stripe instance for server-side operations\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](_stripe__WEBPACK_IMPORTED_MODULE_1__.stripeConfig.secretKey, {\n    apiVersion: \"2024-12-18.acacia\",\n    typescript: true\n});\n// Helper functions for common Stripe operations\nconst stripeHelpers = {\n    // Create a payment intent for campaign billing\n    async createPaymentIntent (amount, metadata) {\n        return await stripe.paymentIntents.create({\n            amount: Math.round(amount * 100),\n            currency: _stripe__WEBPACK_IMPORTED_MODULE_1__.stripeConfig.currency,\n            metadata,\n            automatic_payment_methods: {\n                enabled: true\n            }\n        });\n    },\n    // Create a customer\n    async createCustomer (email, name, metadata) {\n        return await stripe.customers.create({\n            email,\n            name,\n            metadata\n        });\n    },\n    // Retrieve a customer\n    async getCustomer (customerId) {\n        return await stripe.customers.retrieve(customerId);\n    },\n    // Create a setup intent for saving payment methods\n    async createSetupIntent (customerId) {\n        return await stripe.setupIntents.create({\n            customer: customerId,\n            payment_method_types: [\n                \"card\"\n            ]\n        });\n    },\n    // List customer payment methods\n    async listPaymentMethods (customerId) {\n        return await stripe.paymentMethods.list({\n            customer: customerId,\n            type: \"card\"\n        });\n    },\n    // Detach a payment method\n    async detachPaymentMethod (paymentMethodId) {\n        return await stripe.paymentMethods.detach(paymentMethodId);\n    },\n    // Confirm a payment intent\n    async confirmPaymentIntent (paymentIntentId, paymentMethodId) {\n        const updateData = {};\n        if (paymentMethodId) {\n            updateData.payment_method = paymentMethodId;\n        }\n        return await stripe.paymentIntents.confirm(paymentIntentId, updateData);\n    },\n    // Retrieve payment intent\n    async getPaymentIntent (paymentIntentId) {\n        return await stripe.paymentIntents.retrieve(paymentIntentId);\n    },\n    // Create an invoice with line items\n    async createInvoice (customerId, options) {\n        const { description, metadata, lineItems = [], autoAdvance = false } = options || {};\n        // Create invoice\n        const invoice = await stripe.invoices.create({\n            customer: customerId,\n            description,\n            metadata,\n            auto_advance: autoAdvance,\n            collection_method: \"charge_automatically\"\n        });\n        // Add line items if provided\n        for (const item of lineItems){\n            await stripe.invoiceItems.create({\n                customer: customerId,\n                invoice: invoice.id,\n                description: item.description,\n                quantity: item.quantity || 1,\n                unit_amount: item.unitAmount\n            });\n        }\n        return invoice;\n    },\n    // Finalize an invoice\n    async finalizeInvoice (invoiceId) {\n        return await stripe.invoices.finalizeInvoice(invoiceId);\n    },\n    // Pay an invoice\n    async payInvoice (invoiceId) {\n        return await stripe.invoices.pay(invoiceId);\n    },\n    // Verify webhook signature\n    verifyWebhookSignature (payload, signature) {\n        return stripe.webhooks.constructEvent(payload, signature, _stripe__WEBPACK_IMPORTED_MODULE_1__.stripeConfig.webhookSecret);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stripe);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvc3RyaXBlLXNlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QjtBQUN1QztBQUVuRSxtREFBbUQ7QUFDbkRDLGtFQUF5QkE7QUFFekIsb0RBQW9EO0FBQzdDLE1BQU1FLFNBQVMsSUFBSUgsOENBQU1BLENBQUNFLGlEQUFZQSxDQUFDRSxTQUFTLEVBQUc7SUFDekRDLFlBQVk7SUFDWkMsWUFBWTtBQUNiLEdBQUc7QUFFSCxnREFBZ0Q7QUFDekMsTUFBTUMsZ0JBQWdCO0lBQzVCLCtDQUErQztJQUMvQyxNQUFNQyxxQkFBb0JDLE1BQWMsRUFBRUMsUUFBZ0M7UUFDekUsT0FBTyxNQUFNUCxPQUFPUSxjQUFjLENBQUNDLE1BQU0sQ0FBQztZQUN6Q0gsUUFBUUksS0FBS0MsS0FBSyxDQUFDTCxTQUFTO1lBQzVCTSxVQUFVYixpREFBWUEsQ0FBQ2EsUUFBUTtZQUMvQkw7WUFDQU0sMkJBQTJCO2dCQUMxQkMsU0FBUztZQUNWO1FBQ0Q7SUFDRDtJQUVBLG9CQUFvQjtJQUNwQixNQUFNQyxnQkFBZUMsS0FBYSxFQUFFQyxJQUFhLEVBQUVWLFFBQWlDO1FBQ25GLE9BQU8sTUFBTVAsT0FBT2tCLFNBQVMsQ0FBQ1QsTUFBTSxDQUFDO1lBQ3BDTztZQUNBQztZQUNBVjtRQUNEO0lBQ0Q7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTVksYUFBWUMsVUFBa0I7UUFDbkMsT0FBTyxNQUFNcEIsT0FBT2tCLFNBQVMsQ0FBQ0csUUFBUSxDQUFDRDtJQUN4QztJQUVBLG1EQUFtRDtJQUNuRCxNQUFNRSxtQkFBa0JGLFVBQWtCO1FBQ3pDLE9BQU8sTUFBTXBCLE9BQU91QixZQUFZLENBQUNkLE1BQU0sQ0FBQztZQUN2Q2UsVUFBVUo7WUFDVkssc0JBQXNCO2dCQUFDO2FBQU87UUFDL0I7SUFDRDtJQUVBLGdDQUFnQztJQUNoQyxNQUFNQyxvQkFBbUJOLFVBQWtCO1FBQzFDLE9BQU8sTUFBTXBCLE9BQU8yQixjQUFjLENBQUNDLElBQUksQ0FBQztZQUN2Q0osVUFBVUo7WUFDVlMsTUFBTTtRQUNQO0lBQ0Q7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUMscUJBQW9CQyxlQUF1QjtRQUNoRCxPQUFPLE1BQU0vQixPQUFPMkIsY0FBYyxDQUFDSyxNQUFNLENBQUNEO0lBQzNDO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1FLHNCQUFxQkMsZUFBdUIsRUFBRUgsZUFBd0I7UUFDM0UsTUFBTUksYUFBZ0QsQ0FBQztRQUV2RCxJQUFJSixpQkFBaUI7WUFDcEJJLFdBQVdDLGNBQWMsR0FBR0w7UUFDN0I7UUFFQSxPQUFPLE1BQU0vQixPQUFPUSxjQUFjLENBQUM2QixPQUFPLENBQUNILGlCQUFpQkM7SUFDN0Q7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUcsa0JBQWlCSixlQUF1QjtRQUM3QyxPQUFPLE1BQU1sQyxPQUFPUSxjQUFjLENBQUNhLFFBQVEsQ0FBQ2E7SUFDN0M7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTUssZUFDTG5CLFVBQWtCLEVBQ2xCb0IsT0FTQztRQUVELE1BQU0sRUFBRUMsV0FBVyxFQUFFbEMsUUFBUSxFQUFFbUMsWUFBWSxFQUFFLEVBQUVDLGNBQWMsS0FBSyxFQUFFLEdBQUdILFdBQVcsQ0FBQztRQUVuRixpQkFBaUI7UUFDakIsTUFBTUksVUFBVSxNQUFNNUMsT0FBTzZDLFFBQVEsQ0FBQ3BDLE1BQU0sQ0FBQztZQUM1Q2UsVUFBVUo7WUFDVnFCO1lBQ0FsQztZQUNBdUMsY0FBY0g7WUFDZEksbUJBQW1CO1FBQ3BCO1FBRUEsNkJBQTZCO1FBQzdCLEtBQUssTUFBTUMsUUFBUU4sVUFBVztZQUM3QixNQUFNMUMsT0FBT2lELFlBQVksQ0FBQ3hDLE1BQU0sQ0FBQztnQkFDaENlLFVBQVVKO2dCQUNWd0IsU0FBU0EsUUFBUU0sRUFBRTtnQkFDbkJULGFBQWFPLEtBQUtQLFdBQVc7Z0JBQzdCVSxVQUFVSCxLQUFLRyxRQUFRLElBQUk7Z0JBQzNCQyxhQUFhSixLQUFLSyxVQUFVO1lBQzdCO1FBQ0Q7UUFFQSxPQUFPVDtJQUNSO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1VLGlCQUFnQkMsU0FBaUI7UUFDdEMsT0FBTyxNQUFNdkQsT0FBTzZDLFFBQVEsQ0FBQ1MsZUFBZSxDQUFDQztJQUM5QztJQUVBLGlCQUFpQjtJQUNqQixNQUFNQyxZQUFXRCxTQUFpQjtRQUNqQyxPQUFPLE1BQU12RCxPQUFPNkMsUUFBUSxDQUFDWSxHQUFHLENBQUNGO0lBQ2xDO0lBRUEsMkJBQTJCO0lBQzNCRyx3QkFBdUJDLE9BQXdCLEVBQUVDLFNBQWlCO1FBQ2pFLE9BQU81RCxPQUFPNkQsUUFBUSxDQUFDQyxjQUFjLENBQUNILFNBQVNDLFdBQVc3RCxpREFBWUEsQ0FBQ2dFLGFBQWE7SUFDckY7QUFDRCxFQUFFO0FBRUYsaUVBQWUvRCxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXGxpYlxcc3RyaXBlLXNlcnZlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU3RyaXBlIGZyb20gXCJzdHJpcGVcIjtcbmltcG9ydCB7IGxvZ1N0cmlwZUNvbmZpZ1ZhbGlkYXRpb24sIHN0cmlwZUNvbmZpZyB9IGZyb20gXCIuL3N0cmlwZVwiO1xuXG4vLyBWYWxpZGF0ZSBjb25maWd1cmF0aW9uIG9uIGltcG9ydCBhbmQgbG9nIHJlc3VsdHNcbmxvZ1N0cmlwZUNvbmZpZ1ZhbGlkYXRpb24oKTtcblxuLy8gQ3JlYXRlIFN0cmlwZSBpbnN0YW5jZSBmb3Igc2VydmVyLXNpZGUgb3BlcmF0aW9uc1xuZXhwb3J0IGNvbnN0IHN0cmlwZSA9IG5ldyBTdHJpcGUoc3RyaXBlQ29uZmlnLnNlY3JldEtleSEsIHtcblx0YXBpVmVyc2lvbjogXCIyMDI0LTEyLTE4LmFjYWNpYVwiLFxuXHR0eXBlc2NyaXB0OiB0cnVlLFxufSk7XG5cbi8vIEhlbHBlciBmdW5jdGlvbnMgZm9yIGNvbW1vbiBTdHJpcGUgb3BlcmF0aW9uc1xuZXhwb3J0IGNvbnN0IHN0cmlwZUhlbHBlcnMgPSB7XG5cdC8vIENyZWF0ZSBhIHBheW1lbnQgaW50ZW50IGZvciBjYW1wYWlnbiBiaWxsaW5nXG5cdGFzeW5jIGNyZWF0ZVBheW1lbnRJbnRlbnQoYW1vdW50OiBudW1iZXIsIG1ldGFkYXRhOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+KSB7XG5cdFx0cmV0dXJuIGF3YWl0IHN0cmlwZS5wYXltZW50SW50ZW50cy5jcmVhdGUoe1xuXHRcdFx0YW1vdW50OiBNYXRoLnJvdW5kKGFtb3VudCAqIDEwMCksIC8vIENvbnZlcnQgdG8gY2VudHNcblx0XHRcdGN1cnJlbmN5OiBzdHJpcGVDb25maWcuY3VycmVuY3ksXG5cdFx0XHRtZXRhZGF0YSxcblx0XHRcdGF1dG9tYXRpY19wYXltZW50X21ldGhvZHM6IHtcblx0XHRcdFx0ZW5hYmxlZDogdHJ1ZSxcblx0XHRcdH0sXG5cdFx0fSk7XG5cdH0sXG5cblx0Ly8gQ3JlYXRlIGEgY3VzdG9tZXJcblx0YXN5bmMgY3JlYXRlQ3VzdG9tZXIoZW1haWw6IHN0cmluZywgbmFtZT86IHN0cmluZywgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+KSB7XG5cdFx0cmV0dXJuIGF3YWl0IHN0cmlwZS5jdXN0b21lcnMuY3JlYXRlKHtcblx0XHRcdGVtYWlsLFxuXHRcdFx0bmFtZSxcblx0XHRcdG1ldGFkYXRhLFxuXHRcdH0pO1xuXHR9LFxuXG5cdC8vIFJldHJpZXZlIGEgY3VzdG9tZXJcblx0YXN5bmMgZ2V0Q3VzdG9tZXIoY3VzdG9tZXJJZDogc3RyaW5nKSB7XG5cdFx0cmV0dXJuIGF3YWl0IHN0cmlwZS5jdXN0b21lcnMucmV0cmlldmUoY3VzdG9tZXJJZCk7XG5cdH0sXG5cblx0Ly8gQ3JlYXRlIGEgc2V0dXAgaW50ZW50IGZvciBzYXZpbmcgcGF5bWVudCBtZXRob2RzXG5cdGFzeW5jIGNyZWF0ZVNldHVwSW50ZW50KGN1c3RvbWVySWQ6IHN0cmluZykge1xuXHRcdHJldHVybiBhd2FpdCBzdHJpcGUuc2V0dXBJbnRlbnRzLmNyZWF0ZSh7XG5cdFx0XHRjdXN0b21lcjogY3VzdG9tZXJJZCxcblx0XHRcdHBheW1lbnRfbWV0aG9kX3R5cGVzOiBbXCJjYXJkXCJdLFxuXHRcdH0pO1xuXHR9LFxuXG5cdC8vIExpc3QgY3VzdG9tZXIgcGF5bWVudCBtZXRob2RzXG5cdGFzeW5jIGxpc3RQYXltZW50TWV0aG9kcyhjdXN0b21lcklkOiBzdHJpbmcpIHtcblx0XHRyZXR1cm4gYXdhaXQgc3RyaXBlLnBheW1lbnRNZXRob2RzLmxpc3Qoe1xuXHRcdFx0Y3VzdG9tZXI6IGN1c3RvbWVySWQsXG5cdFx0XHR0eXBlOiBcImNhcmRcIixcblx0XHR9KTtcblx0fSxcblxuXHQvLyBEZXRhY2ggYSBwYXltZW50IG1ldGhvZFxuXHRhc3luYyBkZXRhY2hQYXltZW50TWV0aG9kKHBheW1lbnRNZXRob2RJZDogc3RyaW5nKSB7XG5cdFx0cmV0dXJuIGF3YWl0IHN0cmlwZS5wYXltZW50TWV0aG9kcy5kZXRhY2gocGF5bWVudE1ldGhvZElkKTtcblx0fSxcblxuXHQvLyBDb25maXJtIGEgcGF5bWVudCBpbnRlbnRcblx0YXN5bmMgY29uZmlybVBheW1lbnRJbnRlbnQocGF5bWVudEludGVudElkOiBzdHJpbmcsIHBheW1lbnRNZXRob2RJZD86IHN0cmluZykge1xuXHRcdGNvbnN0IHVwZGF0ZURhdGE6IFN0cmlwZS5QYXltZW50SW50ZW50Q29uZmlybVBhcmFtcyA9IHt9O1xuXG5cdFx0aWYgKHBheW1lbnRNZXRob2RJZCkge1xuXHRcdFx0dXBkYXRlRGF0YS5wYXltZW50X21ldGhvZCA9IHBheW1lbnRNZXRob2RJZDtcblx0XHR9XG5cblx0XHRyZXR1cm4gYXdhaXQgc3RyaXBlLnBheW1lbnRJbnRlbnRzLmNvbmZpcm0ocGF5bWVudEludGVudElkLCB1cGRhdGVEYXRhKTtcblx0fSxcblxuXHQvLyBSZXRyaWV2ZSBwYXltZW50IGludGVudFxuXHRhc3luYyBnZXRQYXltZW50SW50ZW50KHBheW1lbnRJbnRlbnRJZDogc3RyaW5nKSB7XG5cdFx0cmV0dXJuIGF3YWl0IHN0cmlwZS5wYXltZW50SW50ZW50cy5yZXRyaWV2ZShwYXltZW50SW50ZW50SWQpO1xuXHR9LFxuXG5cdC8vIENyZWF0ZSBhbiBpbnZvaWNlIHdpdGggbGluZSBpdGVtc1xuXHRhc3luYyBjcmVhdGVJbnZvaWNlKFxuXHRcdGN1c3RvbWVySWQ6IHN0cmluZyxcblx0XHRvcHRpb25zPzoge1xuXHRcdFx0ZGVzY3JpcHRpb24/OiBzdHJpbmc7XG5cdFx0XHRtZXRhZGF0YT86IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XG5cdFx0XHRsaW5lSXRlbXM/OiBBcnJheTx7XG5cdFx0XHRcdGRlc2NyaXB0aW9uOiBzdHJpbmc7XG5cdFx0XHRcdHF1YW50aXR5PzogbnVtYmVyO1xuXHRcdFx0XHR1bml0QW1vdW50OiBudW1iZXI7IC8vIGluIGNlbnRzXG5cdFx0XHR9Pjtcblx0XHRcdGF1dG9BZHZhbmNlPzogYm9vbGVhbjtcblx0XHR9XG5cdCkge1xuXHRcdGNvbnN0IHsgZGVzY3JpcHRpb24sIG1ldGFkYXRhLCBsaW5lSXRlbXMgPSBbXSwgYXV0b0FkdmFuY2UgPSBmYWxzZSB9ID0gb3B0aW9ucyB8fCB7fTtcblxuXHRcdC8vIENyZWF0ZSBpbnZvaWNlXG5cdFx0Y29uc3QgaW52b2ljZSA9IGF3YWl0IHN0cmlwZS5pbnZvaWNlcy5jcmVhdGUoe1xuXHRcdFx0Y3VzdG9tZXI6IGN1c3RvbWVySWQsXG5cdFx0XHRkZXNjcmlwdGlvbixcblx0XHRcdG1ldGFkYXRhLFxuXHRcdFx0YXV0b19hZHZhbmNlOiBhdXRvQWR2YW5jZSxcblx0XHRcdGNvbGxlY3Rpb25fbWV0aG9kOiBcImNoYXJnZV9hdXRvbWF0aWNhbGx5XCIsXG5cdFx0fSk7XG5cblx0XHQvLyBBZGQgbGluZSBpdGVtcyBpZiBwcm92aWRlZFxuXHRcdGZvciAoY29uc3QgaXRlbSBvZiBsaW5lSXRlbXMpIHtcblx0XHRcdGF3YWl0IHN0cmlwZS5pbnZvaWNlSXRlbXMuY3JlYXRlKHtcblx0XHRcdFx0Y3VzdG9tZXI6IGN1c3RvbWVySWQsXG5cdFx0XHRcdGludm9pY2U6IGludm9pY2UuaWQsXG5cdFx0XHRcdGRlc2NyaXB0aW9uOiBpdGVtLmRlc2NyaXB0aW9uLFxuXHRcdFx0XHRxdWFudGl0eTogaXRlbS5xdWFudGl0eSB8fCAxLFxuXHRcdFx0XHR1bml0X2Ftb3VudDogaXRlbS51bml0QW1vdW50LFxuXHRcdFx0fSk7XG5cdFx0fVxuXG5cdFx0cmV0dXJuIGludm9pY2U7XG5cdH0sXG5cblx0Ly8gRmluYWxpemUgYW4gaW52b2ljZVxuXHRhc3luYyBmaW5hbGl6ZUludm9pY2UoaW52b2ljZUlkOiBzdHJpbmcpIHtcblx0XHRyZXR1cm4gYXdhaXQgc3RyaXBlLmludm9pY2VzLmZpbmFsaXplSW52b2ljZShpbnZvaWNlSWQpO1xuXHR9LFxuXG5cdC8vIFBheSBhbiBpbnZvaWNlXG5cdGFzeW5jIHBheUludm9pY2UoaW52b2ljZUlkOiBzdHJpbmcpIHtcblx0XHRyZXR1cm4gYXdhaXQgc3RyaXBlLmludm9pY2VzLnBheShpbnZvaWNlSWQpO1xuXHR9LFxuXG5cdC8vIFZlcmlmeSB3ZWJob29rIHNpZ25hdHVyZVxuXHR2ZXJpZnlXZWJob29rU2lnbmF0dXJlKHBheWxvYWQ6IHN0cmluZyB8IEJ1ZmZlciwgc2lnbmF0dXJlOiBzdHJpbmcpIHtcblx0XHRyZXR1cm4gc3RyaXBlLndlYmhvb2tzLmNvbnN0cnVjdEV2ZW50KHBheWxvYWQsIHNpZ25hdHVyZSwgc3RyaXBlQ29uZmlnLndlYmhvb2tTZWNyZXQhKTtcblx0fSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHN0cmlwZTtcbiJdLCJuYW1lcyI6WyJTdHJpcGUiLCJsb2dTdHJpcGVDb25maWdWYWxpZGF0aW9uIiwic3RyaXBlQ29uZmlnIiwic3RyaXBlIiwic2VjcmV0S2V5IiwiYXBpVmVyc2lvbiIsInR5cGVzY3JpcHQiLCJzdHJpcGVIZWxwZXJzIiwiY3JlYXRlUGF5bWVudEludGVudCIsImFtb3VudCIsIm1ldGFkYXRhIiwicGF5bWVudEludGVudHMiLCJjcmVhdGUiLCJNYXRoIiwicm91bmQiLCJjdXJyZW5jeSIsImF1dG9tYXRpY19wYXltZW50X21ldGhvZHMiLCJlbmFibGVkIiwiY3JlYXRlQ3VzdG9tZXIiLCJlbWFpbCIsIm5hbWUiLCJjdXN0b21lcnMiLCJnZXRDdXN0b21lciIsImN1c3RvbWVySWQiLCJyZXRyaWV2ZSIsImNyZWF0ZVNldHVwSW50ZW50Iiwic2V0dXBJbnRlbnRzIiwiY3VzdG9tZXIiLCJwYXltZW50X21ldGhvZF90eXBlcyIsImxpc3RQYXltZW50TWV0aG9kcyIsInBheW1lbnRNZXRob2RzIiwibGlzdCIsInR5cGUiLCJkZXRhY2hQYXltZW50TWV0aG9kIiwicGF5bWVudE1ldGhvZElkIiwiZGV0YWNoIiwiY29uZmlybVBheW1lbnRJbnRlbnQiLCJwYXltZW50SW50ZW50SWQiLCJ1cGRhdGVEYXRhIiwicGF5bWVudF9tZXRob2QiLCJjb25maXJtIiwiZ2V0UGF5bWVudEludGVudCIsImNyZWF0ZUludm9pY2UiLCJvcHRpb25zIiwiZGVzY3JpcHRpb24iLCJsaW5lSXRlbXMiLCJhdXRvQWR2YW5jZSIsImludm9pY2UiLCJpbnZvaWNlcyIsImF1dG9fYWR2YW5jZSIsImNvbGxlY3Rpb25fbWV0aG9kIiwiaXRlbSIsImludm9pY2VJdGVtcyIsImlkIiwicXVhbnRpdHkiLCJ1bml0X2Ftb3VudCIsInVuaXRBbW91bnQiLCJmaW5hbGl6ZUludm9pY2UiLCJpbnZvaWNlSWQiLCJwYXlJbnZvaWNlIiwicGF5IiwidmVyaWZ5V2ViaG9va1NpZ25hdHVyZSIsInBheWxvYWQiLCJzaWduYXR1cmUiLCJ3ZWJob29rcyIsImNvbnN0cnVjdEV2ZW50Iiwid2ViaG9va1NlY3JldCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/stripe-server.ts\n");

/***/ }),

/***/ "(rsc)/./lib/stripe.ts":
/*!***********************!*\
  !*** ./lib/stripe.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStripe: () => (/* binding */ getStripe),\n/* harmony export */   getStripeEnvironment: () => (/* binding */ getStripeEnvironment),\n/* harmony export */   logStripeConfigValidation: () => (/* binding */ logStripeConfigValidation),\n/* harmony export */   stripeConfig: () => (/* binding */ stripeConfig),\n/* harmony export */   validateStripeConfig: () => (/* binding */ validateStripeConfig)\n/* harmony export */ });\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"(rsc)/./node_modules/@stripe/stripe-js/lib/index.mjs\");\n\n// Client-side Stripe instance\nlet stripePromise;\n/**\n * Get client-side Stripe instance with validation\n */ const getStripe = ()=>{\n    if (!stripePromise) {\n        const publishableKey = \"pk_test_51Rf69YGafGoP3tL5cFQcLdeEwGZUDJGXbikwsjjRrG5ZeO6UGE1wwIrEktk1ZfXsKmAFy7kem1v4reFu4mAotlIl00r1g0lire\";\n        if (!publishableKey) {\n            throw new Error(\"Missing Stripe publishable key. Please set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY in your environment variables.\");\n        }\n        // Validate publishable key format\n        if (!publishableKey.startsWith(\"pk_\")) {\n            throw new Error('Invalid Stripe publishable key format. Key should start with \"pk_test_\" or \"pk_live_\".');\n        }\n        stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(publishableKey);\n    }\n    return stripePromise;\n};\n/**\n * Get Stripe environment configuration\n */ const getStripeEnvironment = ()=>{\n    const publishableKey = \"pk_test_51Rf69YGafGoP3tL5cFQcLdeEwGZUDJGXbikwsjjRrG5ZeO6UGE1wwIrEktk1ZfXsKmAFy7kem1v4reFu4mAotlIl00r1g0lire\";\n    const secretKey = process.env.STRIPE_SECRET_KEY;\n    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n    const currency = process.env.STRIPE_CURRENCY || \"usd\";\n    const country = process.env.STRIPE_COUNTRY || \"US\";\n    // Determine if we're in test mode\n    const isTestMode = publishableKey?.startsWith(\"pk_test_\") || secretKey?.startsWith(\"sk_test_\") || false;\n    return {\n        publishableKey,\n        secretKey,\n        webhookSecret,\n        currency,\n        country,\n        isTestMode\n    };\n};\n/**\n * Validate Stripe configuration\n */ const validateStripeConfig = ()=>{\n    const errors = [];\n    const warnings = [];\n    const env = getStripeEnvironment();\n    // Required environment variables\n    if (!env.publishableKey) {\n        errors.push(\"NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required\");\n    } else if (!env.publishableKey.startsWith(\"pk_\")) {\n        errors.push('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY must start with \"pk_test_\" or \"pk_live_\"');\n    }\n    if (!env.secretKey) {\n        errors.push(\"STRIPE_SECRET_KEY is required\");\n    } else if (!env.secretKey.startsWith(\"sk_\")) {\n        errors.push('STRIPE_SECRET_KEY must start with \"sk_test_\" or \"sk_live_\"');\n    }\n    if (!env.webhookSecret) {\n        errors.push(\"STRIPE_WEBHOOK_SECRET is required\");\n    } else if (!env.webhookSecret.startsWith(\"whsec_\")) {\n        errors.push('STRIPE_WEBHOOK_SECRET must start with \"whsec_\"');\n    }\n    // Key consistency validation\n    if (env.publishableKey && env.secretKey) {\n        const pubKeyIsTest = env.publishableKey.startsWith(\"pk_test_\");\n        const secretKeyIsTest = env.secretKey.startsWith(\"sk_test_\");\n        if (pubKeyIsTest !== secretKeyIsTest) {\n            errors.push(\"Stripe publishable key and secret key must both be test keys or both be live keys\");\n        }\n    }\n    // Currency validation\n    const validCurrencies = [\n        \"usd\",\n        \"eur\",\n        \"gbp\",\n        \"cad\",\n        \"aud\",\n        \"jpy\"\n    ];\n    if (!validCurrencies.includes(env.currency.toLowerCase())) {\n        warnings.push(`Currency \"${env.currency}\" may not be supported. Common currencies: ${validCurrencies.join(\", \")}`);\n    }\n    // Environment warnings\n    if (env.isTestMode && \"development\" === \"production\") {}\n    if (!env.isTestMode && \"development\" === \"development\") {\n        warnings.push(\"Using Stripe live keys in development environment\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        warnings\n    };\n};\n// Server-side Stripe configuration\nconst stripeConfig = {\n    secretKey: process.env.STRIPE_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,\n    currency: process.env.STRIPE_CURRENCY || \"usd\",\n    country: process.env.STRIPE_COUNTRY || \"US\"\n};\n/**\n * Log configuration validation results\n */ const logStripeConfigValidation = ()=>{\n    const validation = validateStripeConfig();\n    if (!validation.isValid) {\n        console.error(\"❌ Stripe Configuration Errors:\");\n        validation.errors.forEach((error)=>console.error(`  - ${error}`));\n    }\n    if (validation.warnings.length > 0) {\n        console.warn(\"⚠️  Stripe Configuration Warnings:\");\n        validation.warnings.forEach((warning)=>console.warn(`  - ${warning}`));\n    }\n    if (validation.isValid && validation.warnings.length === 0) {\n        console.log(\"✅ Stripe configuration is valid\");\n    }\n    return validation;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/stripe.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/stripe/create-checkout-session/route.ts */ \"(rsc)/./app/api/stripe/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/create-checkout-session/route\",\n        pathname: \"/api/stripe/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\api\\\\stripe\\\\create-checkout-session\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdHJpcGUlMkZjcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbiUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGc3RyaXBlJTJGY3JlYXRlLWNoZWNrb3V0LXNlc3Npb24lMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZzdHJpcGUlMkZjcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbiUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiZW5qaSU1Q0Rlc2t0b3AlNUNHX1BST0clNUNOaWNvbGFzJTVDYnRkYXNoLWVjb3N5c3RlbSU1Q2J0ZGFzaC1hZG1hbmFnZXIlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2JlbmppJTVDRGVza3RvcCU1Q0dfUFJPRyU1Q05pY29sYXMlNUNidGRhc2gtZWNvc3lzdGVtJTVDYnRkYXNoLWFkbWFuYWdlciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDbUY7QUFDaEs7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGJlbmppXFxcXERlc2t0b3BcXFxcR19QUk9HXFxcXE5pY29sYXNcXFxcYnRkYXNoLWVjb3N5c3RlbVxcXFxidGRhc2gtYWRtYW5hZ2VyXFxcXGFwcFxcXFxhcGlcXFxcc3RyaXBlXFxcXGNyZWF0ZS1jaGVja291dC1zZXNzaW9uXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zdHJpcGUvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb24vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zdHJpcGUvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb25cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N0cmlwZS9jcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbi9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGJlbmppXFxcXERlc2t0b3BcXFxcR19QUk9HXFxcXE5pY29sYXNcXFxcYnRkYXNoLWVjb3N5c3RlbVxcXFxidGRhc2gtYWRtYW5hZ2VyXFxcXGFwcFxcXFxhcGlcXFxcc3RyaXBlXFxcXGNyZWF0ZS1jaGVja291dC1zZXNzaW9uXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Chooks%5C%5Cuse-user.js%22%2C%22ids%22%3A%5B%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cproviders%5C%5Cauth0-provider.js%22%2C%22ids%22%3A%5B%22Auth0Provider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Chooks%5C%5Cuse-user.js%22%2C%22ids%22%3A%5B%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cproviders%5C%5Cauth0-provider.js%22%2C%22ids%22%3A%5B%22Auth0Provider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JlbmppJTVDJTVDRGVza3RvcCU1QyU1Q0dfUFJPRyU1QyU1Q05pY29sYXMlNUMlNUNidGRhc2gtZWNvc3lzdGVtJTVDJTVDYnRkYXNoLWFkbWFuYWdlciU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGF1dGgwJTVDJTVDbmV4dGpzLWF1dGgwJTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2hvb2tzJTVDJTVDdXNlLXVzZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJ1c2VVc2VyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JlbmppJTVDJTVDRGVza3RvcCU1QyU1Q0dfUFJPRyU1QyU1Q05pY29sYXMlNUMlNUNidGRhc2gtZWNvc3lzdGVtJTVDJTVDYnRkYXNoLWFkbWFuYWdlciU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGF1dGgwJTVDJTVDbmV4dGpzLWF1dGgwJTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q3Byb3ZpZGVycyU1QyU1Q2F1dGgwLXByb3ZpZGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aDBQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE9BQXNOO0FBQ3ROO0FBQ0EsZ1FBQXNPIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJ1c2VVc2VyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcYmVuamlcXFxcRGVza3RvcFxcXFxHX1BST0dcXFxcTmljb2xhc1xcXFxidGRhc2gtZWNvc3lzdGVtXFxcXGJ0ZGFzaC1hZG1hbmFnZXJcXFxcbm9kZV9tb2R1bGVzXFxcXEBhdXRoMFxcXFxuZXh0anMtYXV0aDBcXFxcZGlzdFxcXFxjbGllbnRcXFxcaG9va3NcXFxcdXNlLXVzZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGgwUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiZW5qaVxcXFxEZXNrdG9wXFxcXEdfUFJPR1xcXFxOaWNvbGFzXFxcXGJ0ZGFzaC1lY29zeXN0ZW1cXFxcYnRkYXNoLWFkbWFuYWdlclxcXFxub2RlX21vZHVsZXNcXFxcQGF1dGgwXFxcXG5leHRqcy1hdXRoMFxcXFxkaXN0XFxcXGNsaWVudFxcXFxwcm92aWRlcnNcXFxcYXV0aDAtcHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Chooks%5C%5Cuse-user.js%22%2C%22ids%22%3A%5B%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cproviders%5C%5Cauth0-provider.js%22%2C%22ids%22%3A%5B%22Auth0Provider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Chooks%5C%5Cuse-user.js%22%2C%22ids%22%3A%5B%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cproviders%5C%5Cauth0-provider.js%22%2C%22ids%22%3A%5B%22Auth0Provider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Chooks%5C%5Cuse-user.js%22%2C%22ids%22%3A%5B%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cproviders%5C%5Cauth0-provider.js%22%2C%22ids%22%3A%5B%22Auth0Provider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JlbmppJTVDJTVDRGVza3RvcCU1QyU1Q0dfUFJPRyU1QyU1Q05pY29sYXMlNUMlNUNidGRhc2gtZWNvc3lzdGVtJTVDJTVDYnRkYXNoLWFkbWFuYWdlciU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGF1dGgwJTVDJTVDbmV4dGpzLWF1dGgwJTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2hvb2tzJTVDJTVDdXNlLXVzZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJ1c2VVc2VyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JlbmppJTVDJTVDRGVza3RvcCU1QyU1Q0dfUFJPRyU1QyU1Q05pY29sYXMlNUMlNUNidGRhc2gtZWNvc3lzdGVtJTVDJTVDYnRkYXNoLWFkbWFuYWdlciU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGF1dGgwJTVDJTVDbmV4dGpzLWF1dGgwJTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q3Byb3ZpZGVycyU1QyU1Q2F1dGgwLXByb3ZpZGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aDBQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE9BQXNOO0FBQ3ROO0FBQ0EsZ1FBQXNPIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJ1c2VVc2VyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcYmVuamlcXFxcRGVza3RvcFxcXFxHX1BST0dcXFxcTmljb2xhc1xcXFxidGRhc2gtZWNvc3lzdGVtXFxcXGJ0ZGFzaC1hZG1hbmFnZXJcXFxcbm9kZV9tb2R1bGVzXFxcXEBhdXRoMFxcXFxuZXh0anMtYXV0aDBcXFxcZGlzdFxcXFxjbGllbnRcXFxcaG9va3NcXFxcdXNlLXVzZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGgwUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiZW5qaVxcXFxEZXNrdG9wXFxcXEdfUFJPR1xcXFxOaWNvbGFzXFxcXGJ0ZGFzaC1lY29zeXN0ZW1cXFxcYnRkYXNoLWFkbWFuYWdlclxcXFxub2RlX21vZHVsZXNcXFxcQGF1dGgwXFxcXG5leHRqcy1hdXRoMFxcXFxkaXN0XFxcXGNsaWVudFxcXFxwcm92aWRlcnNcXFxcYXV0aDAtcHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Chooks%5C%5Cuse-user.js%22%2C%22ids%22%3A%5B%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cproviders%5C%5Cauth0-provider.js%22%2C%22ids%22%3A%5B%22Auth0Provider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/swr","vendor-chunks/use-sync-external-store","vendor-chunks/dequal","vendor-chunks/@stripe","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();