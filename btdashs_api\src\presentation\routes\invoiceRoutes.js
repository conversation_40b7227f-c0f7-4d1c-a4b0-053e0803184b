const express = require("express");
const router = express.Router();
const { checkJwt, checkInternalKey } = require("../../middleware/auth");
const checkAdminAuth = require("../../middleware/adminAuth");
const rateLimits = require("../../middleware/rateLimitMiddleware");
const invoiceController = require("../controllers/invoiceController");

// User invoice endpoints (require authentication)
router.get("/", checkJwt, rateLimits.userGeneral, invoiceController.getUserInvoices);
router.get("/:id", checkJwt, rateLimits.userGeneral, invoiceController.getInvoiceDetails);
router.get("/:id/pdf", checkJwt, rateLimits.userGeneral, invoiceController.generateInvoicePDF);
router.get("/:id/download", checkJwt, rateLimits.userGeneral, invoiceController.downloadInvoicePDF);

// Internal invoice management (require internal API key)
router.post("/create", checkInternalKey, rateLimits.ipStrict, invoiceController.createCampaignInvoice);
router.post("/:id/finalize", checkInternalKey, rateLimits.ipStrict, invoiceController.finalizeInvoice);
router.post("/mark-paid", checkInternalKey, rateLimits.ipStrict, invoiceController.markInvoicePaid);
router.post("/associate-payment", checkInternalKey, rateLimits.ipStrict, invoiceController.associatePaymentIntent);

// Admin invoice endpoints
router.get("/admin/stats", checkJwt, checkAdminAuth, rateLimits.adminGeneral, invoiceController.getInvoiceStats);

module.exports = router;
