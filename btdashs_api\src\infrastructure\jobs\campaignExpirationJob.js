const cron = require("node-cron");
const CampaignExpirationService = require("../../application/services/CampaignExpirationService");
const logger = require("../logging/logger");

/**
 * Scheduled job for campaign expiration and activation
 */
class CampaignExpirationJob {
	constructor() {
		this.campaignService = new CampaignExpirationService();
		this.isRunning = false;
	}

	/**
	 * Start the scheduled jobs
	 */
	start() {
		// Run every hour to check for expired campaigns
		cron.schedule("0 * * * *", async () => {
			if (this.isRunning) {
				logger.warn("Campaign expiration job already running, skipping");
				return;
			}

			this.isRunning = true;
			try {
				logger.info("Starting campaign expiration check");
				const result = await this.campaignService.processExpiredCampaigns();
				
				if (result.processed > 0) {
					logger.info("Campaign expiration check completed", {
						processed: result.processed,
						expired: result.expired.length,
						errors: result.errors.length
					});

					// Send notifications if campaigns were expired
					if (result.expired.length > 0) {
						await this.sendExpirationNotifications(result.expired);
					}
				}
			} catch (error) {
				logger.error("Campaign expiration job failed", { error: error.message });
			} finally {
				this.isRunning = false;
			}
		});

		// Run every day at 6 AM to activate campaigns
		cron.schedule("0 6 * * *", async () => {
			try {
				logger.info("Starting campaign activation check");
				const result = await this.campaignService.processStartingCampaigns();
				
				if (result.processed > 0) {
					logger.info("Campaign activation check completed", {
						processed: result.processed,
						activated: result.activated.length,
						errors: result.errors.length
					});

					// Send notifications if campaigns were activated
					if (result.activated.length > 0) {
						await this.sendActivationNotifications(result.activated);
					}
				}
			} catch (error) {
				logger.error("Campaign activation job failed", { error: error.message });
			}
		});

		// Run daily at 9 AM to check for campaigns expiring soon
		cron.schedule("0 9 * * *", async () => {
			try {
				logger.info("Checking for campaigns expiring soon");
				const expiringSoon = await this.campaignService.getCampaignsExpiringSoon(3); // 3 days
				
				if (expiringSoon.length > 0) {
					logger.info("Found campaigns expiring soon", { count: expiringSoon.length });
					await this.sendExpirationWarnings(expiringSoon);
				}
			} catch (error) {
				logger.error("Campaign expiration warning job failed", { error: error.message });
			}
		});

		logger.info("Campaign expiration jobs started");
	}

	/**
	 * Send notifications for expired campaigns
	 * @param {Array} expiredCampaigns - List of expired campaigns
	 */
	async sendExpirationNotifications(expiredCampaigns) {
		try {
			// Group campaigns by advertiser
			const campaignsByAdvertiser = {};
			
			for (const campaign of expiredCampaigns) {
				if (!campaignsByAdvertiser[campaign.advertiser_id]) {
					campaignsByAdvertiser[campaign.advertiser_id] = [];
				}
				campaignsByAdvertiser[campaign.advertiser_id].push(campaign);
			}

			// Send notifications to each advertiser
			for (const [advertiserId, campaigns] of Object.entries(campaignsByAdvertiser)) {
				await this.sendAdvertiserNotification(advertiserId, {
					type: "campaigns_expired",
					campaigns: campaigns,
					message: `${campaigns.length} of your campaigns have expired and been completed.`
				});
			}

			// Send admin notification
			await this.sendAdminNotification({
				type: "campaigns_expired",
				count: expiredCampaigns.length,
				campaigns: expiredCampaigns,
				message: `${expiredCampaigns.length} campaigns have been automatically expired.`
			});

		} catch (error) {
			logger.error("Failed to send expiration notifications", { error: error.message });
		}
	}

	/**
	 * Send notifications for activated campaigns
	 * @param {Array} activatedCampaigns - List of activated campaigns
	 */
	async sendActivationNotifications(activatedCampaigns) {
		try {
			// Group campaigns by advertiser
			const campaignsByAdvertiser = {};
			
			for (const campaign of activatedCampaigns) {
				if (!campaignsByAdvertiser[campaign.advertiser_id]) {
					campaignsByAdvertiser[campaign.advertiser_id] = [];
				}
				campaignsByAdvertiser[campaign.advertiser_id].push(campaign);
			}

			// Send notifications to each advertiser
			for (const [advertiserId, campaigns] of Object.entries(campaignsByAdvertiser)) {
				await this.sendAdvertiserNotification(advertiserId, {
					type: "campaigns_activated",
					campaigns: campaigns,
					message: `${campaigns.length} of your campaigns have been activated and are now live.`
				});
			}

		} catch (error) {
			logger.error("Failed to send activation notifications", { error: error.message });
		}
	}

	/**
	 * Send warnings for campaigns expiring soon
	 * @param {Array} expiringSoon - List of campaigns expiring soon
	 */
	async sendExpirationWarnings(expiringSoon) {
		try {
			// Group campaigns by advertiser
			const campaignsByAdvertiser = {};
			
			for (const campaign of expiringSoon) {
				if (!campaignsByAdvertiser[campaign.advertiser_id]) {
					campaignsByAdvertiser[campaign.advertiser_id] = [];
				}
				campaignsByAdvertiser[campaign.advertiser_id].push(campaign);
			}

			// Send warnings to each advertiser
			for (const [advertiserId, campaigns] of Object.entries(campaignsByAdvertiser)) {
				await this.sendAdvertiserNotification(advertiserId, {
					type: "campaigns_expiring_soon",
					campaigns: campaigns,
					message: `${campaigns.length} of your campaigns will expire within the next 3 days.`
				});
			}

		} catch (error) {
			logger.error("Failed to send expiration warnings", { error: error.message });
		}
	}

	/**
	 * Send notification to advertiser
	 * @param {number} advertiserId - Advertiser ID
	 * @param {Object} notification - Notification data
	 */
	async sendAdvertiserNotification(advertiserId, notification) {
		try {
			// In a real implementation, you would:
			// 1. Get advertiser email from database
			// 2. Send email notification
			// 3. Create in-app notification
			// 4. Log the notification

			logger.info("Advertiser notification", {
				advertiserId,
				type: notification.type,
				campaignCount: notification.campaigns.length
			});

			// Example: Store notification in database
			const db = require("../database/knex");
			
			// Check if notifications table exists
			const tableExists = await db.schema.hasTable("dtm_ads.notifications");
			
			if (tableExists) {
				await db("dtm_ads.notifications").insert({
					user_id: advertiserId,
					type: notification.type,
					title: this.getNotificationTitle(notification.type),
					message: notification.message,
					data: JSON.stringify(notification.campaigns),
					is_read: false,
					created_at: new Date()
				});
			}

		} catch (error) {
			logger.error("Failed to send advertiser notification", {
				advertiserId,
				error: error.message
			});
		}
	}

	/**
	 * Send notification to admins
	 * @param {Object} notification - Notification data
	 */
	async sendAdminNotification(notification) {
		try {
			logger.info("Admin notification", {
				type: notification.type,
				count: notification.count
			});

			// Example: Send to admin webhook or email
			if (process.env.ADMIN_WEBHOOK_URL) {
				await fetch(process.env.ADMIN_WEBHOOK_URL, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${process.env.ADMIN_WEBHOOK_TOKEN}`
					},
					body: JSON.stringify({
						source: 'btdash-campaign-expiration',
						notification
					})
				});
			}

		} catch (error) {
			logger.error("Failed to send admin notification", { error: error.message });
		}
	}

	/**
	 * Get notification title based on type
	 * @param {string} type - Notification type
	 * @returns {string} Notification title
	 */
	getNotificationTitle(type) {
		switch (type) {
			case "campaigns_expired":
				return "Campaigns Expired";
			case "campaigns_activated":
				return "Campaigns Activated";
			case "campaigns_expiring_soon":
				return "Campaigns Expiring Soon";
			default:
				return "Campaign Update";
		}
	}

	/**
	 * Stop the scheduled jobs
	 */
	stop() {
		// Note: node-cron doesn't provide a direct way to stop specific tasks
		// In a production environment, you might want to use a more robust scheduler
		logger.info("Campaign expiration jobs stopped");
	}
}

module.exports = CampaignExpirationJob;
