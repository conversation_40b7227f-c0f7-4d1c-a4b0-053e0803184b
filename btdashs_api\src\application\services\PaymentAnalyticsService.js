const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class PaymentAnalyticsService {
	/**
	 * Record payment failure
	 * @param {Object} failureData - Payment failure data
	 */
	async recordPaymentFailure(failureData) {
		try {
			const {
				paymentIntentId,
				campaignId,
				userId,
				failureCode,
				failureMessage,
				amount,
				currency,
				paymentMethod,
				metadata = {},
			} = failureData;

			await db("dtm_ads.payment_failures").insert({
				payment_intent_id: paymentIntentId,
				campaign_id: campaignId,
				user_id: userId,
				failure_code: failureCode,
				failure_reason: failureMessage,
				amount: amount,
				currency: currency || 'USD',
				payment_method: paymentMethod,
				metadata: JSON.stringify(metadata),
				created_at: new Date(),
			});

			logger.warn("Payment failure recorded", {
				paymentIntentId,
				campaignId,
				userId,
				failureCode,
				failureMessage,
				amount,
			});
		} catch (error) {
			logger.error("Failed to record payment failure", {
				error: error.message,
				failureData,
			});
		}
	}

	/**
	 * Get payment failure analytics
	 * @param {Object} options - Query options
	 * @returns {Promise<Object>} Failure analytics
	 */
	async getFailureAnalytics(options = {}) {
		const { startDate, endDate, userId, campaignId } = options;

		let query = db("dtm_ads.payment_failures");

		if (startDate && endDate) {
			query = query.whereBetween("created_at", [startDate, endDate]);
		}

		if (userId) {
			query = query.where({ user_id: userId });
		}

		if (campaignId) {
			query = query.where({ campaign_id: campaignId });
		}

		const [totalFailures, failuresByCode, failuresByMethod, recentFailures] = await Promise.all([
			// Total failures and amounts
			query.clone()
				.select(
					db.raw("COUNT(*) as total_failures"),
					db.raw("SUM(amount) as total_failed_amount"),
					db.raw("AVG(amount) as average_failed_amount")
				)
				.first(),

			// Failures by code
			query.clone()
				.select("failure_code")
				.count("* as count")
				.sum("amount as total_amount")
				.groupBy("failure_code")
				.orderBy("count", "desc"),

			// Failures by payment method
			query.clone()
				.select("payment_method")
				.count("* as count")
				.sum("amount as total_amount")
				.groupBy("payment_method")
				.orderBy("count", "desc"),

			// Recent failures
			query.clone()
				.orderBy("created_at", "desc")
				.limit(10)
				.select("*"),
		]);

		return {
			summary: totalFailures,
			by_failure_code: failuresByCode,
			by_payment_method: failuresByMethod,
			recent_failures: recentFailures,
		};
	}

	/**
	 * Get payment success analytics
	 * @param {Object} options - Query options
	 * @returns {Promise<Object>} Success analytics
	 */
	async getSuccessAnalytics(options = {}) {
		const { startDate, endDate, userId, campaignId } = options;

		let query = db("dtm_ads.billing_transactions")
			.where({ status: 'completed' });

		if (startDate && endDate) {
			query = query.whereBetween("created_at", [startDate, endDate]);
		}

		if (userId) {
			query = query.where({ user_id: userId });
		}

		if (campaignId) {
			query = query.where({ campaign_id: campaignId });
		}

		const [totalSuccess, successByMethod, dailyTrends] = await Promise.all([
			// Total successful payments
			query.clone()
				.select(
					db.raw("COUNT(*) as total_payments"),
					db.raw("SUM(amount) as total_amount"),
					db.raw("AVG(amount) as average_amount")
				)
				.first(),

			// Success by payment method
			query.clone()
				.select("payment_method")
				.count("* as count")
				.sum("amount as total_amount")
				.groupBy("payment_method")
				.orderBy("count", "desc"),

			// Daily payment trends (last 30 days)
			query.clone()
				.select(
					db.raw("DATE(created_at) as date"),
					db.raw("COUNT(*) as payment_count"),
					db.raw("SUM(amount) as daily_amount")
				)
				.where("created_at", ">=", db.raw("CURRENT_DATE - INTERVAL '30 days'"))
				.groupBy(db.raw("DATE(created_at)"))
				.orderBy("date", "desc"),
		]);

		return {
			summary: totalSuccess,
			by_payment_method: successByMethod,
			daily_trends: dailyTrends,
		};
	}

	/**
	 * Get comprehensive payment analytics
	 * @param {Object} options - Query options
	 * @returns {Promise<Object>} Complete analytics
	 */
	async getPaymentAnalytics(options = {}) {
		const [successAnalytics, failureAnalytics, webhookStats] = await Promise.all([
			this.getSuccessAnalytics(options),
			this.getFailureAnalytics(options),
			this.getWebhookAnalytics(options),
		]);

		const totalAttempts = successAnalytics.summary.total_payments + failureAnalytics.summary.total_failures;
		const successRate = totalAttempts > 0 
			? (successAnalytics.summary.total_payments / totalAttempts * 100).toFixed(2)
			: 0;

		return {
			overview: {
				total_payment_attempts: totalAttempts,
				successful_payments: successAnalytics.summary.total_payments,
				failed_payments: failureAnalytics.summary.total_failures,
				success_rate: `${successRate}%`,
				total_revenue: successAnalytics.summary.total_amount || 0,
				total_failed_amount: failureAnalytics.summary.total_failed_amount || 0,
			},
			success: successAnalytics,
			failures: failureAnalytics,
			webhooks: webhookStats,
		};
	}

	/**
	 * Get webhook processing analytics
	 * @param {Object} options - Query options
	 * @returns {Promise<Object>} Webhook analytics
	 */
	async getWebhookAnalytics(options = {}) {
		const { startDate, endDate } = options;

		let query = db("dtm_ads.webhook_logs");

		if (startDate && endDate) {
			query = query.whereBetween("created_at", [startDate, endDate]);
		}

		const [webhookStats, eventTypeStats] = await Promise.all([
			// Overall webhook stats
			query.clone()
				.select(
					db.raw("COUNT(*) as total_webhooks"),
					db.raw("COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_webhooks"),
					db.raw("COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_webhooks"),
					db.raw("AVG(attempts) as average_attempts")
				)
				.first(),

			// Stats by event type
			query.clone()
				.select("event_type")
				.count("* as count")
				.countDistinct("event_id as unique_events")
				.avg("attempts as avg_attempts")
				.groupBy("event_type")
				.orderBy("count", "desc"),
		]);

		const successRate = webhookStats.total_webhooks > 0
			? (webhookStats.successful_webhooks / webhookStats.total_webhooks * 100).toFixed(2)
			: 0;

		return {
			summary: {
				...webhookStats,
				success_rate: `${successRate}%`,
			},
			by_event_type: eventTypeStats,
		};
	}

	/**
	 * Get user payment patterns
	 * @param {number} userId - User ID
	 * @returns {Promise<Object>} User payment patterns
	 */
	async getUserPaymentPatterns(userId) {
		const [paymentHistory, failureHistory, preferredMethods] = await Promise.all([
			// Payment history
			db("dtm_ads.billing_transactions")
				.where({ user_id: userId, status: 'completed' })
				.orderBy("created_at", "desc")
				.limit(20)
				.select("*"),

			// Failure history
			db("dtm_ads.payment_failures")
				.where({ user_id: userId })
				.orderBy("created_at", "desc")
				.limit(10)
				.select("*"),

			// Preferred payment methods
			db("dtm_ads.billing_transactions")
				.where({ user_id: userId, status: 'completed' })
				.select("payment_method")
				.count("* as usage_count")
				.sum("amount as total_amount")
				.groupBy("payment_method")
				.orderBy("usage_count", "desc"),
		]);

		const totalPayments = paymentHistory.length;
		const totalFailures = failureHistory.length;
		const successRate = totalPayments + totalFailures > 0
			? (totalPayments / (totalPayments + totalFailures) * 100).toFixed(2)
			: 0;

		return {
			summary: {
				total_successful_payments: totalPayments,
				total_failed_payments: totalFailures,
				success_rate: `${successRate}%`,
				total_spent: paymentHistory.reduce((sum, payment) => sum + parseFloat(payment.amount), 0),
			},
			payment_history: paymentHistory,
			failure_history: failureHistory,
			preferred_methods: preferredMethods,
		};
	}

	/**
	 * Generate payment health report
	 * @param {Object} options - Report options
	 * @returns {Promise<Object>} Health report
	 */
	async generateHealthReport(options = {}) {
		const analytics = await this.getPaymentAnalytics(options);
		const health = {
			status: 'healthy',
			issues: [],
			recommendations: [],
		};

		// Check success rate
		const successRate = parseFloat(analytics.overview.success_rate);
		if (successRate < 95) {
			health.status = 'warning';
			health.issues.push(`Low payment success rate: ${successRate}%`);
			health.recommendations.push('Investigate common failure reasons and improve payment flow');
		}

		// Check webhook processing
		const webhookSuccessRate = parseFloat(analytics.webhooks.summary.success_rate);
		if (webhookSuccessRate < 98) {
			health.status = 'warning';
			health.issues.push(`Low webhook success rate: ${webhookSuccessRate}%`);
			health.recommendations.push('Review webhook retry logic and error handling');
		}

		// Check for high failure amounts
		const failedAmount = analytics.overview.total_failed_amount || 0;
		const totalRevenue = analytics.overview.total_revenue || 0;
		if (failedAmount > totalRevenue * 0.1) {
			health.status = 'critical';
			health.issues.push(`High failed payment amount: $${failedAmount}`);
			health.recommendations.push('Urgent review of payment failures required');
		}

		return {
			...analytics,
			health,
		};
	}
}

module.exports = PaymentAnalyticsService;
