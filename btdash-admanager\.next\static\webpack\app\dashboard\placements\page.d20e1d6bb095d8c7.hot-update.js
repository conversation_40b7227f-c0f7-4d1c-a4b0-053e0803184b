"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/placements/page",{

/***/ "(app-pages-browser)/./app/dashboard/placements/client-wrapper.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/placements/client-wrapper.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlacementsClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n// app/dashboard/placements/client-wrapper.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction PlacementsClientWrapper(param) {\n    let { placements } = param;\n    const homePlacements = placements.filter((p)=>p.page === \"home\");\n    const newsletterPlacements = placements.filter((p)=>p.page === \"newsletter\");\n    const subnetsPlacements = placements.filter((p)=>p.page === \"subnets\");\n    const companiesPlacements = placements.filter((p)=>p.page === \"companies\");\n    const renderPlacementCard = (placement)=>{\n        var _placement_allowed_ad_types;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    children: placement.page === \"home\" ? \"home\" : placement.page === \"subnets\" ? \"Subnets\" : placement.page === \"companies\" ? \"Companies\" : \"Newsletter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 6\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    variant: \"outline\",\n                                    children: [\n                                        placement.width,\n                                        \"x\",\n                                        placement.height\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 6\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 5\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"mt-2\",\n                            children: placement.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 5\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: placement.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 5\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 4\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video overflow-hidden rounded-lg bg-muted\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                src: \"/placeholder.svg?text=\".concat(placement.width, \"x\").concat(placement.height),\n                                alt: placement.name,\n                                width: placement.width,\n                                height: placement.height,\n                                className: \"object-contain w-full h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 6\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 5\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Dimensions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 7\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                placement.width,\n                                                \"x\",\n                                                placement.height,\n                                                \"px\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 7\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 6\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"justify-self-end text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 7\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: ((_placement_allowed_ad_types = placement.allowed_ad_types) === null || _placement_allowed_ad_types === void 0 ? void 0 : _placement_allowed_ad_types.join(\", \")) || \"Image/HTML\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 7\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 6\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 5\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 4\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/dashboard/campaigns/create?placement=\".concat(placement.id),\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            className: \"w-full\",\n                            children: \"Select Placement\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 5\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 4\n                }, this)\n            ]\n        }, placement.id, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n            lineNumber: 19,\n            columnNumber: 3\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n        defaultValue: \"all\",\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                        value: \"all\",\n                        children: \"All\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                        value: \"home\",\n                        children: \"Home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                        value: \"subnets\",\n                        children: \"Subnets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                        value: \"companies\",\n                        children: \"Companies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                lineNumber: 73,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                value: \"all\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                    children: placements.map(renderPlacementCard)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                lineNumber: 80,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                value: \"home\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                    children: homePlacements.map(renderPlacementCard)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                lineNumber: 84,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                value: \"subnets\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                    children: subnetsPlacements.map(renderPlacementCard)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                lineNumber: 90,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                value: \"companies\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                    children: companiesPlacements.map(renderPlacementCard)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n                lineNumber: 96,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\placements\\\\client-wrapper.tsx\",\n        lineNumber: 72,\n        columnNumber: 3\n    }, this);\n}\n_c = PlacementsClientWrapper;\nvar _c;\n$RefreshReg$(_c, \"PlacementsClientWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvcGxhY2VtZW50cy9jbGllbnQtd3JhcHBlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsOENBQThDOztBQUdBO0FBQ0U7QUFDNkQ7QUFDN0I7QUFFakQ7QUFDRjtBQUVkLFNBQVNjLHdCQUF3QixLQUF3QztRQUF4QyxFQUFFQyxVQUFVLEVBQTRCLEdBQXhDO0lBQy9DLE1BQU1DLGlCQUFpQkQsV0FBV0UsTUFBTSxDQUFDLENBQUNDLElBQU1BLEVBQUVDLElBQUksS0FBSztJQUMzRCxNQUFNQyx1QkFBdUJMLFdBQVdFLE1BQU0sQ0FBQyxDQUFDQyxJQUFNQSxFQUFFQyxJQUFJLEtBQUs7SUFDakUsTUFBTUUsb0JBQW9CTixXQUFXRSxNQUFNLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRUMsSUFBSSxLQUFLO0lBQzlELE1BQU1HLHNCQUFzQlAsV0FBV0UsTUFBTSxDQUFDLENBQUNDLElBQU1BLEVBQUVDLElBQUksS0FBSztJQUVoRSxNQUFNSSxzQkFBc0IsQ0FBQ0M7WUF3Q3RCQTs2QkF2Q04sOERBQUN0QixxREFBSUE7OzhCQUNKLDhEQUFDSSwyREFBVUE7b0JBQUNtQixXQUFVOztzQ0FDckIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDZCw4REFBQ3pCLHVEQUFLQTs4Q0FDSndCLFVBQVVMLElBQUksS0FBSyxTQUNqQixTQUNBSyxVQUFVTCxJQUFJLEtBQUssWUFDbkIsWUFDQUssVUFBVUwsSUFBSSxLQUFLLGNBQ25CLGNBQ0E7Ozs7Ozs4Q0FFSiw4REFBQ25CLHVEQUFLQTtvQ0FBQzJCLFNBQVE7O3dDQUNiSCxVQUFVSSxLQUFLO3dDQUFDO3dDQUFFSixVQUFVSyxNQUFNOzs7Ozs7Ozs7Ozs7O3NDQUdyQyw4REFBQ3RCLDBEQUFTQTs0QkFBQ2tCLFdBQVU7c0NBQVFELFVBQVVNLElBQUk7Ozs7OztzQ0FDM0MsOERBQUMxQixnRUFBZUE7c0NBQUVvQixVQUFVTyxXQUFXOzs7Ozs7Ozs7Ozs7OEJBRXhDLDhEQUFDNUIsNERBQVdBO29CQUFDc0IsV0FBVTs7c0NBQ3RCLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDZCw0RUFBQ2Isa0RBQUtBO2dDQUNMb0IsS0FBSyx5QkFBNENSLE9BQW5CQSxVQUFVSSxLQUFLLEVBQUMsS0FBb0IsT0FBakJKLFVBQVVLLE1BQU07Z0NBQ2pFSSxLQUFLVCxVQUFVTSxJQUFJO2dDQUNuQkYsT0FBT0osVUFBVUksS0FBSztnQ0FDdEJDLFFBQVFMLFVBQVVLLE1BQU07Z0NBQ3hCSixXQUFVOzs7Ozs7Ozs7OztzQ0FHWiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNkLDhEQUFDQzs7c0RBQ0EsOERBQUNSOzRDQUFFTyxXQUFVO3NEQUFzQjs7Ozs7O3NEQUNuQyw4REFBQ1A7NENBQUVPLFdBQVU7O2dEQUNYRCxVQUFVSSxLQUFLO2dEQUFDO2dEQUFFSixVQUFVSyxNQUFNO2dEQUFDOzs7Ozs7Ozs7Ozs7OzhDQUd0Qyw4REFBQ0g7b0NBQUlELFdBQVU7O3NEQUNkLDhEQUFDUDs0Q0FBRU8sV0FBVTtzREFBc0I7Ozs7OztzREFDbkMsOERBQUNQOzRDQUFFTyxXQUFVO3NEQUNYRCxFQUFBQSw4QkFBQUEsVUFBVVUsZ0JBQWdCLGNBQTFCVixrREFBQUEsNEJBQTRCVyxJQUFJLENBQUMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUsvQyw4REFBQzlCLDJEQUFVQTs4QkFDViw0RUFBQ1Esa0RBQUlBO3dCQUFDdUIsTUFBTSx5Q0FBc0QsT0FBYlosVUFBVWEsRUFBRTt3QkFBSVosV0FBVTtrQ0FDOUUsNEVBQUN4Qix5REFBTUE7NEJBQUN3QixXQUFVO3NDQUFTOzs7Ozs7Ozs7Ozs7Ozs7OztXQTlDbkJELFVBQVVhLEVBQUU7Ozs7OztJQW9EeEIscUJBQ0MsOERBQUM3QixxREFBSUE7UUFBQzhCLGNBQWE7UUFBTWIsV0FBVTs7MEJBQ2xDLDhEQUFDZix5REFBUUE7O2tDQUNSLDhEQUFDQyw0REFBV0E7d0JBQUM0QixPQUFNO2tDQUFNOzs7Ozs7a0NBQ3pCLDhEQUFDNUIsNERBQVdBO3dCQUFDNEIsT0FBTTtrQ0FBTzs7Ozs7O2tDQUMxQiw4REFBQzVCLDREQUFXQTt3QkFBQzRCLE9BQU07a0NBQVU7Ozs7OztrQ0FDN0IsOERBQUM1Qiw0REFBV0E7d0JBQUM0QixPQUFNO2tDQUFZOzs7Ozs7Ozs7Ozs7MEJBR2hDLDhEQUFDOUIsNERBQVdBO2dCQUFDOEIsT0FBTTtnQkFBTWQsV0FBVTswQkFDbEMsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUE0Q1YsV0FBV3lCLEdBQUcsQ0FBQ2pCOzs7Ozs7Ozs7OzswQkFHM0UsOERBQUNkLDREQUFXQTtnQkFBQzhCLE9BQU07Z0JBQU9kLFdBQVU7MEJBQ25DLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYlQsZUFBZXdCLEdBQUcsQ0FBQ2pCOzs7Ozs7Ozs7OzswQkFJdEIsOERBQUNkLDREQUFXQTtnQkFBQzhCLE9BQU07Z0JBQVVkLFdBQVU7MEJBQ3RDLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYkosa0JBQWtCbUIsR0FBRyxDQUFDakI7Ozs7Ozs7Ozs7OzBCQUl6Qiw4REFBQ2QsNERBQVdBO2dCQUFDOEIsT0FBTTtnQkFBWWQsV0FBVTswQkFDeEMsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUNiSCxvQkFBb0JrQixHQUFHLENBQUNqQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLOUI7S0EzRndCVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxhcHBcXGRhc2hib2FyZFxccGxhY2VtZW50c1xcY2xpZW50LXdyYXBwZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGFwcC9kYXNoYm9hcmQvcGxhY2VtZW50cy9jbGllbnQtd3JhcHBlci50c3hcblwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkRm9vdGVyLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RhYnNcIjtcbmltcG9ydCB0eXBlIHsgQWRTbG90IH0gZnJvbSBcIkAvbGliL2RiL21vZGVsc1wiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBsYWNlbWVudHNDbGllbnRXcmFwcGVyKHsgcGxhY2VtZW50cyB9OiB7IHBsYWNlbWVudHM6IEFkU2xvdFtdIH0pIHtcblx0Y29uc3QgaG9tZVBsYWNlbWVudHMgPSBwbGFjZW1lbnRzLmZpbHRlcigocCkgPT4gcC5wYWdlID09PSBcImhvbWVcIik7XG5cdGNvbnN0IG5ld3NsZXR0ZXJQbGFjZW1lbnRzID0gcGxhY2VtZW50cy5maWx0ZXIoKHApID0+IHAucGFnZSA9PT0gXCJuZXdzbGV0dGVyXCIpO1xuXHRjb25zdCBzdWJuZXRzUGxhY2VtZW50cyA9IHBsYWNlbWVudHMuZmlsdGVyKChwKSA9PiBwLnBhZ2UgPT09IFwic3VibmV0c1wiKTtcblx0Y29uc3QgY29tcGFuaWVzUGxhY2VtZW50cyA9IHBsYWNlbWVudHMuZmlsdGVyKChwKSA9PiBwLnBhZ2UgPT09IFwiY29tcGFuaWVzXCIpO1xuXG5cdGNvbnN0IHJlbmRlclBsYWNlbWVudENhcmQgPSAocGxhY2VtZW50OiBBZFNsb3QpID0+IChcblx0XHQ8Q2FyZCBrZXk9e3BsYWNlbWVudC5pZH0+XG5cdFx0XHQ8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi00XCI+XG5cdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG5cdFx0XHRcdFx0PEJhZGdlPlxuXHRcdFx0XHRcdFx0e3BsYWNlbWVudC5wYWdlID09PSBcImhvbWVcIlxuXHRcdFx0XHRcdFx0XHQ/IFwiaG9tZVwiXG5cdFx0XHRcdFx0XHRcdDogcGxhY2VtZW50LnBhZ2UgPT09IFwic3VibmV0c1wiXG5cdFx0XHRcdFx0XHRcdD8gXCJTdWJuZXRzXCJcblx0XHRcdFx0XHRcdFx0OiBwbGFjZW1lbnQucGFnZSA9PT0gXCJjb21wYW5pZXNcIlxuXHRcdFx0XHRcdFx0XHQ/IFwiQ29tcGFuaWVzXCJcblx0XHRcdFx0XHRcdFx0OiBcIk5ld3NsZXR0ZXJcIn1cblx0XHRcdFx0XHQ8L0JhZGdlPlxuXHRcdFx0XHRcdDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiPlxuXHRcdFx0XHRcdFx0e3BsYWNlbWVudC53aWR0aH14e3BsYWNlbWVudC5oZWlnaHR9XG5cdFx0XHRcdFx0PC9CYWRnZT5cblx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwibXQtMlwiPntwbGFjZW1lbnQubmFtZX08L0NhcmRUaXRsZT5cblx0XHRcdFx0PENhcmREZXNjcmlwdGlvbj57cGxhY2VtZW50LmRlc2NyaXB0aW9ufTwvQ2FyZERlc2NyaXB0aW9uPlxuXHRcdFx0PC9DYXJkSGVhZGVyPlxuXHRcdFx0PENhcmRDb250ZW50IGNsYXNzTmFtZT1cInBiLTJcIj5cblx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJhc3BlY3QtdmlkZW8gb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtbGcgYmctbXV0ZWRcIj5cblx0XHRcdFx0XHQ8SW1hZ2Vcblx0XHRcdFx0XHRcdHNyYz17YC9wbGFjZWhvbGRlci5zdmc/dGV4dD0ke3BsYWNlbWVudC53aWR0aH14JHtwbGFjZW1lbnQuaGVpZ2h0fWB9XG5cdFx0XHRcdFx0XHRhbHQ9e3BsYWNlbWVudC5uYW1lfVxuXHRcdFx0XHRcdFx0d2lkdGg9e3BsYWNlbWVudC53aWR0aH1cblx0XHRcdFx0XHRcdGhlaWdodD17cGxhY2VtZW50LmhlaWdodH1cblx0XHRcdFx0XHRcdGNsYXNzTmFtZT1cIm9iamVjdC1jb250YWluIHctZnVsbCBoLWZ1bGxcIlxuXHRcdFx0XHRcdC8+XG5cdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTQgbXQtNFwiPlxuXHRcdFx0XHRcdDxkaXY+XG5cdFx0XHRcdFx0XHQ8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+RGltZW5zaW9uczwvcD5cblx0XHRcdFx0XHRcdDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG5cdFx0XHRcdFx0XHRcdHtwbGFjZW1lbnQud2lkdGh9eHtwbGFjZW1lbnQuaGVpZ2h0fXB4XG5cdFx0XHRcdFx0XHQ8L3A+XG5cdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJqdXN0aWZ5LXNlbGYtZW5kIHRleHQtcmlnaHRcIj5cblx0XHRcdFx0XHRcdDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5Gb3JtYXQ8L3A+XG5cdFx0XHRcdFx0XHQ8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuXHRcdFx0XHRcdFx0XHR7cGxhY2VtZW50LmFsbG93ZWRfYWRfdHlwZXM/LmpvaW4oXCIsIFwiKSB8fCBcIkltYWdlL0hUTUxcIn1cblx0XHRcdFx0XHRcdDwvcD5cblx0XHRcdFx0XHQ8L2Rpdj5cblx0XHRcdFx0PC9kaXY+XG5cdFx0XHQ8L0NhcmRDb250ZW50PlxuXHRcdFx0PENhcmRGb290ZXI+XG5cdFx0XHRcdDxMaW5rIGhyZWY9e2AvZGFzaGJvYXJkL2NhbXBhaWducy9jcmVhdGU/cGxhY2VtZW50PSR7cGxhY2VtZW50LmlkfWB9IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuXHRcdFx0XHRcdDxCdXR0b24gY2xhc3NOYW1lPVwidy1mdWxsXCI+U2VsZWN0IFBsYWNlbWVudDwvQnV0dG9uPlxuXHRcdFx0XHQ8L0xpbms+XG5cdFx0XHQ8L0NhcmRGb290ZXI+XG5cdFx0PC9DYXJkPlxuXHQpO1xuXG5cdHJldHVybiAoXG5cdFx0PFRhYnMgZGVmYXVsdFZhbHVlPVwiYWxsXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG5cdFx0XHQ8VGFic0xpc3Q+XG5cdFx0XHRcdDxUYWJzVHJpZ2dlciB2YWx1ZT1cImFsbFwiPkFsbDwvVGFic1RyaWdnZXI+XG5cdFx0XHRcdDxUYWJzVHJpZ2dlciB2YWx1ZT1cImhvbWVcIj5Ib21lPC9UYWJzVHJpZ2dlcj5cblx0XHRcdFx0PFRhYnNUcmlnZ2VyIHZhbHVlPVwic3VibmV0c1wiPlN1Ym5ldHM8L1RhYnNUcmlnZ2VyPlxuXHRcdFx0XHQ8VGFic1RyaWdnZXIgdmFsdWU9XCJjb21wYW5pZXNcIj5Db21wYW5pZXM8L1RhYnNUcmlnZ2VyPlxuXHRcdFx0PC9UYWJzTGlzdD5cblxuXHRcdFx0PFRhYnNDb250ZW50IHZhbHVlPVwiYWxsXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG5cdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtM1wiPntwbGFjZW1lbnRzLm1hcChyZW5kZXJQbGFjZW1lbnRDYXJkKX08L2Rpdj5cblx0XHRcdDwvVGFic0NvbnRlbnQ+XG5cblx0XHRcdDxUYWJzQ29udGVudCB2YWx1ZT1cImhvbWVcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cblx0XHRcdFx0PGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00IG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zXCI+XG5cdFx0XHRcdFx0e2hvbWVQbGFjZW1lbnRzLm1hcChyZW5kZXJQbGFjZW1lbnRDYXJkKX1cblx0XHRcdFx0PC9kaXY+XG5cdFx0XHQ8L1RhYnNDb250ZW50PlxuXG5cdFx0XHQ8VGFic0NvbnRlbnQgdmFsdWU9XCJzdWJuZXRzXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG5cdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtM1wiPlxuXHRcdFx0XHRcdHtzdWJuZXRzUGxhY2VtZW50cy5tYXAocmVuZGVyUGxhY2VtZW50Q2FyZCl9XG5cdFx0XHRcdDwvZGl2PlxuXHRcdFx0PC9UYWJzQ29udGVudD5cblxuXHRcdFx0PFRhYnNDb250ZW50IHZhbHVlPVwiY29tcGFuaWVzXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG5cdFx0XHRcdDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtM1wiPlxuXHRcdFx0XHRcdHtjb21wYW5pZXNQbGFjZW1lbnRzLm1hcChyZW5kZXJQbGFjZW1lbnRDYXJkKX1cblx0XHRcdFx0PC9kaXY+XG5cdFx0XHQ8L1RhYnNDb250ZW50PlxuXHRcdDwvVGFicz5cblx0KTtcbn1cbiJdLCJuYW1lcyI6WyJCYWRnZSIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRGb290ZXIiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsIkltYWdlIiwiTGluayIsIlBsYWNlbWVudHNDbGllbnRXcmFwcGVyIiwicGxhY2VtZW50cyIsImhvbWVQbGFjZW1lbnRzIiwiZmlsdGVyIiwicCIsInBhZ2UiLCJuZXdzbGV0dGVyUGxhY2VtZW50cyIsInN1Ym5ldHNQbGFjZW1lbnRzIiwiY29tcGFuaWVzUGxhY2VtZW50cyIsInJlbmRlclBsYWNlbWVudENhcmQiLCJwbGFjZW1lbnQiLCJjbGFzc05hbWUiLCJkaXYiLCJ2YXJpYW50Iiwid2lkdGgiLCJoZWlnaHQiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJzcmMiLCJhbHQiLCJhbGxvd2VkX2FkX3R5cGVzIiwiam9pbiIsImhyZWYiLCJpZCIsImRlZmF1bHRWYWx1ZSIsInZhbHVlIiwibWFwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/placements/client-wrapper.tsx\n"));

/***/ })

});