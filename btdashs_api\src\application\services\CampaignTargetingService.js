const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const { validateTargetingRules, analyzeTargetingEffectiveness } = require("../../validators/targetingValidators");

/**
 * Campaign Targeting Service
 *
 * Handles targeting rules at the campaign level.
 * All ads in a campaign inherit the campaign's targeting settings.
 *
 * <AUTHOR> Development Team
 * @since 2.0.0
 */
class CampaignTargetingService {
	/**
	 * Create or update targeting rules for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} targetingRules - Targeting rules object
	 * @returns {Promise<Object>} Updated targeting configuration
	 */
	async setTargetingRules(campaignId, targetingRules) {
		try {
			// Validate targeting rules
			const validation = validateTargetingRules(targetingRules);

			if (!validation.valid) {
				throw new Error(`Invalid targeting rules: ${validation.errors.map((e) => e.message).join(", ")}`);
			}

			// Use validated and normalized targeting rules
			const normalizedTargeting = validation.value;

			// Analyze targeting effectiveness
			const effectiveness = analyzeTargetingEffectiveness(normalizedTargeting);

			// Update the campaign's targeting JSONB column directly
			await db("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.update({
					targeting: JSON.stringify(normalizedTargeting),
					updated_at: new Date(),
				});

			logger.info("Campaign targeting rules updated", {
				campaignId,
				targetingRules: normalizedTargeting,
				effectiveness: effectiveness.score,
				warnings: validation.warnings,
			});

			return {
				campaignId,
				targeting: normalizedTargeting,
				validation: {
					valid: true,
					warnings: validation.warnings,
					effectiveness,
				},
				message: "Targeting rules updated successfully",
			};
		} catch (error) {
			logger.error("Error setting campaign targeting rules", {
				error,
				campaignId,
				targetingRules,
			});
			throw error;
		}
	}

	/**
	 * Get targeting rules for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @returns {Promise<Object>} Targeting rules object
	 */
	async getTargetingRules(campaignId) {
		try {
			const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId }).select("targeting").first();

			if (!campaign || !campaign.targeting) {
				// Return default targeting structure
				return {
					countries: { mode: "all", include: [], exclude: [] },
					pageTypes: { types: [], categories: {} },
					devices: [],
					languages: [],
					interests: [],
					age: { min: null, max: null },
				};
			}

			// Parse targeting JSON
			const targetingRules =
				typeof campaign.targeting === "string" ? JSON.parse(campaign.targeting) : campaign.targeting;

			logger.info("Campaign targeting rules retrieved", {
				campaignId,
				hasTargeting: !!campaign.targeting,
			});

			return targetingRules;
		} catch (error) {
			logger.error("Error getting campaign targeting rules", {
				error,
				campaignId,
			});
			throw error;
		}
	}

	/**
	 * Check if a user/request matches campaign targeting criteria
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} userContext - User context (country, device, etc.)
	 * @returns {Promise<boolean>} Whether user matches targeting
	 */
	async matchesTargeting(campaignId, userContext) {
		try {
			const targetingRules = await this.getTargetingRules(campaignId);

			// If no targeting rules, show to everyone
			if (!targetingRules || Object.keys(targetingRules).length === 0) {
				return true;
			}

			// Check country targeting
			if (targetingRules.countries) {
				const { mode, include, exclude } = targetingRules.countries;
				const userCountry = userContext.country_code;

				if (mode === "include" && include.length > 0) {
					if (!userCountry || !include.includes(userCountry)) {
						return false;
					}
				} else if (mode === "exclude" && exclude.length > 0) {
					if (userCountry && exclude.includes(userCountry)) {
						return false;
					}
				}
			}

			// Check device targeting
			if (targetingRules.devices && targetingRules.devices.length > 0) {
				const userDevice = userContext.device_type;
				if (!userDevice || !targetingRules.devices.includes(userDevice)) {
					return false;
				}
			}

			// Check language targeting
			if (targetingRules.languages && targetingRules.languages.length > 0) {
				const userLanguage = userContext.language;
				if (!userLanguage || !targetingRules.languages.includes(userLanguage)) {
					return false;
				}
			}

			// Additional targeting checks can be added here...

			return true;
		} catch (error) {
			logger.error("Error checking campaign targeting match", {
				error,
				campaignId,
				userContext,
			});
			// On error, default to showing the ad
			return true;
		}
	}

	/**
	 * Get all ads that match targeting for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} userContext - User context
	 * @returns {Promise<Array>} Array of matching ads
	 */
	async getTargetedAds(campaignId, userContext) {
		try {
			const matches = await this.matchesTargeting(campaignId, userContext);

			if (!matches) {
				return [];
			}

			// Get all active ads for this campaign
			const ads = await db("dtm_ads.ads")
				.where({
					campaign_id: campaignId,
					status: "active",
				})
				.select("*");

			return ads;
		} catch (error) {
			logger.error("Error getting targeted ads", { error, campaignId, userContext });
			throw error;
		}
	}

	/**
	 * Validate targeting rules without saving them
	 * @param {Object} targetingRules - Targeting rules to validate
	 * @returns {Promise<Object>} Validation result with effectiveness analysis
	 */
	async validateTargetingRules(targetingRules) {
		try {
			const validation = validateTargetingRules(targetingRules);

			if (validation.valid) {
				const effectiveness = analyzeTargetingEffectiveness(validation.value);
				validation.effectiveness = effectiveness;
			}

			logger.info("Targeting rules validated", {
				valid: validation.valid,
				errorCount: validation.errors.length,
				warningCount: validation.warnings.length,
			});

			return validation;
		} catch (error) {
			logger.error("Error validating targeting rules", { error, targetingRules });
			throw error;
		}
	}

	/**
	 * Get targeting options for UI
	 * @returns {Promise<Object>} Available targeting options
	 */
	async getTargetingOptions() {
		try {
			return {
				countries: [
					{ code: "US", name: "United States" },
					{ code: "CA", name: "Canada" },
					{ code: "UK", name: "United Kingdom" },
					{ code: "DE", name: "Germany" },
					{ code: "FR", name: "France" },
					{ code: "AU", name: "Australia" },
					{ code: "JP", name: "Japan" },
					{ code: "BR", name: "Brazil" },
					{ code: "IN", name: "India" },
					{ code: "CN", name: "China" },
				],
				devices: ["desktop", "mobile", "tablet"],
				languages: [
					{ code: "en", name: "English" },
					{ code: "es", name: "Spanish" },
					{ code: "fr", name: "French" },
					{ code: "de", name: "German" },
					{ code: "pt", name: "Portuguese" },
					{ code: "ja", name: "Japanese" },
					{ code: "zh", name: "Chinese" },
					{ code: "hi", name: "Hindi" },
				],
				pageTypes: [
					{ type: "subnet", name: "Subnet Pages" },
					{ type: "companies", name: "Company Pages" },
					{ type: "products", name: "Product Pages" },
					{ type: "news", name: "News Pages" },
				],
				interests: [
					"technology",
					"business",
					"finance",
					"healthcare",
					"education",
					"entertainment",
					"sports",
					"travel",
				],
				age_ranges: [
					{ value: "18-24", name: "18-24 years" },
					{ value: "25-34", name: "25-34 years" },
					{ value: "35-44", name: "35-44 years" },
					{ value: "45-54", name: "45-54 years" },
					{ value: "55-64", name: "55-64 years" },
					{ value: "65+", name: "65+ years" },
				],
			};
		} catch (error) {
			logger.error("Error getting targeting options", { error });
			throw error;
		}
	}
}

module.exports = CampaignTargetingService;
