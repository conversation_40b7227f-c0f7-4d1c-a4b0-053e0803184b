"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  CreditCard, 
  RefreshCw, 
  Server, 
  TrendingDown, 
  TrendingUp,
  Users,
  Zap
} from "lucide-react";
import { toast } from "sonner";

interface MonitoringData {
  keyMetrics: {
    systemUptime: string;
    paymentSuccessRate: string;
    errorRate: string;
    activeAlerts: number;
    totalTransactions: number;
    failedTransactions: number;
  };
  systemHealth: {
    healthy: boolean;
    errorRate: number;
    database: {
      healthy: boolean;
      responseTime: number;
    };
    api: {
      healthy: boolean;
      averageResponseTime: number;
    };
  };
  paymentStats: {
    hourly: {
      totalAttempts: number;
      failures: number;
      failureRate: number;
      totalFailedAmount: number;
    };
    daily: {
      totalAttempts: number;
      failures: number;
      failureRate: number;
      totalFailedAmount: number;
    };
  };
  recentAlerts: Array<{
    id: string;
    type: string;
    severity: string;
    message: string;
    created_at: string;
  }>;
  lastUpdated: string;
}

export function MonitoringDashboard() {
  const [data, setData] = useState<MonitoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMonitoringData = async () => {
    try {
      const response = await fetch("/api/monitoring/dashboard");
      if (!response.ok) {
        throw new Error(`Failed to fetch monitoring data: ${response.status}`);
      }
      
      const result = await response.json();
      if (result.success) {
        setData(result.data);
        setError(null);
      } else {
        throw new Error(result.message || "Failed to fetch monitoring data");
      }
    } catch (error) {
      console.error("Error fetching monitoring data:", error);
      setError(error instanceof Error ? error.message : "Unknown error");
    }
  };

  const triggerManualCheck = async () => {
    setRefreshing(true);
    try {
      const response = await fetch("/api/monitoring/check", { method: "POST" });
      if (response.ok) {
        toast.success("Monitoring check completed");
        await fetchMonitoringData();
      } else {
        throw new Error("Failed to trigger monitoring check");
      }
    } catch (error) {
      toast.error("Failed to trigger monitoring check");
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchMonitoringData();
      setLoading(false);
    };

    loadData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMonitoringData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">System Monitoring</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <Button onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load monitoring data: {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!data) return null;

  const getStatusColor = (status: string) => {
    return status === "UP" ? "text-green-600" : "text-red-600";
  };

  const getHealthBadge = (healthy: boolean) => {
    return healthy ? (
      <Badge className="bg-green-100 text-green-800">Healthy</Badge>
    ) : (
      <Badge className="bg-red-100 text-red-800">Unhealthy</Badge>
    );
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "text-red-600";
      case "high": return "text-orange-600";
      case "medium": return "text-yellow-600";
      default: return "text-blue-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <p className="text-muted-foreground">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </p>
        </div>
        <Button onClick={triggerManualCheck} disabled={refreshing}>
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`} />
          {refreshing ? "Checking..." : "Manual Check"}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Status</p>
                <p className={`text-2xl font-bold ${getStatusColor(data.keyMetrics.systemUptime)}`}>
                  {data.keyMetrics.systemUptime}
                </p>
              </div>
              <Server className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Payment Success Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {data.keyMetrics.paymentSuccessRate}%
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Error Rate</p>
                <p className="text-2xl font-bold text-orange-600">
                  {data.keyMetrics.errorRate}%
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Alerts</p>
                <p className="text-2xl font-bold text-red-600">
                  {data.keyMetrics.activeAlerts}
                </p>
              </div>
              <Zap className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Health */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>System Health</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Overall Health</span>
              {getHealthBadge(data.systemHealth.healthy)}
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Database</span>
                <div className="flex items-center space-x-2">
                  {getHealthBadge(data.systemHealth.database.healthy)}
                  <span className="text-xs text-muted-foreground">
                    {data.systemHealth.database.responseTime}ms
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">API</span>
                <div className="flex items-center space-x-2">
                  {getHealthBadge(data.systemHealth.api.healthy)}
                  <span className="text-xs text-muted-foreground">
                    {data.systemHealth.api.averageResponseTime}ms
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Error Rate</span>
                <span className="text-sm font-medium">
                  {(data.systemHealth.errorRate * 100).toFixed(2)}%
                </span>
              </div>
              <Progress value={data.systemHealth.errorRate * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>Payment Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Last Hour</p>
                <p className="text-lg font-semibold">
                  {data.paymentStats.hourly.totalAttempts} attempts
                </p>
                <p className="text-sm text-red-600">
                  {data.paymentStats.hourly.failures} failures
                </p>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground">Last 24 Hours</p>
                <p className="text-lg font-semibold">
                  {data.paymentStats.daily.totalAttempts} attempts
                </p>
                <p className="text-sm text-red-600">
                  {data.paymentStats.daily.failures} failures
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Daily Failure Rate</span>
                <span className="text-sm font-medium">
                  {(data.paymentStats.daily.failureRate * 100).toFixed(1)}%
                </span>
              </div>
              <Progress value={data.paymentStats.daily.failureRate * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Recent Alerts</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.recentAlerts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>No recent alerts. System is running smoothly.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {data.recentAlerts.map((alert) => (
                <div key={alert.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <AlertTriangle className={`h-5 w-5 mt-0.5 ${getSeverityColor(alert.severity)}`} />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {new Date(alert.created_at).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-sm font-medium mt-1">{alert.message}</p>
                    <p className="text-xs text-muted-foreground">{alert.type}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
