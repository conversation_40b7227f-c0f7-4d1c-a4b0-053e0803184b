const { asyncHandler } = require("../../middleware/errorHandler");
const BudgetService = require("../../application/services/BudgetService");
const AdminService = require("../../application/services/AdminService");
const InvoiceService = require("../../application/services/InvoiceService");
const WebhookRetryService = require("../../application/services/WebhookRetryService");
const PaymentAnalyticsService = require("../../application/services/PaymentAnalyticsService");
const { sendSuccess, sendError, sendUnauthorized } = require("../../utils/responseWrapper");
const logger = require("../../../logger");
const db = require("../../infrastructure/database/knex");

/**
 * Store payment intent in database for tracking
 */
const storePaymentIntent = asyncHandler(async (req, res) => {
	const { payment_intent_id, campaign_id, amount, status, currency } = req.body;

	if (!payment_intent_id || !campaign_id || !amount) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Get campaign details to find advertiser
		const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaign_id }).first();

		if (!campaign) {
			return sendError(res, "Campaign not found", null, 404);
		}

		const budgetService = new BudgetService();
		const transaction = await db("dtm_ads.billing_transactions")
			.insert({
				user_id: campaign.manager_id,
				amount: amount,
				currency: currency || "USD",
				description: `Campaign payment for "${campaign.name}"`,
				status: status || "pending",
				payment_method: "stripe",
				campaign_id: campaign_id,
				payment_intent_id: payment_intent_id,
				created_at: new Date(),
				updated_at: new Date(),
			})
			.returning("*");

		logger.info("Payment intent stored", {
			payment_intent_id,
			campaign_id,
			amount,
			transaction_id: transaction[0]?.id,
		});

		return sendSuccess(res, transaction[0], "Payment intent stored successfully");
	} catch (error) {
		logger.error("Error storing payment intent", { error, payment_intent_id, campaign_id });
		return sendError(res, "Failed to store payment intent", error.message, 500);
	}
});

/**
 * Handle successful payment and activate campaign
 */
const handlePaymentSuccess = asyncHandler(async (req, res) => {
	const webhookRetryService = new WebhookRetryService();

	const success = await webhookRetryService.processWithRetry(
		"payment_intent.succeeded",
		req.body,
		async (eventData) => {
			await processPaymentSuccess(eventData);
		}
	);

	if (success) {
		return sendSuccess(res, null, "Payment success processed");
	} else {
		return sendError(res, "Payment processing failed after retries", null, 500);
	}
});

const processPaymentSuccess = async (eventData) => {
	const { payment_intent_id, campaign_id, amount, status, payment_method } = eventData;

	if (!payment_intent_id || !campaign_id) {
		throw new Error("Missing required fields");
	}

	const trx = await db.transaction();

	try {
		await trx("dtm_ads.billing_transactions")
			.where({
				payment_intent_id: payment_intent_id,
				campaign_id: campaign_id,
			})
			.update({
				status: "completed",
				payment_method: payment_method || "stripe",
				updated_at: new Date(),
			});

		const campaign = await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).first();

		if (!campaign) {
			throw new Error("Campaign not found");
		}

		const invoiceService = new InvoiceService();
		await invoiceService.markInvoicePaid(payment_intent_id, payment_method || "stripe");

		const budgetService = new BudgetService();
		await budgetService.addFunds(campaign.manager_id, amount, `Campaign payment for "${campaign.name}"`, "stripe");

		await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).update({
			status: "active",
			updated_at: new Date(),
		});

		// Ads inherit the campaign status
		await trx("dtm_ads.ads").where({ campaign_id: campaign_id }).update({
			status: "active",
			updated_at: new Date(),
		});

		await trx.commit();

		logger.info("Payment processed successfully", {
			payment_intent_id,
			campaign_id,
			amount,
			advertiser_id: campaign.advertiser_id,
		});

		return sendSuccess(res, { campaign_id, status: "active" }, "Payment processed and campaign activated");
	} catch (error) {
		await trx.rollback();
		logger.error("Error processing payment success", { error, payment_intent_id, campaign_id });
		throw error;
	}
};

/**
 * Handle failed payment
 */
const handlePaymentFailed = asyncHandler(async (req, res) => {
	const webhookRetryService = new WebhookRetryService();

	const success = await webhookRetryService.processWithRetry(
		"payment_intent.payment_failed",
		req.body,
		async (eventData) => {
			await processPaymentFailure(eventData);
		}
	);

	if (success) {
		return sendSuccess(res, null, "Payment failure processed");
	} else {
		return sendError(res, "Payment failure processing failed after retries", null, 500);
	}
});

const processPaymentFailure = async (eventData) => {
	const { payment_intent_id, campaign_id, status, failure_reason, failure_code, amount, currency, payment_method } =
		eventData;

	if (!payment_intent_id || !campaign_id) {
		throw new Error("Missing required fields");
	}

	const trx = await db.transaction();

	try {
		const campaign = await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).first();

		if (!campaign) {
			throw new Error("Campaign not found");
		}

		await trx("dtm_ads.billing_transactions")
			.where({
				payment_intent_id: payment_intent_id,
				campaign_id: campaign_id,
			})
			.update({
				status: status === "processing_error" ? "processing_error" : "failed",
				description: db.raw("description || ' - ' || ?", [failure_reason || "Payment failed"]),
				updated_at: new Date(),
			});

		const paymentAnalytics = new PaymentAnalyticsService();
		await paymentAnalytics.recordPaymentFailure({
			paymentIntentId: payment_intent_id,
			campaignId: campaign_id,
			userId: campaign.manager_id,
			failureCode: failure_code || "unknown",
			failureMessage: failure_reason || "Payment failed",
			amount: amount,
			currency: currency || "USD",
			paymentMethod: payment_method || "stripe",
			metadata: {
				campaign_name: campaign.name,
				advertiser_id: campaign.advertiser_id,
			},
		});

		const newStatus = status === "processing_error" ? "payment_error" : "payment_failed";
		await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).update({
			status: newStatus,
			updated_at: new Date(),
		});

		// Send user notification about payment failure
		const NotificationService = require("../../application/services/NotificationService");
		const notificationService = new NotificationService();

		const isProcessingError = status === "processing_error";
		await notificationService.sendNotification({
			user_id: campaign.advertiser_id,
			type: "payment_failure",
			title: isProcessingError ? "Payment Processing Error" : "Payment Failed",
			message: isProcessingError
				? `There was an error processing payment for your campaign "${campaign.name}". Our team has been notified and will resolve this issue. You may retry payment or contact support.`
				: `Payment failed for your campaign "${campaign.name}". Reason: ${
						failure_reason || "Payment declined"
				  }. Please update your payment method and try again.`,
			related_id: campaign_id,
			metadata: {
				payment_intent_id,
				failure_reason,
				retry_available: true,
				support_contact: true,
			},
			email: {
				enabled: true,
				subject: isProcessingError ? "Payment Processing Error - BTDash" : "Payment Failed - BTDash",
				template: isProcessingError ? "payment_processing_error" : "payment_failed",
				templateData: {
					campaignName: campaign.name,
					failureReason: failure_reason,
					supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
					retryUrl: `${process.env.FRONTEND_URL}/dashboard/campaigns/${campaign_id}/payment`,
				},
			},
		});

		await trx.commit();

		logger.info("Payment failure processed with user notification", {
			payment_intent_id,
			campaign_id,
			failure_reason,
			status: newStatus,
		});

		return sendSuccess(
			res,
			{
				campaign_id,
				status: newStatus,
				retry_available: true,
				user_notified: true,
			},
			"Payment failure processed and user notified"
		);
	} catch (error) {
		await trx.rollback();
		logger.error("Error processing payment failure", { error, payment_intent_id, campaign_id });
		return sendError(res, "Failed to process payment failure", error.message, 500);
	}
};

/**
 * Activate campaign after successful payment
 */
const activateCampaign = asyncHandler(async (req, res) => {
	const { id: campaignId } = req.params;
	const { payment_intent_id } = req.body;

	if (!campaignId || !payment_intent_id) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Verify payment was successful
		const transaction = await db("dtm_ads.billing_transactions")
			.where({
				invoice_id: payment_intent_id,
				campaign_id: campaignId,
				status: "completed",
			})
			.first();

		if (!transaction) {
			return sendError(res, "Payment not found or not completed", null, 400);
		}

		// Update campaign status to active
		const [updatedCampaign] = await db("dtm_ads.ad_campaigns")
			.where({ id: campaignId })
			.update({
				status: "active",
				updated_at: new Date(),
			})
			.returning("*");

		if (!updatedCampaign) {
			return sendError(res, "Campaign not found", null, 404);
		}

		// Ads inherit the campaign status
		await db("dtm_ads.ads").where({ campaign_id: campaignId }).update({
			status: "active",
			updated_at: new Date(),
		});

		logger.info("Campaign activated after payment", {
			campaignId,
			payment_intent_id,
			transaction_id: transaction.id,
		});

		return sendSuccess(res, updatedCampaign, "Campaign activated successfully");
	} catch (error) {
		logger.error("Error activating campaign", { error, campaignId, payment_intent_id });
		return sendError(res, "Failed to activate campaign", error.message, 500);
	}
});

/**
 * Retry payment for a failed campaign
 */
const retryPayment = asyncHandler(async (req, res) => {
	const { id: campaignId } = req.params;
	const userId = req.auth?.sub;

	if (!campaignId || !userId) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Get campaign details and verify ownership
		const campaign = await db("dtm_ads.ad_campaigns")
			.leftJoin("dtm_base.users", "ad_campaigns.advertiser_id", "users.id")
			.where({
				"ad_campaigns.id": campaignId,
				"users.auth0_id": userId,
			})
			.select("ad_campaigns.*")
			.first();

		if (!campaign) {
			return sendError(res, "Campaign not found or access denied", null, 404);
		}

		// Check if campaign is in a retryable state
		if (!["payment_failed", "payment_error"].includes(campaign.status)) {
			return sendError(res, "Campaign is not in a retryable state", null, 400);
		}

		// Reset campaign status to approved for retry
		await db("dtm_ads.ad_campaigns").where({ id: campaignId }).update({
			status: "approved",
			updated_at: new Date(),
		});

		// Create new billing transaction for retry
		await db("dtm_ads.billing_transactions").insert({
			advertiser_id: campaign.advertiser_id,
			amount: campaign.total_budget,
			currency: "USD",
			description: `Campaign payment retry for "${campaign.name}"`,
			status: "pending",
			payment_method: "stripe",
			campaign_id: campaignId,
			created_at: new Date(),
			updated_at: new Date(),
		});

		logger.info("Payment retry initiated", {
			campaignId,
			userId,
			budget: campaign.total_budget,
		});

		return sendSuccess(
			res,
			{
				campaign_id: campaignId,
				status: "approved",
				retry_initiated: true,
				payment_required: true,
			},
			"Payment retry initiated successfully"
		);
	} catch (error) {
		logger.error("Error initiating payment retry", { error, campaignId, userId });
		return sendError(res, "Failed to initiate payment retry", error.message, 500);
	}
});

/**
 * Get payment analytics
 */
const getPaymentAnalytics = asyncHandler(async (req, res) => {
	const { startDate, endDate, userId, campaignId } = req.query;

	const paymentAnalytics = new PaymentAnalyticsService();
	const analytics = await paymentAnalytics.getPaymentAnalytics({
		startDate: startDate ? new Date(startDate) : undefined,
		endDate: endDate ? new Date(endDate) : undefined,
		userId: userId ? parseInt(userId) : undefined,
		campaignId: campaignId ? parseInt(campaignId) : undefined,
	});

	return sendSuccess(res, analytics, "Payment analytics retrieved successfully");
});

/**
 * Get payment health report
 */
const getPaymentHealthReport = asyncHandler(async (req, res) => {
	const { startDate, endDate } = req.query;

	const paymentAnalytics = new PaymentAnalyticsService();
	const healthReport = await paymentAnalytics.generateHealthReport({
		startDate: startDate ? new Date(startDate) : undefined,
		endDate: endDate ? new Date(endDate) : undefined,
	});

	return sendSuccess(res, healthReport, "Payment health report generated successfully");
});

/**
 * Get webhook statistics
 */
const getWebhookStats = asyncHandler(async (req, res) => {
	const { startDate, endDate } = req.query;

	const webhookRetryService = new WebhookRetryService();
	const stats = await webhookRetryService.getWebhookStats({
		startDate: startDate ? new Date(startDate) : undefined,
		endDate: endDate ? new Date(endDate) : undefined,
	});

	return sendSuccess(res, stats, "Webhook statistics retrieved successfully");
});

/**
 * Retry failed webhook
 */
const retryFailedWebhook = asyncHandler(async (req, res) => {
	const { webhookLogId } = req.params;
	const { eventType } = req.body;

	const webhookRetryService = new WebhookRetryService();

	let processor;
	if (eventType === "payment_intent.succeeded") {
		processor = processPaymentSuccess;
	} else if (eventType === "payment_intent.payment_failed") {
		processor = processPaymentFailure;
	} else {
		return sendError(res, "Unsupported event type for retry", null, 400);
	}

	try {
		await webhookRetryService.retryFailedWebhook(parseInt(webhookLogId), processor);
		return sendSuccess(res, null, "Webhook retried successfully");
	} catch (error) {
		logger.error("Failed to retry webhook", { error: error.message, webhookLogId });
		return sendError(res, "Failed to retry webhook", error.message, 500);
	}
});

module.exports = {
	storePaymentIntent,
	handlePaymentSuccess,
	handlePaymentFailed,
	activateCampaign,
	retryPayment,
	getPaymentAnalytics,
	getPaymentHealthReport,
	getWebhookStats,
	retryFailedWebhook,
};
