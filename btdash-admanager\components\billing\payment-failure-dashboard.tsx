"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
	AlertTriangle,
	Clock,
	CreditCard,
	DollarSign,
	ExternalLink,
	Info,
	Mail,
	RefreshCw,
	XCircle,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";

interface FailedCampaign {
	id: number;
	name: string;
	total_budget: number | string;
	status: string;
	failure_reason?: string;
	last_payment_attempt?: string;
	retry_count?: number;
}

interface PaymentFailureDashboardProps {
	failedCampaigns: FailedCampaign[];
	onRetryPayment?: (campaignId: number) => void;
	onContactSupport?: () => void;
}

export function PaymentFailureDashboard({
	failedCampaigns,
	onRetryPayment,
	onContactSupport,
}: PaymentFailureDashboardProps) {
	const [retryingCampaigns, setRetryingCampaigns] = useState<Set<number>>(new Set());
	const [dismissedCampaigns, setDismissedCampaigns] = useState<Set<number>>(new Set());

	const visibleCampaigns = failedCampaigns.filter((campaign) => !dismissedCampaigns.has(campaign.id));

	const totalFailedAmount = failedCampaigns.reduce((sum, campaign) => {
		const budget =
			typeof campaign.total_budget === "number"
				? campaign.total_budget
				: parseFloat(campaign.total_budget?.toString() || "0");
		return sum + budget;
	}, 0);

	const handleRetryPayment = async (campaignId: number) => {
		setRetryingCampaigns((prev) => new Set(prev).add(campaignId));

		try {
			if (onRetryPayment) {
				await onRetryPayment(campaignId);
			} else {
				// Default retry behavior - redirect to payment page
				window.location.href = `/dashboard/campaigns/${campaignId}/payment`;
			}
			toast.success("Payment retry initiated");
		} catch (error) {
			toast.error("Failed to retry payment");
		} finally {
			setRetryingCampaigns((prev) => {
				const newSet = new Set(prev);
				newSet.delete(campaignId);
				return newSet;
			});
		}
	};

	const handleDismissCampaign = (campaignId: number) => {
		setDismissedCampaigns((prev) => new Set(prev).add(campaignId));
	};

	const handleContactSupport = () => {
		if (onContactSupport) {
			onContactSupport();
		} else {
			const subject = encodeURIComponent("Payment Issues - Multiple Campaigns");
			const campaignList = failedCampaigns
				.map((c) => {
					const budget =
						typeof c.total_budget === "number"
							? c.total_budget.toFixed(2)
							: parseFloat(c.total_budget?.toString() || "0").toFixed(2);
					return `- ${c.name} (ID: ${c.id}) - $${budget}`;
				})
				.join("\n");

			const body = encodeURIComponent(`
Hello BTDash Support,

I'm experiencing payment issues with multiple campaigns:

${campaignList}

Total affected amount: $${totalFailedAmount.toFixed(2)}

Please help me resolve these issues.

Thank you,
      `);

			window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, "_blank");
		}
	};

	const getFailureTypeColor = (status: string) => {
		switch (status) {
			case "payment_failed":
				return "destructive";
			case "payment_error":
				return "secondary";
			default:
				return "outline";
		}
	};

	const getRetryRecommendation = (campaign: FailedCampaign) => {
		const retryCount = campaign.retry_count || 0;

		if (retryCount >= 3) {
			return {
				message: "Maximum retries reached. Contact support recommended.",
				action: "contact",
				variant: "destructive" as const,
			};
		} else if (campaign.status === "payment_error") {
			return {
				message: "Processing error detected. Safe to retry.",
				action: "retry",
				variant: "default" as const,
			};
		} else {
			return {
				message: "Payment declined. Check payment method before retry.",
				action: "retry",
				variant: "secondary" as const,
			};
		}
	};

	if (visibleCampaigns.length === 0) {
		return null;
	}

	return (
		<div className="space-y-6">
			{/* Summary Card */}
			<Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-red-800 dark:text-red-200">
						<AlertTriangle className="h-5 w-5" />
						Payment Issues Detected
					</CardTitle>
					<CardDescription className="text-red-700 dark:text-red-300">
						{visibleCampaigns.length} campaign{visibleCampaigns.length > 1 ? "s" : ""} require
						{visibleCampaigns.length === 1 ? "s" : ""} payment attention
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="flex items-center gap-2">
							<DollarSign className="h-4 w-4 text-red-600" />
							<div>
								<div className="text-sm font-medium">Total Affected</div>
								<div className="text-lg font-bold">${totalFailedAmount.toFixed(2)}</div>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<XCircle className="h-4 w-4 text-red-600" />
							<div>
								<div className="text-sm font-medium">Failed Campaigns</div>
								<div className="text-lg font-bold">{visibleCampaigns.length}</div>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Clock className="h-4 w-4 text-red-600" />
							<div>
								<div className="text-sm font-medium">Needs Action</div>
								<div className="text-lg font-bold">Immediate</div>
							</div>
						</div>
					</div>

					<div className="flex gap-3">
						<Button onClick={handleContactSupport} variant="outline" className="flex-1">
							<Mail className="mr-2 h-4 w-4" />
							Contact Support
						</Button>
						<Button asChild className="flex-1">
							<Link href="/dashboard/billing">
								<CreditCard className="mr-2 h-4 w-4" />
								Manage Billing
							</Link>
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Individual Campaign Cards */}
			<div className="space-y-4">
				{visibleCampaigns.map((campaign) => {
					const recommendation = getRetryRecommendation(campaign);
					const isRetrying = retryingCampaigns.has(campaign.id);

					return (
						<Card key={campaign.id} className="border-orange-200">
							<CardHeader className="pb-3">
								<div className="flex items-center justify-between">
									<div className="space-y-1">
										<CardTitle className="text-lg">{campaign.name}</CardTitle>
										<div className="flex items-center gap-2">
											<Badge variant={getFailureTypeColor(campaign.status)}>
												{campaign.status.replace("_", " ").toUpperCase()}
											</Badge>
											<span className="text-sm text-muted-foreground">
												$
												{typeof campaign.total_budget === "number"
													? campaign.total_budget.toFixed(2)
													: parseFloat(campaign.total_budget?.toString() || "0").toFixed(2)}
											</span>
										</div>
									</div>
									<Button
										variant="ghost"
										size="sm"
										onClick={() => handleDismissCampaign(campaign.id)}
									>
										×
									</Button>
								</div>
							</CardHeader>

							<CardContent className="space-y-4">
								{campaign.failure_reason && (
									<Alert variant="destructive">
										<AlertTriangle className="h-4 w-4" />
										<AlertDescription>
											<strong>Failure Reason:</strong> {campaign.failure_reason}
										</AlertDescription>
									</Alert>
								)}

								<Alert variant={recommendation.variant}>
									<Info className="h-4 w-4" />
									<AlertDescription>{recommendation.message}</AlertDescription>
								</Alert>

								{campaign.last_payment_attempt && (
									<div className="text-sm text-muted-foreground">
										Last attempt: {new Date(campaign.last_payment_attempt).toLocaleString()}
									</div>
								)}

								<div className="flex gap-3">
									<Button
										onClick={() => handleRetryPayment(campaign.id)}
										disabled={isRetrying || recommendation.action === "contact"}
										className="flex-1"
									>
										{isRetrying ? (
											<>
												<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
												Retrying...
											</>
										) : (
											<>
												<CreditCard className="mr-2 h-4 w-4" />
												Retry Payment
											</>
										)}
									</Button>

									<Button variant="outline" asChild className="flex-1">
										<Link href={`/dashboard/campaigns/${campaign.id}/payment`}>
											<ExternalLink className="mr-2 h-4 w-4" />
											Payment Details
										</Link>
									</Button>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
