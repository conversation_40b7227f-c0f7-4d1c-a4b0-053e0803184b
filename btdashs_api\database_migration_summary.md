# BTDash API - Phase 2 Database Migration Summary

## Current Schema Analysis

Based on the provided schema, the following tables already exist:

-   ✅ `ad_campaigns` - Complete, needs minor enhancements
-   ✅ `ad_clicks` - Exists, needs additional columns
-   ✅ `ad_impressions` - Exists, needs additional columns
-   ✅ `ad_reports_daily` - Exists, will be renamed and enhanced
-   ✅ `ad_slots` - Complete
-   ✅ `ad_targets` - Exists, needs additional columns
-   ✅ `ads` - Exists, needs minor enhancements

## Required Changes

### 1. New Tables to Create

-   `advertiser_balances` - User balance tracking
-   `billing_transactions` - Financial transaction history
-   `ad_notifications` - User notification system
-   `admin_actions` - Admin action logging
-   `rejection_reasons` - Standardized rejection reasons
-   `campaign_daily_reports` - Campaign-level daily aggregates

### 2. Existing Tables to Enhance

#### `ad_impressions`

-   ➕ Add `country_code VARCHAR(2)`
-   ➕ Add `device_type VARCHAR(20)`
-   ➕ Add `viewed_time INTEGER`

#### `ad_clicks`

-   ➕ Add `country_code VARCHAR(2)`
-   ➕ Add `device_type VARCHAR(20)`
-   ➕ Add `referrer_url TEXT`

#### `ads`

-   ➕ Add `rejection_reason TEXT`

#### `ad_targets`

-   ➕ Add `operator VARCHAR(20) DEFAULT 'equals'`
-   ➕ Add `created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
-   ➕ Add `updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`

#### `ad_reports_daily` → `daily_ad_reports`

-   🔄 Rename table to `daily_ad_reports`
-   ➕ Add `spend DECIMAL(10,2) DEFAULT 0.00`
-   ➕ Add `avg_view_time INTEGER`
-   ➕ Add `unique_users INTEGER DEFAULT 0`
-   ➕ Add `created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
-   ➕ Add `updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`

#### `users` (in dtm_base schema)

-   ➕ Add `is_admin BOOLEAN DEFAULT FALSE`

## Migration Execution Plan

### Step 1: Run Core Migration

```bash
psql -d your_database -f database_migrations_phase2.sql
```

This will:

-   Create all new tables
-   Add missing columns to existing tables
-   Rename `ad_reports_daily` to `daily_ad_reports`
-   Add necessary constraints

### Step 2: Add Indexes and Optimization

```bash
psql -d your_database -f database_indexes_phase2.sql
```

This will:

-   Create performance indexes
-   Add foreign key constraints
-   Set up automatic timestamp triggers
-   Create optimization views

### Step 3: Insert Reference Data

```bash
psql -d your_database -f database_seed_data_phase2.sql
```

This will:

-   Insert rejection reasons
-   Create utility functions for testing
-   Optionally create test data

## Validation Queries

After migration, run these queries to verify success:

```sql
-- Check all new tables exist
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'dtm_ads'
ORDER BY table_name;

-- Check enhanced columns were added
SELECT table_name, column_name, data_type
FROM information_schema.columns
WHERE table_schema = 'dtm_ads'
AND column_name IN ('country_code', 'device_type', 'rejection_reason', 'operator')
ORDER BY table_name, column_name;

-- Check rejection reasons were inserted
SELECT entity_type, COUNT(*) as reason_count
FROM dtm_ads.rejection_reasons
GROUP BY entity_type;

-- Check indexes were created
SELECT schemaname, tablename, indexname
FROM pg_indexes
WHERE schemaname = 'dtm_ads'
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
```

## Rollback Plan

If rollback is needed:

```sql
-- Drop new tables (in reverse dependency order)
DROP TABLE IF EXISTS dtm_ads.campaign_daily_reports CASCADE;
DROP TABLE IF EXISTS dtm_ads.admin_actions CASCADE;
DROP TABLE IF EXISTS dtm_ads.ad_notifications CASCADE;
DROP TABLE IF EXISTS dtm_ads.billing_transactions CASCADE;
DROP TABLE IF EXISTS dtm_ads.advertiser_balances CASCADE;
DROP TABLE IF EXISTS dtm_ads.rejection_reasons CASCADE;

-- Remove added columns (if needed)
ALTER TABLE dtm_ads.ad_impressions DROP COLUMN IF EXISTS country_code;
ALTER TABLE dtm_ads.ad_impressions DROP COLUMN IF EXISTS device_type;
ALTER TABLE dtm_ads.ad_impressions DROP COLUMN IF EXISTS viewed_time;

ALTER TABLE dtm_ads.ad_clicks DROP COLUMN IF EXISTS country_code;
ALTER TABLE dtm_ads.ad_clicks DROP COLUMN IF EXISTS device_type;
ALTER TABLE dtm_ads.ad_clicks DROP COLUMN IF EXISTS referrer_url;

ALTER TABLE dtm_ads.ads DROP COLUMN IF EXISTS rejection_reason;

-- DEPRECATED: ad_targets table completely removed in favor of campaign-level targeting
-- DROP TABLE dtm_ads.ad_targets; -- Handled by migration 002_remove_legacy_ad_targeting.sql

ALTER TABLE dtm_base.users DROP COLUMN IF EXISTS is_admin;

-- Rename table back if needed
ALTER TABLE dtm_ads.daily_ad_reports RENAME TO ad_reports_daily;
```

## Post-Migration Tasks

1. **Update Application Configuration**

    - Verify database connection settings
    - Update any hardcoded table names in code

2. **Test Core Functionality**

    - Test budget tracking endpoints
    - Test admin approval workflow
    - Test file upload functionality
    - Test targeting system

3. **Performance Monitoring**

    - Monitor query performance with new indexes
    - Check for any slow queries
    - Verify analytics endpoints performance

4. **Data Validation**
    - Ensure existing data integrity
    - Test new constraint validations
    - Verify foreign key relationships

## Notes

-   All migrations are designed to be non-destructive
-   Existing data will be preserved
-   New columns are nullable or have defaults
-   Constraints are added safely with proper checks
-   The migration handles both old and new table names gracefully

## Support

If you encounter issues during migration:

1. Check PostgreSQL logs for detailed error messages
2. Verify user permissions for schema modifications
3. Ensure sufficient disk space for new tables and indexes
4. Run validation queries to identify specific issues

The migration scripts include extensive error handling and will skip operations that have already been completed, making them safe to re-run if needed.
