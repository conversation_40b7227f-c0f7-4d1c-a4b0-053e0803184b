# ========================
# Application Configuration
# ========================
API_BASE_URL=http://localhost:5000/api
APP_BASE_URL=http://localhost:3000
INTERNAL_API_KEY=WVcQAfUkPxMt3nGanMWWBLUIAaw8LJdoBffYbzzCCLs

NODE_ENV=development

# ========================
# Auth0 Configuration
# ========================
# Domain and Client Credentials
AUTH0_DOMAIN=dev-ji1jed8mnt28gmsw.us.auth0.com
AUTH0_CLIENT_ID=PJpgYLUC6wJVdylf9B0E0yFfqdaey3Bx
AUTH0_CLIENT_SECRET=****************************************************************
# Security
AUTH0_SECRET=****************************************************************
# Base URL
AUTH0_BASE_URL=http://localhost:3000
# Authentication Parameters
AUTH0_SCOPE=openid profile email
AUTH0_AUDIENCE=https://dtbash-api-4995fcf365cb.herokuapp.com/
AUTH0_ISSUER_BASE_URL=https://dev-ji1jed8mnt28gmsw.us.auth0.com
AUTH0_CLAIM_NAMESPACE=https://dtm.com
AUTH0_BASE_URL=http://localhost:3000

# ========================
# Stripe Configuration
# ========================
# Stripe Keys (Test Mode)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51Rf69YGafGoP3tL5cFQcLdeEwGZUDJGXbikwsjjRrG5ZeO6UGE1wwIrEktk1ZfXsKmAFy7kem1v4reFu4mAotlIl00r1g0lire
STRIPE_SECRET_KEY=sk_test_51Rf69YGafGoP3tL5GHCbbrP92X2QMVlsjFp4BLhONDyfNwLlmLK6sp0Scayjwkdb57QLJIzj3AVRpZKVnZqVhcak00DuT6GS21
STRIPE_WEBHOOK_SECRET=whsec_nbv8DykK1D2UqHgoxIE7UuCNxhqhdNm6
# Stripe Settings
STRIPE_CURRENCY=usd
STRIPE_COUNTRY=US
