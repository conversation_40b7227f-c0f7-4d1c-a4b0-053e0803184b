# Webhook Configuration for BTDash API
# Copy this file to .env.local and configure for your environment

# =============================================================================
# WEBHOOK BASE CONFIGURATION
# =============================================================================

# Base URL for webhook endpoints
# Development: Use ngrok URL when testing with external services
# Production: Use your production domain
WEBHOOK_BASE_URL=http://localhost:3001

# Ngrok URL (when using ngrok for local development)
# Example: https://abc123.ngrok.io
# NGROK_URL=

# Application base URL (fallback)
APP_BASE_URL=http://localhost:3001

# =============================================================================
# STRIPE WEBHOOK CONFIGURATION
# =============================================================================

# Stripe webhook endpoint secret (from Stripe Dashboard)
# This is different from your Stripe secret key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe API keys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# =============================================================================
# WEBHOOK SECURITY
# =============================================================================

# Internal API key for webhook management endpoints
# Generate a secure random string for production
INTERNAL_API_KEY=your_internal_api_key_here

# =============================================================================
# WEBHOOK RETRY CONFIGURATION
# =============================================================================

# Maximum number of webhook retry attempts
WEBHOOK_MAX_RETRIES=5

# Base delay for exponential backoff (milliseconds)
WEBHOOK_BASE_DELAY=1000

# Maximum delay between retries (milliseconds)
WEBHOOK_MAX_DELAY=30000

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Environment (development, staging, production)
NODE_ENV=development

# =============================================================================
# NGROK SETUP INSTRUCTIONS
# =============================================================================

# 1. Install ngrok: https://ngrok.com/download
# 2. Run your API server: npm run dev
# 3. In another terminal, run: ngrok http 3001
# 4. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
# 5. Set NGROK_URL in your .env.local file
# 6. Use the webhook initialization endpoint to setup Stripe webhooks

# Example ngrok command:
# ngrok http 3001 --subdomain=btdash-dev (requires paid ngrok account)

# =============================================================================
# WEBHOOK ENDPOINT URLS (Auto-generated)
# =============================================================================

# These URLs are automatically generated based on WEBHOOK_BASE_URL or NGROK_URL:
# - Stripe webhooks: {BASE_URL}/api/billing/webhooks/stripe
# - Health check: {BASE_URL}/api/billing/webhooks/health
# - Internal webhooks: {BASE_URL}/api/billing/webhooks/internal

# =============================================================================
# PRODUCTION CONSIDERATIONS
# =============================================================================

# For production deployment:
# 1. Use HTTPS URLs only
# 2. Set up proper SSL certificates
# 3. Configure firewall rules for webhook endpoints
# 4. Use environment-specific webhook secrets
# 5. Enable webhook signature verification
# 6. Set up monitoring and alerting for webhook failures
# 7. Configure backup webhook endpoints for redundancy
