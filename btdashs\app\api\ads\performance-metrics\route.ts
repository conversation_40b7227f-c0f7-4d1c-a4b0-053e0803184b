import { NextRequest, NextResponse } from "next/server";

interface AdPerformanceMetrics {
  slotId: number;
  paidAdSuccess: boolean;
  adsenseSuccess: boolean;
  loadTime: number;
  fallbackUsed: boolean;
  timestamp: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { metrics } = body;

    if (!Array.isArray(metrics)) {
      return NextResponse.json(
        { success: false, message: "Invalid request format" },
        { status: 400 }
      );
    }

    // Validate performance metrics
    const validMetrics = metrics.filter((metric: any) => {
      return (
        typeof metric.slotId === 'number' &&
        typeof metric.paidAdSuccess === 'boolean' &&
        typeof metric.adsenseSuccess === 'boolean' &&
        typeof metric.loadTime === 'number' &&
        typeof metric.fallbackUsed === 'boolean' &&
        metric.timestamp
      );
    });

    if (validMetrics.length === 0) {
      return NextResponse.json(
        { success: false, message: "No valid performance metrics provided" },
        { status: 400 }
      );
    }

    // Analyze performance metrics
    const analysis = analyzePerformanceMetrics(validMetrics);

    // Log performance data
    console.log('Ad Performance Metrics Received:', {
      count: validMetrics.length,
      analysis,
      timestamp: new Date().toISOString(),
    });

    // Optional: Send to analytics service
    if (process.env.NODE_ENV === 'production') {
      await sendToAnalyticsService(validMetrics, analysis);
    }

    // Optional: Store in database for analysis
    if (process.env.STORE_AD_METRICS === 'true') {
      await storePerformanceMetrics(validMetrics);
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${validMetrics.length} metric(s)`,
      processed: validMetrics.length,
      analysis,
    });

  } catch (error) {
    console.error('Error processing ad performance metrics:', error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

async function sendToAnalyticsService(metrics: AdPerformanceMetrics[], analysis: any): Promise<void> {
  try {
    // Example: Send to analytics service (e.g., Google Analytics, Mixpanel, etc.)
    if (process.env.ANALYTICS_WEBHOOK_URL) {
      await fetch(process.env.ANALYTICS_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`,
        },
        body: JSON.stringify({
          source: 'btdash-ad-performance',
          metrics,
          analysis,
          timestamp: new Date().toISOString(),
        }),
      });
    }
  } catch (error) {
    console.error('Failed to send to analytics service:', error);
  }
}

async function storePerformanceMetrics(metrics: AdPerformanceMetrics[]): Promise<void> {
  try {
    const API_BASE = process.env.API_BASE_URL;
    const INTERNAL_API_KEY = process.env.INTERNAL_API_KEY;

    if (API_BASE && INTERNAL_API_KEY) {
      await fetch(`${API_BASE}/ad-performance-metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-internal-api-key': INTERNAL_API_KEY,
        },
        body: JSON.stringify({ metrics }),
      });
    }
  } catch (error) {
    console.error('Failed to store performance metrics in database:', error);
  }
}

function analyzePerformanceMetrics(metrics: AdPerformanceMetrics[]): any {
  const analysis = {
    totalRequests: metrics.length,
    paidAdSuccessRate: 0,
    adsenseSuccessRate: 0,
    fallbackRate: 0,
    averageLoadTime: 0,
    loadTimePercentiles: {
      p50: 0,
      p90: 0,
      p95: 0,
      p99: 0,
    },
    performanceBySlot: {} as Record<number, {
      requests: number;
      paidAdSuccess: number;
      adsenseSuccess: number;
      averageLoadTime: number;
      fallbackRate: number;
    }>,
    timeRange: {
      start: metrics[0]?.timestamp,
      end: metrics[metrics.length - 1]?.timestamp,
    },
  };

  if (metrics.length === 0) return analysis;

  // Calculate success rates
  const paidAdSuccesses = metrics.filter(m => m.paidAdSuccess).length;
  const adsenseSuccesses = metrics.filter(m => m.adsenseSuccess).length;
  const fallbacks = metrics.filter(m => m.fallbackUsed).length;

  analysis.paidAdSuccessRate = (paidAdSuccesses / metrics.length) * 100;
  analysis.adsenseSuccessRate = (adsenseSuccesses / metrics.length) * 100;
  analysis.fallbackRate = (fallbacks / metrics.length) * 100;

  // Calculate load time statistics
  const loadTimes = metrics.map(m => m.loadTime).sort((a, b) => a - b);
  analysis.averageLoadTime = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;

  // Calculate percentiles
  analysis.loadTimePercentiles.p50 = loadTimes[Math.floor(loadTimes.length * 0.5)];
  analysis.loadTimePercentiles.p90 = loadTimes[Math.floor(loadTimes.length * 0.9)];
  analysis.loadTimePercentiles.p95 = loadTimes[Math.floor(loadTimes.length * 0.95)];
  analysis.loadTimePercentiles.p99 = loadTimes[Math.floor(loadTimes.length * 0.99)];

  // Analyze by slot
  metrics.forEach(metric => {
    if (!analysis.performanceBySlot[metric.slotId]) {
      analysis.performanceBySlot[metric.slotId] = {
        requests: 0,
        paidAdSuccess: 0,
        adsenseSuccess: 0,
        averageLoadTime: 0,
        fallbackRate: 0,
      };
    }

    const slotData = analysis.performanceBySlot[metric.slotId];
    slotData.requests++;
    if (metric.paidAdSuccess) slotData.paidAdSuccess++;
    if (metric.adsenseSuccess) slotData.adsenseSuccess++;
    if (metric.fallbackUsed) slotData.fallbackRate++;
  });

  // Calculate averages for each slot
  Object.keys(analysis.performanceBySlot).forEach(slotId => {
    const slotData = analysis.performanceBySlot[parseInt(slotId)];
    const slotMetrics = metrics.filter(m => m.slotId === parseInt(slotId));
    
    slotData.averageLoadTime = slotMetrics.reduce((sum, m) => sum + m.loadTime, 0) / slotMetrics.length;
    slotData.fallbackRate = (slotData.fallbackRate / slotData.requests) * 100;
  });

  return analysis;
}
