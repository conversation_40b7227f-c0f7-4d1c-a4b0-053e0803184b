import { loadStripe, Stripe } from "@stripe/stripe-js";

// Types for configuration validation
interface StripeConfigValidation {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

interface StripeEnvironment {
	publishableKey: string | undefined;
	secretKey: string | undefined;
	webhookSecret: string | undefined;
	currency: string;
	country: string;
	isTestMode: boolean;
}

// Client-side Stripe instance
let stripePromise: Promise<Stripe | null>;

/**
 * Get client-side Stripe instance with validation
 */
export const getStripe = () => {
	if (!stripePromise) {
		const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

		if (!publishableKey) {
			throw new Error(
				"Missing Stripe publishable key. Please set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY in your environment variables."
			);
		}

		// Validate publishable key format
		if (!publishableKey.startsWith("pk_")) {
			throw new Error('Invalid Stripe publishable key format. Key should start with "pk_test_" or "pk_live_".');
		}

		stripePromise = loadStripe(publishableKey);
	}

	return stripePromise;
};

/**
 * Get Stripe environment configuration
 */
export const getStripeEnvironment = (): StripeEnvironment => {
	const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
	const secretKey = process.env.STRIPE_SECRET_KEY;
	const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
	const currency = process.env.STRIPE_CURRENCY || "usd";
	const country = process.env.STRIPE_COUNTRY || "US";

	// Determine if we're in test mode
	const isTestMode = publishableKey?.startsWith("pk_test_") || secretKey?.startsWith("sk_test_") || false;

	return {
		publishableKey,
		secretKey,
		webhookSecret,
		currency,
		country,
		isTestMode,
	};
};

/**
 * Validate Stripe configuration
 */
export const validateStripeConfig = (): StripeConfigValidation => {
	const errors: string[] = [];
	const warnings: string[] = [];
	const env = getStripeEnvironment();

	// Required environment variables
	if (!env.publishableKey) {
		errors.push("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required");
	} else if (!env.publishableKey.startsWith("pk_")) {
		errors.push('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY must start with "pk_test_" or "pk_live_"');
	}

	if (!env.secretKey) {
		errors.push("STRIPE_SECRET_KEY is required");
	} else if (!env.secretKey.startsWith("sk_")) {
		errors.push('STRIPE_SECRET_KEY must start with "sk_test_" or "sk_live_"');
	}

	if (!env.webhookSecret) {
		errors.push("STRIPE_WEBHOOK_SECRET is required");
	} else if (!env.webhookSecret.startsWith("whsec_")) {
		errors.push('STRIPE_WEBHOOK_SECRET must start with "whsec_"');
	}

	// Key consistency validation
	if (env.publishableKey && env.secretKey) {
		const pubKeyIsTest = env.publishableKey.startsWith("pk_test_");
		const secretKeyIsTest = env.secretKey.startsWith("sk_test_");

		if (pubKeyIsTest !== secretKeyIsTest) {
			errors.push("Stripe publishable key and secret key must both be test keys or both be live keys");
		}
	}

	// Currency validation
	const validCurrencies = ["usd", "eur", "gbp", "cad", "aud", "jpy"];
	if (!validCurrencies.includes(env.currency.toLowerCase())) {
		warnings.push(
			`Currency "${env.currency}" may not be supported. Common currencies: ${validCurrencies.join(", ")}`
		);
	}

	// Environment warnings
	if (env.isTestMode && process.env.NODE_ENV === "production") {
		warnings.push("Using Stripe test keys in production environment");
	}

	if (!env.isTestMode && process.env.NODE_ENV === "development") {
		warnings.push("Using Stripe live keys in development environment");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
};

// Server-side Stripe configuration
export const stripeConfig = {
	secretKey: process.env.STRIPE_SECRET_KEY,
	webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
	currency: process.env.STRIPE_CURRENCY || "usd",
	country: process.env.STRIPE_COUNTRY || "US",
};

/**
 * Log configuration validation results
 */
export const logStripeConfigValidation = () => {
	const validation = validateStripeConfig();

	if (!validation.isValid) {
		console.error('❌ Stripe Configuration Errors:');
		validation.errors.forEach(error => console.error(`  - ${error}`));
	}

	if (validation.warnings.length > 0) {
		console.warn('⚠️  Stripe Configuration Warnings:');
		validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
	}

	if (validation.isValid && validation.warnings.length === 0) {
		console.log('✅ Stripe configuration is valid');
	}

	return validation;
};
		throw new Error("Missing Stripe secret key");
	}

	if (!stripeConfig.webhookSecret) {
		throw new Error("Missing Stripe webhook secret");
	}

	return true;
};
