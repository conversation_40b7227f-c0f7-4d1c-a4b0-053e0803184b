const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const logger = require("../../../logger");

class WebhookConfigService {
	constructor() {
		this.webhookEndpoints = new Map();
		this.requiredEvents = [
			'payment_intent.succeeded',
			'payment_intent.payment_failed',
			'payment_intent.processing',
			'payment_intent.requires_action',
			'invoice.payment_succeeded',
			'invoice.payment_failed',
			'customer.subscription.created',
			'customer.subscription.updated',
			'customer.subscription.deleted',
		];
	}

	/**
	 * Get the webhook base URL based on environment
	 * @returns {string} Base URL for webhooks
	 */
	getWebhookBaseUrl() {
		const environment = process.env.NODE_ENV || 'development';
		
		if (environment === 'production') {
			return process.env.WEBHOOK_BASE_URL || process.env.APP_BASE_URL;
		}
		
		// For development, check if ngrok URL is provided
		if (process.env.NGROK_URL) {
			return process.env.NGROK_URL;
		}
		
		// Fallback to local development URL
		return process.env.WEBHOOK_BASE_URL || 'http://localhost:3001';
	}

	/**
	 * Generate webhook endpoint URLs
	 * @returns {Object} Webhook endpoint URLs
	 */
	getWebhookEndpoints() {
		const baseUrl = this.getWebhookBaseUrl();
		
		return {
			stripe: `${baseUrl}/api/billing/webhooks/stripe`,
			internal: `${baseUrl}/api/billing/webhooks/internal`,
			health: `${baseUrl}/api/billing/webhooks/health`,
		};
	}

	/**
	 * Create or update Stripe webhook endpoint
	 * @param {Object} options - Webhook configuration options
	 * @returns {Promise<Object>} Webhook endpoint object
	 */
	async setupStripeWebhook(options = {}) {
		try {
			const endpoints = this.getWebhookEndpoints();
			const {
				url = endpoints.stripe,
				events = this.requiredEvents,
				description = 'BTDash Payment Webhooks',
				enabled = true,
			} = options;

			// Check if webhook endpoint already exists
			const existingEndpoints = await stripe.webhookEndpoints.list({
				limit: 100,
			});

			const existingEndpoint = existingEndpoints.data.find(
				endpoint => endpoint.url === url
			);

			if (existingEndpoint) {
				logger.info("Updating existing Stripe webhook endpoint", {
					id: existingEndpoint.id,
					url: existingEndpoint.url,
				});

				const updatedEndpoint = await stripe.webhookEndpoints.update(
					existingEndpoint.id,
					{
						enabled_events: events,
						description,
						disabled: !enabled,
					}
				);

				this.webhookEndpoints.set('stripe', updatedEndpoint);
				return updatedEndpoint;
			} else {
				logger.info("Creating new Stripe webhook endpoint", { url, events });

				const newEndpoint = await stripe.webhookEndpoints.create({
					url,
					enabled_events: events,
					description,
					disabled: !enabled,
				});

				this.webhookEndpoints.set('stripe', newEndpoint);
				return newEndpoint;
			}
		} catch (error) {
			logger.error("Failed to setup Stripe webhook", {
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Verify webhook endpoint is accessible
	 * @param {string} url - Webhook URL to verify
	 * @returns {Promise<boolean>} Whether endpoint is accessible
	 */
	async verifyWebhookEndpoint(url) {
		try {
			const response = await fetch(`${url}/health`, {
				method: 'GET',
				timeout: 5000,
			});

			return response.ok;
		} catch (error) {
			logger.warn("Webhook endpoint verification failed", {
				url,
				error: error.message,
			});
			return false;
		}
	}

	/**
	 * Get webhook endpoint status
	 * @returns {Promise<Object>} Webhook status information
	 */
	async getWebhookStatus() {
		try {
			const endpoints = this.getWebhookEndpoints();
			const stripeEndpoints = await stripe.webhookEndpoints.list();
			
			const status = {
				environment: process.env.NODE_ENV || 'development',
				baseUrl: this.getWebhookBaseUrl(),
				endpoints: endpoints,
				stripe: {
					configured: stripeEndpoints.data.length > 0,
					endpoints: stripeEndpoints.data.map(endpoint => ({
						id: endpoint.id,
						url: endpoint.url,
						status: endpoint.status,
						enabled_events: endpoint.enabled_events,
						created: endpoint.created,
					})),
				},
				health: {},
			};

			// Check endpoint health
			for (const [name, url] of Object.entries(endpoints)) {
				status.health[name] = await this.verifyWebhookEndpoint(url);
			}

			return status;
		} catch (error) {
			logger.error("Failed to get webhook status", {
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Delete webhook endpoint
	 * @param {string} endpointId - Stripe webhook endpoint ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteStripeWebhook(endpointId) {
		try {
			await stripe.webhookEndpoints.del(endpointId);
			logger.info("Deleted Stripe webhook endpoint", { endpointId });
			return true;
		} catch (error) {
			logger.error("Failed to delete Stripe webhook", {
				endpointId,
				error: error.message,
			});
			return false;
		}
	}

	/**
	 * List all webhook endpoints
	 * @returns {Promise<Array>} List of webhook endpoints
	 */
	async listStripeWebhooks() {
		try {
			const endpoints = await stripe.webhookEndpoints.list({
				limit: 100,
			});
			return endpoints.data;
		} catch (error) {
			logger.error("Failed to list Stripe webhooks", {
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Test webhook endpoint with a test event
	 * @param {string} url - Webhook URL to test
	 * @returns {Promise<boolean>} Test result
	 */
	async testWebhookEndpoint(url) {
		try {
			const testPayload = {
				id: 'evt_test_webhook',
				object: 'event',
				api_version: '2020-08-27',
				created: Math.floor(Date.now() / 1000),
				data: {
					object: {
						id: 'pi_test_webhook',
						object: 'payment_intent',
						status: 'succeeded',
						amount: 1000,
						currency: 'usd',
					},
				},
				livemode: false,
				pending_webhooks: 1,
				request: {
					id: 'req_test_webhook',
					idempotency_key: null,
				},
				type: 'payment_intent.succeeded',
			};

			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Stripe-Signature': 'test_signature',
				},
				body: JSON.stringify(testPayload),
				timeout: 10000,
			});

			const success = response.ok;
			
			logger.info("Webhook endpoint test completed", {
				url,
				success,
				status: response.status,
			});

			return success;
		} catch (error) {
			logger.error("Webhook endpoint test failed", {
				url,
				error: error.message,
			});
			return false;
		}
	}

	/**
	 * Get webhook configuration for environment
	 * @returns {Object} Webhook configuration
	 */
	getWebhookConfig() {
		return {
			environment: process.env.NODE_ENV || 'development',
			baseUrl: this.getWebhookBaseUrl(),
			endpoints: this.getWebhookEndpoints(),
			events: this.requiredEvents,
			stripeSecretKey: process.env.STRIPE_SECRET_KEY ? 'configured' : 'missing',
			webhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? 'configured' : 'missing',
		};
	}
}

module.exports = WebhookConfigService;
