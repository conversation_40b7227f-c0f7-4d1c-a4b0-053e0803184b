const express = require("express");
const router = express.Router();
const { checkJwt, checkInternalKey } = require("../../middleware/auth");
const rateLimits = require("../../middleware/rateLimitMiddleware");
const billingController = require("../controllers/billingController");

// Payment intent management
router.post("/payment-intent", checkJwt, rateLimits.userGeneral, billingController.storePaymentIntent);

// Payment processing webhooks (internal API calls)
router.post("/payment-success", checkInternalKey, rateLimits.ipStrict, billingController.handlePaymentSuccess);
router.post("/payment-failed", checkInternalKey, rateLimits.ipStrict, billingController.handlePaymentFailed);

// Campaign activation
router.post("/campaigns/:id/activate", checkInternalKey, rateLimits.ipStrict, billingController.activateCampaign);

// Payment retry
router.post("/campaigns/:id/retry-payment", checkJwt, rateLimits.userCampaigns, billingController.retryPayment);

// Analytics and monitoring
router.get("/analytics", checkJwt, rateLimits.userGeneral, billingController.getPaymentAnalytics);
router.get("/health-report", checkJwt, rateLimits.userGeneral, billingController.getPaymentHealthReport);
router.get("/webhook-stats", checkJwt, rateLimits.userGeneral, billingController.getWebhookStats);

// Webhook retry (admin only)
router.post(
	"/webhooks/:webhookLogId/retry",
	checkInternalKey,
	rateLimits.ipStrict,
	billingController.retryFailedWebhook
);

module.exports = router;
