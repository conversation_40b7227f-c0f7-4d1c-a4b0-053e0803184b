const express = require("express");
const reconciliationController = require("../controllers/reconciliationController");
const { checkJwt } = require("../../middleware/auth");
const rateLimits = require("../../middleware/rateLimits");

const router = express.Router();

/**
 * POST /api/reconciliation/run
 * Run payment reconciliation for specified period
 * Admin only
 */
router.post("/run", checkJwt, rateLimits.adminGeneral, reconciliationController.runReconciliation);

/**
 * GET /api/reconciliation/stats
 * Get reconciliation statistics and summary
 * Admin only
 */
router.get("/stats", checkJwt, rateLimits.adminGeneral, reconciliationController.getReconciliationStats);

/**
 * POST /api/reconciliation/quick-check
 * Quick reconciliation check for last 24 hours
 * Admin only
 */
router.post("/quick-check", checkJwt, rateLimits.adminGeneral, reconciliationController.quickReconciliationCheck);

module.exports = router;
