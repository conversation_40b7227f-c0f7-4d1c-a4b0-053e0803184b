"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth0";
exports.ids = ["vendor-chunks/@auth0"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/helpers/get-access-token.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/client/helpers/get-access-token.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken)\n/* harmony export */ });\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/errors/index.js\");\n\nasync function getAccessToken() {\n    const tokenRes = await fetch(process.env.NEXT_PUBLIC_ACCESS_TOKEN_ROUTE || \"/auth/access-token\");\n    if (!tokenRes.ok) {\n        // try to parse it as JSON and throw the error from the API\n        // otherwise, throw a generic error\n        let accessTokenError;\n        try {\n            accessTokenError = await tokenRes.json();\n        }\n        catch (e) {\n            throw new Error(\"An unexpected error occurred while trying to fetch the access token.\");\n        }\n        throw new _errors__WEBPACK_IMPORTED_MODULE_0__.AccessTokenError(accessTokenError.error.code, accessTokenError.error.message);\n    }\n    const tokenSet = await tokenRes.json();\n    return tokenSet.token;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L2NsaWVudC9oZWxwZXJzL2dldC1hY2Nlc3MtdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDekM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHFEQUFnQjtBQUNsQztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXEBhdXRoMFxcbmV4dGpzLWF1dGgwXFxkaXN0XFxjbGllbnRcXGhlbHBlcnNcXGdldC1hY2Nlc3MtdG9rZW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQWNjZXNzVG9rZW5FcnJvciB9IGZyb20gXCIuLi8uLi9lcnJvcnNcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBY2Nlc3NUb2tlbigpIHtcbiAgICBjb25zdCB0b2tlblJlcyA9IGF3YWl0IGZldGNoKHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FDQ0VTU19UT0tFTl9ST1VURSB8fCBcIi9hdXRoL2FjY2Vzcy10b2tlblwiKTtcbiAgICBpZiAoIXRva2VuUmVzLm9rKSB7XG4gICAgICAgIC8vIHRyeSB0byBwYXJzZSBpdCBhcyBKU09OIGFuZCB0aHJvdyB0aGUgZXJyb3IgZnJvbSB0aGUgQVBJXG4gICAgICAgIC8vIG90aGVyd2lzZSwgdGhyb3cgYSBnZW5lcmljIGVycm9yXG4gICAgICAgIGxldCBhY2Nlc3NUb2tlbkVycm9yO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgYWNjZXNzVG9rZW5FcnJvciA9IGF3YWl0IHRva2VuUmVzLmpzb24oKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCB3aGlsZSB0cnlpbmcgdG8gZmV0Y2ggdGhlIGFjY2VzcyB0b2tlbi5cIik7XG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgbmV3IEFjY2Vzc1Rva2VuRXJyb3IoYWNjZXNzVG9rZW5FcnJvci5lcnJvci5jb2RlLCBhY2Nlc3NUb2tlbkVycm9yLmVycm9yLm1lc3NhZ2UpO1xuICAgIH1cbiAgICBjb25zdCB0b2tlblNldCA9IGF3YWl0IHRva2VuUmVzLmpzb24oKTtcbiAgICByZXR1cm4gdG9rZW5TZXQudG9rZW47XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/helpers/get-access-token.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js":
/*!************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useUser: () => (/* binding */ useUser)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useUser = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\G_PROG\\Nicolas\\btdash-ecosystem\\btdash-admanager\\node_modules\\@auth0\\nextjs-auth0\\dist\\client\\hooks\\use-user.js",
"useUser",
);

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/client/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Auth0Provider: () => (/* reexport safe */ _providers_auth0_provider__WEBPACK_IMPORTED_MODULE_2__.Auth0Provider),\n/* harmony export */   getAccessToken: () => (/* reexport safe */ _helpers_get_access_token__WEBPACK_IMPORTED_MODULE_1__.getAccessToken),\n/* harmony export */   useUser: () => (/* reexport safe */ _hooks_use_user__WEBPACK_IMPORTED_MODULE_0__.useUser)\n/* harmony export */ });\n/* harmony import */ var _hooks_use_user__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/use-user */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js\");\n/* harmony import */ var _helpers_get_access_token__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers/get-access-token */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/helpers/get-access-token.js\");\n/* harmony import */ var _providers_auth0_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers/auth0-provider */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L2NsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDaUI7QUFDRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXEBhdXRoMFxcbmV4dGpzLWF1dGgwXFxkaXN0XFxjbGllbnRcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHVzZVVzZXIgfSBmcm9tIFwiLi9ob29rcy91c2UtdXNlclwiO1xuZXhwb3J0IHsgZ2V0QWNjZXNzVG9rZW4gfSBmcm9tIFwiLi9oZWxwZXJzL2dldC1hY2Nlc3MtdG9rZW5cIjtcbmV4cG9ydCB7IEF1dGgwUHJvdmlkZXIgfSBmcm9tIFwiLi9wcm92aWRlcnMvYXV0aDAtcHJvdmlkZXJcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Auth0Provider: () => (/* binding */ Auth0Provider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Auth0Provider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Auth0Provider() from the server but Auth0Provider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\G_PROG\\Nicolas\\btdash-ecosystem\\btdash-admanager\\node_modules\\@auth0\\nextjs-auth0\\dist\\client\\providers\\auth0-provider.js",
"Auth0Provider",
);

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/errors/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/errors/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessTokenError: () => (/* binding */ AccessTokenError),\n/* harmony export */   AccessTokenErrorCode: () => (/* binding */ AccessTokenErrorCode),\n/* harmony export */   AccessTokenForConnectionError: () => (/* binding */ AccessTokenForConnectionError),\n/* harmony export */   AccessTokenForConnectionErrorCode: () => (/* binding */ AccessTokenForConnectionErrorCode),\n/* harmony export */   AuthorizationCodeGrantError: () => (/* binding */ AuthorizationCodeGrantError),\n/* harmony export */   AuthorizationError: () => (/* binding */ AuthorizationError),\n/* harmony export */   BackchannelLogoutError: () => (/* binding */ BackchannelLogoutError),\n/* harmony export */   DiscoveryError: () => (/* binding */ DiscoveryError),\n/* harmony export */   InvalidStateError: () => (/* binding */ InvalidStateError),\n/* harmony export */   MissingStateError: () => (/* binding */ MissingStateError),\n/* harmony export */   OAuth2Error: () => (/* binding */ OAuth2Error),\n/* harmony export */   SdkError: () => (/* binding */ SdkError)\n/* harmony export */ });\nclass SdkError extends Error {\n}\n/**\n * Errors that come from Auth0 in the `redirect_uri` callback may contain reflected user input via the OpenID Connect `error` and `error_description` query parameter.\n * You should **not** render the error `message`, or `error` and `error_description` properties without properly escaping them first.\n */\nclass OAuth2Error extends SdkError {\n    constructor({ code, message }) {\n        super(message ??\n            \"An error occured while interacting with the authorization server.\");\n        this.name = \"OAuth2Error\";\n        this.code = code;\n    }\n}\nclass DiscoveryError extends SdkError {\n    constructor(message) {\n        super(message ?? \"Discovery failed for the OpenID Connect configuration.\");\n        this.code = \"discovery_error\";\n        this.name = \"DiscoveryError\";\n    }\n}\nclass MissingStateError extends SdkError {\n    constructor(message) {\n        super(message ?? \"The state parameter is missing.\");\n        this.code = \"missing_state\";\n        this.name = \"MissingStateError\";\n    }\n}\nclass InvalidStateError extends SdkError {\n    constructor(message) {\n        super(message ?? \"The state parameter is invalid.\");\n        this.code = \"invalid_state\";\n        this.name = \"InvalidStateError\";\n    }\n}\nclass AuthorizationError extends SdkError {\n    constructor({ cause, message }) {\n        super(message ?? \"An error occured during the authorization flow.\");\n        this.code = \"authorization_error\";\n        this.cause = cause;\n        this.name = \"AuthorizationError\";\n    }\n}\nclass AuthorizationCodeGrantError extends SdkError {\n    constructor({ cause, message }) {\n        super(message ??\n            \"An error occured while trying to exchange the authorization code.\");\n        this.code = \"authorization_code_grant_error\";\n        this.cause = cause;\n        this.name = \"AuthorizationCodeGrantError\";\n    }\n}\nclass BackchannelLogoutError extends SdkError {\n    constructor(message) {\n        super(message ??\n            \"An error occured while completing the backchannel logout request.\");\n        this.code = \"backchannel_logout_error\";\n        this.name = \"BackchannelLogoutError\";\n    }\n}\nvar AccessTokenErrorCode;\n(function (AccessTokenErrorCode) {\n    AccessTokenErrorCode[\"MISSING_SESSION\"] = \"missing_session\";\n    AccessTokenErrorCode[\"MISSING_REFRESH_TOKEN\"] = \"missing_refresh_token\";\n    AccessTokenErrorCode[\"FAILED_TO_REFRESH_TOKEN\"] = \"failed_to_refresh_token\";\n})(AccessTokenErrorCode || (AccessTokenErrorCode = {}));\nclass AccessTokenError extends SdkError {\n    constructor(code, message) {\n        super(message);\n        this.name = \"AccessTokenError\";\n        this.code = code;\n    }\n}\n/**\n * Enum representing error codes related to access tokens for connections.\n */\nvar AccessTokenForConnectionErrorCode;\n(function (AccessTokenForConnectionErrorCode) {\n    /**\n     * The session is missing.\n     */\n    AccessTokenForConnectionErrorCode[\"MISSING_SESSION\"] = \"missing_session\";\n    /**\n     * The refresh token is missing.\n     */\n    AccessTokenForConnectionErrorCode[\"MISSING_REFRESH_TOKEN\"] = \"missing_refresh_token\";\n    /**\n     * Failed to exchange the refresh token.\n     */\n    AccessTokenForConnectionErrorCode[\"FAILED_TO_EXCHANGE\"] = \"failed_to_exchange_refresh_token\";\n})(AccessTokenForConnectionErrorCode || (AccessTokenForConnectionErrorCode = {}));\n/**\n * Error class representing an access token for connection error.\n * Extends the `SdkError` class.\n */\nclass AccessTokenForConnectionError extends SdkError {\n    /**\n     * Constructs a new `AccessTokenForConnectionError` instance.\n     *\n     * @param code - The error code.\n     * @param message - The error message.\n     * @param cause - The OAuth2 cause of the error.\n     */\n    constructor(code, message, cause) {\n        super(message);\n        this.name = \"AccessTokenForConnectionError\";\n        this.code = code;\n        this.cause = cause;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/errors/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/auth-client.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/auth-client.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthClient: () => (/* binding */ AuthClient)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwks/remote.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/verify.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var oauth4webapi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! oauth4webapi */ \"(rsc)/./node_modules/oauth4webapi/build/index.js\");\n/* harmony import */ var _package_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/package.json\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/errors/index.js\");\n/* harmony import */ var _utils_pathUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/pathUtils */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/utils/pathUtils.js\");\n/* harmony import */ var _utils_url_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/url-helpers */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/utils/url-helpers.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cookies */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js\");\n/* harmony import */ var _user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./user */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/user.js\");\n\n\n\n\n\n\n\n\n\n// params passed to the /authorize endpoint that cannot be overwritten\nconst INTERNAL_AUTHORIZE_PARAMS = [\n    \"client_id\",\n    \"redirect_uri\",\n    \"response_type\",\n    \"code_challenge\",\n    \"code_challenge_method\",\n    \"state\",\n    \"nonce\"\n];\nconst DEFAULT_SCOPES = [\"openid\", \"profile\", \"email\", \"offline_access\"].join(\" \");\n/**\n * A constant representing the grant type for federated connection access token exchange.\n *\n * This grant type is used in OAuth token exchange scenarios where a federated connection\n * access token is required. It is specific to Auth0's implementation and follows the\n * \"urn:auth0:params:oauth:grant-type:token-exchange:federated-connection-access-token\" format.\n */\nconst GRANT_TYPE_FEDERATED_CONNECTION_ACCESS_TOKEN = \"urn:auth0:params:oauth:grant-type:token-exchange:federated-connection-access-token\";\n/**\n * Constant representing the subject type for a refresh token.\n * This is used in OAuth 2.0 token exchange to specify that the token being exchanged is a refresh token.\n *\n * @see {@link https://tools.ietf.org/html/rfc8693#section-3.1 RFC 8693 Section 3.1}\n */\nconst SUBJECT_TYPE_REFRESH_TOKEN = \"urn:ietf:params:oauth:token-type:refresh_token\";\n/**\n * A constant representing the token type for federated connection access tokens.\n * This is used to specify the type of token being requested from Auth0.\n *\n * @constant\n * @type {string}\n */\nconst REQUESTED_TOKEN_TYPE_FEDERATED_CONNECTION_ACCESS_TOKEN = \"http://auth0.com/oauth/token-type/federated-connection-access-token\";\nfunction createRouteUrl(url, base) {\n    return new URL((0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_3__.ensureNoLeadingSlash)(url), (0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_3__.ensureTrailingSlash)(base));\n}\nclass AuthClient {\n    constructor(options) {\n        // dependencies\n        this.fetch = options.fetch || fetch;\n        this.jwksCache = options.jwksCache || {};\n        this.allowInsecureRequests = options.allowInsecureRequests ?? false;\n        this.httpOptions = () => {\n            const headers = new Headers();\n            const enableTelemetry = options.enableTelemetry ?? true;\n            const timeout = options.httpTimeout ?? 5000;\n            if (enableTelemetry) {\n                const name = \"nextjs-auth0\";\n                const version = _package_json__WEBPACK_IMPORTED_MODULE_1__.version;\n                headers.set(\"User-Agent\", `${name}/${version}`);\n                headers.set(\"Auth0-Client\", encodeBase64(JSON.stringify({\n                    name,\n                    version\n                })));\n            }\n            return {\n                signal: AbortSignal.timeout(timeout),\n                headers\n            };\n        };\n        if (this.allowInsecureRequests && \"development\" === \"production\") {}\n        // stores\n        this.transactionStore = options.transactionStore;\n        this.sessionStore = options.sessionStore;\n        // authorization server\n        this.domain = options.domain;\n        this.clientMetadata = { client_id: options.clientId };\n        this.clientSecret = options.clientSecret;\n        this.authorizationParameters = options.authorizationParameters || {\n            scope: DEFAULT_SCOPES\n        };\n        this.pushedAuthorizationRequests =\n            options.pushedAuthorizationRequests ?? false;\n        this.clientAssertionSigningKey = options.clientAssertionSigningKey;\n        this.clientAssertionSigningAlg =\n            options.clientAssertionSigningAlg || \"RS256\";\n        if (!this.authorizationParameters.scope) {\n            this.authorizationParameters.scope = DEFAULT_SCOPES;\n        }\n        const scope = this.authorizationParameters.scope\n            .split(\" \")\n            .map((s) => s.trim());\n        if (!scope.includes(\"openid\")) {\n            throw new Error(\"The 'openid' scope must be included in the set of scopes. See https://auth0.com/docs\");\n        }\n        // application\n        this.appBaseUrl = options.appBaseUrl;\n        this.signInReturnToPath = options.signInReturnToPath || \"/\";\n        // hooks\n        this.beforeSessionSaved = options.beforeSessionSaved;\n        this.onCallback = options.onCallback || this.defaultOnCallback;\n        // routes\n        this.routes = {\n            login: \"/auth/login\",\n            logout: \"/auth/logout\",\n            callback: \"/auth/callback\",\n            backChannelLogout: \"/auth/backchannel-logout\",\n            profile: process.env.NEXT_PUBLIC_PROFILE_ROUTE || \"/auth/profile\",\n            accessToken: process.env.NEXT_PUBLIC_ACCESS_TOKEN_ROUTE || \"/auth/access-token\",\n            ...options.routes\n        };\n        this.enableAccessTokenEndpoint = options.enableAccessTokenEndpoint ?? true;\n    }\n    async handler(req) {\n        const { pathname } = req.nextUrl;\n        const sanitizedPathname = (0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_3__.removeTrailingSlash)(pathname);\n        const method = req.method;\n        if (method === \"GET\" && sanitizedPathname === this.routes.login) {\n            return this.handleLogin(req);\n        }\n        else if (method === \"GET\" && sanitizedPathname === this.routes.logout) {\n            return this.handleLogout(req);\n        }\n        else if (method === \"GET\" && sanitizedPathname === this.routes.callback) {\n            return this.handleCallback(req);\n        }\n        else if (method === \"GET\" && sanitizedPathname === this.routes.profile) {\n            return this.handleProfile(req);\n        }\n        else if (method === \"GET\" &&\n            sanitizedPathname === this.routes.accessToken &&\n            this.enableAccessTokenEndpoint) {\n            return this.handleAccessToken(req);\n        }\n        else if (method === \"POST\" &&\n            sanitizedPathname === this.routes.backChannelLogout) {\n            return this.handleBackChannelLogout(req);\n        }\n        else {\n            // no auth handler found, simply touch the sessions\n            // TODO: this should only happen if rolling sessions are enabled. Also, we should\n            // try to avoid reading from the DB (for stateful sessions) on every request if possible.\n            const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            const session = await this.sessionStore.get(req.cookies);\n            if (session) {\n                // we pass the existing session (containing an `createdAt` timestamp) to the set method\n                // which will update the cookie's `maxAge` property based on the `createdAt` time\n                await this.sessionStore.set(req.cookies, res.cookies, {\n                    ...session\n                });\n                (0,_cookies__WEBPACK_IMPORTED_MODULE_5__.addCacheControlHeadersForSession)(res);\n            }\n            return res;\n        }\n    }\n    async startInteractiveLogin(options = {}) {\n        const redirectUri = createRouteUrl(this.routes.callback, this.appBaseUrl); // must be registed with the authorization server\n        let returnTo = this.signInReturnToPath;\n        // Validate returnTo parameter\n        if (options.returnTo) {\n            const safeBaseUrl = new URL(this.authorizationParameters.redirect_uri ||\n                this.appBaseUrl);\n            const sanitizedReturnTo = (0,_utils_url_helpers__WEBPACK_IMPORTED_MODULE_4__.toSafeRedirect)(options.returnTo, safeBaseUrl);\n            if (sanitizedReturnTo) {\n                returnTo = sanitizedReturnTo;\n            }\n        }\n        // Generate PKCE challenges\n        const codeChallengeMethod = \"S256\";\n        const codeVerifier = oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.generateRandomCodeVerifier();\n        const codeChallenge = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.calculatePKCECodeChallenge(codeVerifier);\n        const state = oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.generateRandomState();\n        const nonce = oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.generateRandomNonce();\n        // Construct base authorization parameters\n        const authorizationParams = new URLSearchParams();\n        authorizationParams.set(\"client_id\", this.clientMetadata.client_id);\n        authorizationParams.set(\"redirect_uri\", redirectUri.toString());\n        authorizationParams.set(\"response_type\", \"code\");\n        authorizationParams.set(\"code_challenge\", codeChallenge);\n        authorizationParams.set(\"code_challenge_method\", codeChallengeMethod);\n        authorizationParams.set(\"state\", state);\n        authorizationParams.set(\"nonce\", nonce);\n        const mergedAuthorizationParams = {\n            // any custom params to forward to /authorize defined as configuration\n            ...this.authorizationParameters,\n            // custom parameters passed in via the query params to ensure only the confidential client can set them\n            ...options.authorizationParameters\n        };\n        Object.entries(mergedAuthorizationParams).forEach(([key, val]) => {\n            if (!INTERNAL_AUTHORIZE_PARAMS.includes(key) && val != null) {\n                authorizationParams.set(key, String(val));\n            }\n        });\n        // Prepare transaction state\n        const transactionState = {\n            nonce,\n            maxAge: this.authorizationParameters.max_age,\n            codeVerifier,\n            responseType: \"code\",\n            state,\n            returnTo\n        };\n        // Generate authorization URL with PAR handling\n        const [error, authorizationUrl] = await this.authorizationUrl(authorizationParams);\n        if (error) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"An error occured while trying to initiate the login request.\", {\n                status: 500\n            });\n        }\n        // Set response and save transaction\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(authorizationUrl.toString());\n        await this.transactionStore.save(res.cookies, transactionState);\n        return res;\n    }\n    async handleLogin(req) {\n        const searchParams = Object.fromEntries(req.nextUrl.searchParams.entries());\n        const options = {\n            // SECURITY CRITICAL: Only forward query params when PAR is disabled\n            authorizationParameters: !this.pushedAuthorizationRequests\n                ? searchParams\n                : {},\n            returnTo: searchParams.returnTo\n        };\n        return this.startInteractiveLogin(options);\n    }\n    async handleLogout(req) {\n        const session = await this.sessionStore.get(req.cookies);\n        const [discoveryError, authorizationServerMetadata] = await this.discoverAuthorizationServerMetadata();\n        if (discoveryError) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"An error occured while trying to initiate the logout request.\", {\n                status: 500\n            });\n        }\n        const returnTo = req.nextUrl.searchParams.get(\"returnTo\") || this.appBaseUrl;\n        if (!authorizationServerMetadata.end_session_endpoint) {\n            // the Auth0 client does not have RP-initiated logout enabled, redirect to the `/v2/logout` endpoint\n            console.warn(\"The Auth0 client does not have RP-initiated logout enabled, the user will be redirected to the `/v2/logout` endpoint instead. Learn how to enable it here: https://auth0.com/docs/authenticate/login/logout/log-users-out-of-auth0#enable-endpoint-discovery\");\n            const url = new URL(\"/v2/logout\", this.issuer);\n            url.searchParams.set(\"returnTo\", returnTo);\n            url.searchParams.set(\"client_id\", this.clientMetadata.client_id);\n            const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n            await this.sessionStore.delete(req.cookies, res.cookies);\n            // Clear any orphaned transaction cookies\n            await this.transactionStore.deleteAll(req.cookies, res.cookies);\n            return res;\n        }\n        const url = new URL(authorizationServerMetadata.end_session_endpoint);\n        url.searchParams.set(\"client_id\", this.clientMetadata.client_id);\n        url.searchParams.set(\"post_logout_redirect_uri\", returnTo);\n        if (session?.internal.sid) {\n            url.searchParams.set(\"logout_hint\", session.internal.sid);\n        }\n        if (session?.tokenSet.idToken) {\n            url.searchParams.set(\"id_token_hint\", session?.tokenSet.idToken);\n        }\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n        await this.sessionStore.delete(req.cookies, res.cookies);\n        (0,_cookies__WEBPACK_IMPORTED_MODULE_5__.addCacheControlHeadersForSession)(res);\n        // Clear any orphaned transaction cookies\n        await this.transactionStore.deleteAll(req.cookies, res.cookies);\n        return res;\n    }\n    async handleCallback(req) {\n        const state = req.nextUrl.searchParams.get(\"state\");\n        if (!state) {\n            return this.onCallback(new _errors__WEBPACK_IMPORTED_MODULE_2__.MissingStateError(), {}, null);\n        }\n        const transactionStateCookie = await this.transactionStore.get(req.cookies, state);\n        if (!transactionStateCookie) {\n            return this.onCallback(new _errors__WEBPACK_IMPORTED_MODULE_2__.InvalidStateError(), {}, null);\n        }\n        const transactionState = transactionStateCookie.payload;\n        const onCallbackCtx = {\n            returnTo: transactionState.returnTo\n        };\n        const [discoveryError, authorizationServerMetadata] = await this.discoverAuthorizationServerMetadata();\n        if (discoveryError) {\n            return this.onCallback(discoveryError, onCallbackCtx, null);\n        }\n        let codeGrantParams;\n        try {\n            codeGrantParams = oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.validateAuthResponse(authorizationServerMetadata, this.clientMetadata, req.nextUrl.searchParams, transactionState.state);\n        }\n        catch (e) {\n            return this.onCallback(new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthorizationError({\n                cause: new _errors__WEBPACK_IMPORTED_MODULE_2__.OAuth2Error({\n                    code: e.error,\n                    message: e.error_description\n                })\n            }), onCallbackCtx, null);\n        }\n        const redirectUri = createRouteUrl(this.routes.callback, this.appBaseUrl); // must be registed with the authorization server\n        const codeGrantResponse = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.authorizationCodeGrantRequest(authorizationServerMetadata, this.clientMetadata, await this.getClientAuth(), codeGrantParams, redirectUri.toString(), transactionState.codeVerifier, {\n            ...this.httpOptions(),\n            [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.customFetch]: this.fetch,\n            [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.allowInsecureRequests]: this.allowInsecureRequests\n        });\n        let oidcRes;\n        try {\n            oidcRes = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.processAuthorizationCodeResponse(authorizationServerMetadata, this.clientMetadata, codeGrantResponse, {\n                expectedNonce: transactionState.nonce,\n                maxAge: transactionState.maxAge,\n                requireIdToken: true\n            });\n        }\n        catch (e) {\n            return this.onCallback(new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthorizationCodeGrantError({\n                cause: new _errors__WEBPACK_IMPORTED_MODULE_2__.OAuth2Error({\n                    code: e.error,\n                    message: e.error_description\n                })\n            }), onCallbackCtx, null);\n        }\n        const idTokenClaims = oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.getValidatedIdTokenClaims(oidcRes);\n        let session = {\n            user: idTokenClaims,\n            tokenSet: {\n                accessToken: oidcRes.access_token,\n                idToken: oidcRes.id_token,\n                scope: oidcRes.scope,\n                refreshToken: oidcRes.refresh_token,\n                expiresAt: Math.floor(Date.now() / 1000) + Number(oidcRes.expires_in)\n            },\n            internal: {\n                sid: idTokenClaims.sid,\n                createdAt: Math.floor(Date.now() / 1000)\n            }\n        };\n        const res = await this.onCallback(null, onCallbackCtx, session);\n        if (this.beforeSessionSaved) {\n            const updatedSession = await this.beforeSessionSaved(session, oidcRes.id_token ?? null);\n            session = {\n                ...updatedSession,\n                internal: session.internal\n            };\n        }\n        else {\n            session.user = (0,_user__WEBPACK_IMPORTED_MODULE_6__.filterClaims)(idTokenClaims);\n        }\n        await this.sessionStore.set(req.cookies, res.cookies, session, true);\n        (0,_cookies__WEBPACK_IMPORTED_MODULE_5__.addCacheControlHeadersForSession)(res);\n        await this.transactionStore.delete(res.cookies, state);\n        return res;\n    }\n    async handleProfile(req) {\n        const session = await this.sessionStore.get(req.cookies);\n        if (!session) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 401\n            });\n        }\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(session?.user);\n        (0,_cookies__WEBPACK_IMPORTED_MODULE_5__.addCacheControlHeadersForSession)(res);\n        return res;\n    }\n    async handleAccessToken(req) {\n        const session = await this.sessionStore.get(req.cookies);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: {\n                    message: \"The user does not have an active session.\",\n                    code: _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenErrorCode.MISSING_SESSION\n                }\n            }, {\n                status: 401\n            });\n        }\n        const [error, updatedTokenSet] = await this.getTokenSet(session.tokenSet);\n        if (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: {\n                    message: error.message,\n                    code: error.code\n                }\n            }, {\n                status: 401\n            });\n        }\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            token: updatedTokenSet.accessToken,\n            scope: updatedTokenSet.scope,\n            expires_at: updatedTokenSet.expiresAt\n        });\n        if (updatedTokenSet.accessToken !== session.tokenSet.accessToken ||\n            updatedTokenSet.refreshToken !== session.tokenSet.refreshToken ||\n            updatedTokenSet.expiresAt !== session.tokenSet.expiresAt) {\n            await this.sessionStore.set(req.cookies, res.cookies, {\n                ...session,\n                tokenSet: updatedTokenSet\n            });\n            (0,_cookies__WEBPACK_IMPORTED_MODULE_5__.addCacheControlHeadersForSession)(res);\n        }\n        return res;\n    }\n    async handleBackChannelLogout(req) {\n        if (!this.sessionStore.store) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"A session data store is not configured.\", {\n                status: 500\n            });\n        }\n        if (!this.sessionStore.store.deleteByLogoutToken) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"Back-channel logout is not supported by the session data store.\", {\n                status: 500\n            });\n        }\n        const body = new URLSearchParams(await req.text());\n        const logoutToken = body.get(\"logout_token\");\n        if (!logoutToken) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"Missing `logout_token` in the request body.\", {\n                status: 400\n            });\n        }\n        const [error, logoutTokenClaims] = await this.verifyLogoutToken(logoutToken);\n        if (error) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(error.message, {\n                status: 400\n            });\n        }\n        await this.sessionStore.store.deleteByLogoutToken(logoutTokenClaims);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n            status: 204\n        });\n    }\n    /**\n     * getTokenSet returns a valid token set. If the access token has expired, it will attempt to\n     * refresh it using the refresh token, if available.\n     */\n    async getTokenSet(tokenSet, forceRefresh) {\n        // the access token has expired but we do not have a refresh token\n        if (!tokenSet.refreshToken && tokenSet.expiresAt <= Date.now() / 1000) {\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenError(_errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenErrorCode.MISSING_REFRESH_TOKEN, \"The access token has expired and a refresh token was not provided. The user needs to re-authenticate.\"),\n                null\n            ];\n        }\n        if (tokenSet.refreshToken) {\n            // either the access token has expired or we are forcing a refresh\n            if (forceRefresh || tokenSet.expiresAt <= Date.now() / 1000) {\n                const [discoveryError, authorizationServerMetadata] = await this.discoverAuthorizationServerMetadata();\n                if (discoveryError) {\n                    console.error(discoveryError);\n                    return [discoveryError, null];\n                }\n                const refreshTokenRes = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.refreshTokenGrantRequest(authorizationServerMetadata, this.clientMetadata, await this.getClientAuth(), tokenSet.refreshToken, {\n                    ...this.httpOptions(),\n                    [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.customFetch]: this.fetch,\n                    [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.allowInsecureRequests]: this.allowInsecureRequests\n                });\n                let oauthRes;\n                try {\n                    oauthRes = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.processRefreshTokenResponse(authorizationServerMetadata, this.clientMetadata, refreshTokenRes);\n                }\n                catch (e) {\n                    console.error(e);\n                    return [\n                        new _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenError(_errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenErrorCode.FAILED_TO_REFRESH_TOKEN, \"The access token has expired and there was an error while trying to refresh it. Check the server logs for more information.\"),\n                        null\n                    ];\n                }\n                const accessTokenExpiresAt = Math.floor(Date.now() / 1000) + Number(oauthRes.expires_in);\n                const updatedTokenSet = {\n                    ...tokenSet, // contains the existing `iat` claim to maintain the session lifetime\n                    accessToken: oauthRes.access_token,\n                    idToken: oauthRes.id_token,\n                    expiresAt: accessTokenExpiresAt\n                };\n                if (oauthRes.refresh_token) {\n                    // refresh token rotation is enabled, persist the new refresh token from the response\n                    updatedTokenSet.refreshToken = oauthRes.refresh_token;\n                }\n                else {\n                    // we did not get a refresh token back, keep the current long-lived refresh token around\n                    updatedTokenSet.refreshToken = tokenSet.refreshToken;\n                }\n                return [null, updatedTokenSet];\n            }\n        }\n        return [null, tokenSet];\n    }\n    async discoverAuthorizationServerMetadata() {\n        if (this.authorizationServerMetadata) {\n            return [null, this.authorizationServerMetadata];\n        }\n        const issuer = new URL(this.issuer);\n        try {\n            const authorizationServerMetadata = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.discoveryRequest(issuer, {\n                ...this.httpOptions(),\n                [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.customFetch]: this.fetch,\n                [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.allowInsecureRequests]: this.allowInsecureRequests\n            })\n                .then((response) => oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.processDiscoveryResponse(issuer, response));\n            this.authorizationServerMetadata = authorizationServerMetadata;\n            return [null, authorizationServerMetadata];\n        }\n        catch (e) {\n            console.error(`An error occured while performing the discovery request. Please make sure the AUTH0_DOMAIN environment variable is correctly configured — the format must be 'example.us.auth0.com'. issuer=${issuer.toString()}, error:`, e);\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.DiscoveryError(\"Discovery failed for the OpenID Connect configuration.\"),\n                null\n            ];\n        }\n    }\n    async defaultOnCallback(error, ctx) {\n        if (error) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(error.message, {\n                status: 500\n            });\n        }\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(createRouteUrl(ctx.returnTo || \"/\", this.appBaseUrl));\n        return res;\n    }\n    async verifyLogoutToken(logoutToken) {\n        const [discoveryError, authorizationServerMetadata] = await this.discoverAuthorizationServerMetadata();\n        if (discoveryError) {\n            return [discoveryError, null];\n        }\n        // only `RS256` is supported for logout tokens\n        const ID_TOKEN_SIGNING_ALG = \"RS256\";\n        const keyInput = jose__WEBPACK_IMPORTED_MODULE_8__.createRemoteJWKSet(new URL(authorizationServerMetadata.jwks_uri), {\n            [jose__WEBPACK_IMPORTED_MODULE_8__.jwksCache]: this.jwksCache\n        });\n        const { payload } = await jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify(logoutToken, keyInput, {\n            issuer: authorizationServerMetadata.issuer,\n            audience: this.clientMetadata.client_id,\n            algorithms: [ID_TOKEN_SIGNING_ALG],\n            requiredClaims: [\"iat\"]\n        });\n        if (!(\"sid\" in payload) && !(\"sub\" in payload)) {\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('either \"sid\" or \"sub\" (or both) claims must be present'),\n                null\n            ];\n        }\n        if (\"sid\" in payload && typeof payload.sid !== \"string\") {\n            return [new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"sid\" claim must be a string'), null];\n        }\n        if (\"sub\" in payload && typeof payload.sub !== \"string\") {\n            return [new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"sub\" claim must be a string'), null];\n        }\n        if (\"nonce\" in payload) {\n            return [new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"nonce\" claim is prohibited'), null];\n        }\n        if (!(\"events\" in payload)) {\n            return [new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"events\" claim is missing'), null];\n        }\n        if (typeof payload.events !== \"object\" || payload.events === null) {\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"events\" claim must be an object'),\n                null\n            ];\n        }\n        if (!(\"http://schemas.openid.net/event/backchannel-logout\" in payload.events)) {\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"http://schemas.openid.net/event/backchannel-logout\" member is missing in the \"events\" claim'),\n                null\n            ];\n        }\n        if (typeof payload.events[\"http://schemas.openid.net/event/backchannel-logout\"] !== \"object\") {\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.BackchannelLogoutError('\"http://schemas.openid.net/event/backchannel-logout\" member in the \"events\" claim must be an object'),\n                null\n            ];\n        }\n        return [\n            null,\n            {\n                sid: payload.sid,\n                sub: payload.sub\n            }\n        ];\n    }\n    async authorizationUrl(params) {\n        const [discoveryError, authorizationServerMetadata] = await this.discoverAuthorizationServerMetadata();\n        if (discoveryError) {\n            return [discoveryError, null];\n        }\n        if (this.pushedAuthorizationRequests &&\n            !authorizationServerMetadata.pushed_authorization_request_endpoint) {\n            console.error(\"The Auth0 tenant does not have pushed authorization requests enabled. Learn how to enable it here: https://auth0.com/docs/get-started/applications/configure-par\");\n            return [\n                new Error(\"The authorization server does not support pushed authorization requests.\"),\n                null\n            ];\n        }\n        const authorizationUrl = new URL(authorizationServerMetadata.authorization_endpoint);\n        if (this.pushedAuthorizationRequests) {\n            // push the request params to the authorization server\n            const response = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.pushedAuthorizationRequest(authorizationServerMetadata, this.clientMetadata, await this.getClientAuth(), params, {\n                ...this.httpOptions(),\n                [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.customFetch]: this.fetch,\n                [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.allowInsecureRequests]: this.allowInsecureRequests\n            });\n            let parRes;\n            try {\n                parRes = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.processPushedAuthorizationResponse(authorizationServerMetadata, this.clientMetadata, response);\n            }\n            catch (e) {\n                return [\n                    new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthorizationError({\n                        cause: new _errors__WEBPACK_IMPORTED_MODULE_2__.OAuth2Error({\n                            code: e.error,\n                            message: e.error_description\n                        }),\n                        message: \"An error occured while pushing the authorization request.\"\n                    }),\n                    null\n                ];\n            }\n            authorizationUrl.searchParams.set(\"request_uri\", parRes.request_uri);\n            authorizationUrl.searchParams.set(\"client_id\", this.clientMetadata.client_id);\n            return [null, authorizationUrl];\n        }\n        // append the query parameters to the authorization URL for the normal flow\n        authorizationUrl.search = params.toString();\n        return [null, authorizationUrl];\n    }\n    async getClientAuth() {\n        if (!this.clientSecret && !this.clientAssertionSigningKey) {\n            throw new Error(\"The client secret or client assertion signing key must be provided.\");\n        }\n        let clientPrivateKey = this.clientAssertionSigningKey;\n        if (clientPrivateKey && !(clientPrivateKey instanceof CryptoKey)) {\n            clientPrivateKey = await jose__WEBPACK_IMPORTED_MODULE_10__.importPKCS8(clientPrivateKey, this.clientAssertionSigningAlg);\n        }\n        return clientPrivateKey\n            ? oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.PrivateKeyJwt(clientPrivateKey)\n            : oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.ClientSecretPost(this.clientSecret);\n    }\n    get issuer() {\n        return this.domain.startsWith(\"http://\") ||\n            this.domain.startsWith(\"https://\")\n            ? this.domain\n            : `https://${this.domain}`;\n    }\n    /**\n     * Exchanges a refresh token for an access token for a connection.\n     *\n     * This method performs a token exchange using the provided refresh token and connection details.\n     * It first checks if the refresh token is present in the `tokenSet`. If not, it returns an error.\n     * Then, it constructs the necessary parameters for the token exchange request and performs\n     * the request to the authorization server's token endpoint.\n     *\n     * @returns {Promise<[AccessTokenForConnectionError, null] | [null, ConnectionTokenSet]>} A promise that resolves to a tuple.\n     *          The first element is either an `AccessTokenForConnectionError` if an error occurred, or `null` if the request was successful.\n     *          The second element is either `null` if an error occurred, or a `ConnectionTokenSet` object\n     *          containing the access token, expiration time, and scope if the request was successful.\n     *\n     * @throws {AccessTokenForConnectionError} If the refresh token is missing or if there is an error during the token exchange process.\n     */\n    async getConnectionTokenSet(tokenSet, connectionTokenSet, options) {\n        // If we do not have a refresh token\n        // and we do not have a connection token set in the cache or the one we have is expired,\n        // there is noting to retrieve and we return an error.\n        if (!tokenSet.refreshToken &&\n            (!connectionTokenSet || connectionTokenSet.expiresAt <= Date.now() / 1000)) {\n            return [\n                new _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenForConnectionError(_errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenForConnectionErrorCode.MISSING_REFRESH_TOKEN, \"A refresh token was not present, Connection Access Token requires a refresh token. The user needs to re-authenticate.\"),\n                null\n            ];\n        }\n        // If we do have a refresh token,\n        // and we do not have a connection token set in the cache or the one we have is expired,\n        // we need to exchange the refresh token for a connection access token.\n        if (tokenSet.refreshToken &&\n            (!connectionTokenSet || connectionTokenSet.expiresAt <= Date.now() / 1000)) {\n            const params = new URLSearchParams();\n            params.append(\"connection\", options.connection);\n            params.append(\"subject_token_type\", SUBJECT_TYPE_REFRESH_TOKEN);\n            params.append(\"subject_token\", tokenSet.refreshToken);\n            params.append(\"requested_token_type\", REQUESTED_TOKEN_TYPE_FEDERATED_CONNECTION_ACCESS_TOKEN);\n            if (options.login_hint) {\n                params.append(\"login_hint\", options.login_hint);\n            }\n            const [discoveryError, authorizationServerMetadata] = await this.discoverAuthorizationServerMetadata();\n            if (discoveryError) {\n                console.error(discoveryError);\n                return [discoveryError, null];\n            }\n            const httpResponse = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.genericTokenEndpointRequest(authorizationServerMetadata, this.clientMetadata, await this.getClientAuth(), GRANT_TYPE_FEDERATED_CONNECTION_ACCESS_TOKEN, params, {\n                [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.customFetch]: this.fetch,\n                [oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.allowInsecureRequests]: this.allowInsecureRequests\n            });\n            let tokenEndpointResponse;\n            try {\n                tokenEndpointResponse = await oauth4webapi__WEBPACK_IMPORTED_MODULE_7__.processGenericTokenEndpointResponse(authorizationServerMetadata, this.clientMetadata, httpResponse);\n            }\n            catch (err) {\n                console.error(err);\n                return [\n                    new _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenForConnectionError(_errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenForConnectionErrorCode.FAILED_TO_EXCHANGE, \"There was an error trying to exchange the refresh token for a connection access token. Check the server logs for more information.\", new _errors__WEBPACK_IMPORTED_MODULE_2__.OAuth2Error({\n                        code: err.error,\n                        message: err.error_description\n                    })),\n                    null\n                ];\n            }\n            return [\n                null,\n                {\n                    accessToken: tokenEndpointResponse.access_token,\n                    expiresAt: Math.floor(Date.now() / 1000) +\n                        Number(tokenEndpointResponse.expires_in),\n                    scope: tokenEndpointResponse.scope,\n                    connection: options.connection\n                }\n            ];\n        }\n        return [null, connectionTokenSet];\n    }\n}\nconst encodeBase64 = (input) => {\n    const unencoded = new TextEncoder().encode(input);\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < unencoded.length; i += CHUNK_SIZE) {\n        arr.push(\n        // @ts-expect-error Argument of type 'Uint8Array' is not assignable to parameter of type 'number[]'.\n        String.fromCharCode.apply(null, unencoded.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(\"\"));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/auth-client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/client.js":
/*!****************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/client.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Auth0Client: () => (/* binding */ Auth0Client)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/errors/index.js\");\n/* harmony import */ var _auth_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./auth-client */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/auth-client.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cookies */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js\");\n/* harmony import */ var _session_stateful_session_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./session/stateful-session-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/stateful-session-store.js\");\n/* harmony import */ var _session_stateless_session_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./session/stateless-session-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/stateless-session-store.js\");\n/* harmony import */ var _transaction_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./transaction-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/transaction-store.js\");\n\n\n\n\n\n\n\n\nclass Auth0Client {\n    constructor(options = {}) {\n        // Extract and validate required options\n        const { domain, clientId, clientSecret, appBaseUrl, secret, clientAssertionSigningKey } = this.validateAndExtractRequiredOptions(options);\n        const clientAssertionSigningAlg = options.clientAssertionSigningAlg ||\n            process.env.AUTH0_CLIENT_ASSERTION_SIGNING_ALG;\n        const sessionCookieOptions = {\n            name: options.session?.cookie?.name ?? \"__session\",\n            secure: options.session?.cookie?.secure ??\n                process.env.AUTH0_COOKIE_SECURE === \"true\",\n            sameSite: options.session?.cookie?.sameSite ??\n                process.env.AUTH0_COOKIE_SAME_SITE ??\n                \"lax\",\n            path: options.session?.cookie?.path ?? process.env.AUTH0_COOKIE_PATH ?? \"/\",\n            transient: options.session?.cookie?.transient ??\n                process.env.AUTH0_COOKIE_TRANSIENT === \"true\",\n            domain: options.session?.cookie?.domain ?? process.env.AUTH0_COOKIE_DOMAIN\n        };\n        const transactionCookieOptions = {\n            prefix: options.transactionCookie?.prefix ?? \"__txn_\",\n            secure: options.transactionCookie?.secure ?? false,\n            sameSite: options.transactionCookie?.sameSite ?? \"lax\",\n            path: options.transactionCookie?.path ?? \"/\"\n        };\n        if (appBaseUrl) {\n            const { protocol } = new URL(appBaseUrl);\n            if (protocol === \"https:\") {\n                sessionCookieOptions.secure = true;\n                transactionCookieOptions.secure = true;\n            }\n        }\n        this.transactionStore = new _transaction_store__WEBPACK_IMPORTED_MODULE_7__.TransactionStore({\n            ...options.session,\n            secret,\n            cookieOptions: transactionCookieOptions\n        });\n        this.sessionStore = options.sessionStore\n            ? new _session_stateful_session_store__WEBPACK_IMPORTED_MODULE_5__.StatefulSessionStore({\n                ...options.session,\n                secret,\n                store: options.sessionStore,\n                cookieOptions: sessionCookieOptions\n            })\n            : new _session_stateless_session_store__WEBPACK_IMPORTED_MODULE_6__.StatelessSessionStore({\n                ...options.session,\n                secret,\n                cookieOptions: sessionCookieOptions\n            });\n        this.authClient = new _auth_client__WEBPACK_IMPORTED_MODULE_3__.AuthClient({\n            transactionStore: this.transactionStore,\n            sessionStore: this.sessionStore,\n            domain,\n            clientId,\n            clientSecret,\n            clientAssertionSigningKey,\n            clientAssertionSigningAlg,\n            authorizationParameters: options.authorizationParameters,\n            pushedAuthorizationRequests: options.pushedAuthorizationRequests,\n            appBaseUrl,\n            secret,\n            signInReturnToPath: options.signInReturnToPath,\n            beforeSessionSaved: options.beforeSessionSaved,\n            onCallback: options.onCallback,\n            routes: options.routes,\n            allowInsecureRequests: options.allowInsecureRequests,\n            httpTimeout: options.httpTimeout,\n            enableTelemetry: options.enableTelemetry,\n            enableAccessTokenEndpoint: options.enableAccessTokenEndpoint\n        });\n    }\n    /**\n     * middleware mounts the SDK routes to run as a middleware function.\n     */\n    middleware(req) {\n        return this.authClient.handler.bind(this.authClient)(req);\n    }\n    /**\n     * getSession returns the session data for the current request.\n     */\n    async getSession(req) {\n        if (req) {\n            // middleware usage\n            if (req instanceof next_server__WEBPACK_IMPORTED_MODULE_1__.NextRequest) {\n                return this.sessionStore.get(req.cookies);\n            }\n            // pages router usage\n            return this.sessionStore.get(this.createRequestCookies(req));\n        }\n        // app router usage: Server Components, Server Actions, Route Handlers\n        return this.sessionStore.get(await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)());\n    }\n    /**\n     * getAccessToken returns the access token.\n     *\n     * NOTE: Server Components cannot set cookies. Calling `getAccessToken()` in a Server Component will cause the access token to be refreshed, if it is expired, and the updated token set will not to be persisted.\n     * It is recommended to call `getAccessToken(req, res)` in the middleware if you need to retrieve the access token in a Server Component to ensure the updated token set is persisted.\n     */\n    async getAccessToken(arg1, arg2, arg3) {\n        const defaultOptions = {\n            refresh: false\n        };\n        let req = undefined;\n        let res = undefined;\n        let options = {};\n        // Determine which overload was called based on arguments\n        if (arg1 &&\n            (arg1 instanceof Request || typeof arg1.headers === \"object\")) {\n            // Case: getAccessToken(req, res, options?)\n            req = arg1;\n            res = arg2; // arg2 must be Response if arg1 is Request\n            // Merge provided options (arg3) with defaults\n            options = { ...defaultOptions, ...(arg3 ?? {}) };\n            if (!res) {\n                throw new TypeError(\"getAccessToken(req, res): The 'res' argument is missing. Both 'req' and 'res' must be provided together for Pages Router or middleware usage.\");\n            }\n        }\n        else {\n            // Case: getAccessToken(options?) or getAccessToken()\n            // arg1 (if present) must be options, arg2 and arg3 must be undefined.\n            if (arg2 !== undefined || arg3 !== undefined) {\n                throw new TypeError(\"getAccessToken: Invalid arguments. Valid signatures are getAccessToken(), getAccessToken(options), or getAccessToken(req, res, options).\");\n            }\n            // Merge provided options (arg1) with defaults\n            options = {\n                ...defaultOptions,\n                ...(arg1 ?? {})\n            };\n        }\n        const session = req\n            ? await this.getSession(req)\n            : await this.getSession();\n        if (!session) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenError(_errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenErrorCode.MISSING_SESSION, \"The user does not have an active session.\");\n        }\n        const [error, tokenSet] = await this.authClient.getTokenSet(session.tokenSet, options.refresh);\n        if (error) {\n            throw error;\n        }\n        // update the session with the new token set, if necessary\n        if (tokenSet.accessToken !== session.tokenSet.accessToken ||\n            tokenSet.expiresAt !== session.tokenSet.expiresAt ||\n            tokenSet.refreshToken !== session.tokenSet.refreshToken) {\n            await this.saveToSession({\n                ...session,\n                tokenSet\n            }, req, res);\n        }\n        return {\n            token: tokenSet.accessToken,\n            scope: tokenSet.scope,\n            expiresAt: tokenSet.expiresAt\n        };\n    }\n    /**\n     * Retrieves an access token for a connection.\n     *\n     * This method attempts to obtain an access token for a specified connection.\n     * It first checks if a session exists, either from the provided request or from cookies.\n     * If no session is found, it throws a `AccessTokenForConnectionError` indicating\n     * that the user does not have an active session.\n     *\n     * @param {AccessTokenForConnectionOptions} options - Options for retrieving an access token for a connection.\n     * @param {PagesRouterRequest | NextRequest} [req] - An optional request object from which to extract session information.\n     * @param {PagesRouterResponse | NextResponse} [res] - An optional response object from which to extract session information.\n     *\n     * @throws {AccessTokenForConnectionError} If the user does not have an active session.\n     * @throws {Error} If there is an error during the token exchange process.\n     *\n     * @returns {Promise<{ token: string; expiresAt: number; scope?: string }} An object containing the access token and its expiration time.\n     */\n    async getAccessTokenForConnection(options, req, res) {\n        const session = req\n            ? await this.getSession(req)\n            : await this.getSession();\n        if (!session) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenForConnectionError(_errors__WEBPACK_IMPORTED_MODULE_2__.AccessTokenForConnectionErrorCode.MISSING_SESSION, \"The user does not have an active session.\");\n        }\n        // Find the connection token set in the session\n        const existingTokenSet = session.connectionTokenSets?.find((tokenSet) => tokenSet.connection === options.connection);\n        const [error, retrievedTokenSet] = await this.authClient.getConnectionTokenSet(session.tokenSet, existingTokenSet, options);\n        if (error !== null) {\n            throw error;\n        }\n        // If we didnt have a corresponding connection token set in the session\n        // or if the one we have in the session does not match the one we received\n        // We want to update the store incase we retrieved a token set.\n        if (retrievedTokenSet &&\n            (!existingTokenSet ||\n                retrievedTokenSet.accessToken !== existingTokenSet.accessToken ||\n                retrievedTokenSet.expiresAt !== existingTokenSet.expiresAt ||\n                retrievedTokenSet.scope !== existingTokenSet.scope)) {\n            let tokenSets;\n            // If we already had the connection token set in the session\n            // we need to update the item in the array\n            // If not, we need to add it.\n            if (existingTokenSet) {\n                tokenSets = session.connectionTokenSets?.map((tokenSet) => tokenSet.connection === options.connection\n                    ? retrievedTokenSet\n                    : tokenSet);\n            }\n            else {\n                tokenSets = [...(session.connectionTokenSets || []), retrievedTokenSet];\n            }\n            await this.saveToSession({\n                ...session,\n                connectionTokenSets: tokenSets\n            }, req, res);\n        }\n        return {\n            token: retrievedTokenSet.accessToken,\n            scope: retrievedTokenSet.scope,\n            expiresAt: retrievedTokenSet.expiresAt\n        };\n    }\n    /**\n     * updateSession updates the session of the currently authenticated user. If the user does not have a session, an error is thrown.\n     */\n    async updateSession(reqOrSession, res, sessionData) {\n        if (!res) {\n            // app router: Server Actions, Route Handlers\n            const existingSession = await this.getSession();\n            if (!existingSession) {\n                throw new Error(\"The user is not authenticated.\");\n            }\n            const updatedSession = reqOrSession;\n            if (!updatedSession) {\n                throw new Error(\"The session data is missing.\");\n            }\n            await this.sessionStore.set(await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)(), await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)(), {\n                ...updatedSession,\n                internal: {\n                    ...existingSession.internal\n                }\n            });\n        }\n        else {\n            const req = reqOrSession;\n            if (!sessionData) {\n                throw new Error(\"The session data is missing.\");\n            }\n            if (req instanceof next_server__WEBPACK_IMPORTED_MODULE_1__.NextRequest && res instanceof next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse) {\n                // middleware usage\n                const existingSession = await this.getSession(req);\n                if (!existingSession) {\n                    throw new Error(\"The user is not authenticated.\");\n                }\n                await this.sessionStore.set(req.cookies, res.cookies, {\n                    ...sessionData,\n                    internal: {\n                        ...existingSession.internal\n                    }\n                });\n            }\n            else {\n                // pages router usage\n                const existingSession = await this.getSession(req);\n                if (!existingSession) {\n                    throw new Error(\"The user is not authenticated.\");\n                }\n                const resHeaders = new Headers();\n                const resCookies = new _cookies__WEBPACK_IMPORTED_MODULE_4__.ResponseCookies(resHeaders);\n                const updatedSession = sessionData;\n                const reqCookies = this.createRequestCookies(req);\n                const pagesRouterRes = res;\n                await this.sessionStore.set(reqCookies, resCookies, {\n                    ...updatedSession,\n                    internal: {\n                        ...existingSession.internal\n                    }\n                });\n                for (const [key, value] of resHeaders.entries()) {\n                    pagesRouterRes.setHeader(key, value);\n                }\n            }\n        }\n    }\n    createRequestCookies(req) {\n        const headers = new Headers();\n        for (const key in req.headers) {\n            if (Array.isArray(req.headers[key])) {\n                for (const value of req.headers[key]) {\n                    headers.append(key, value);\n                }\n            }\n            else {\n                headers.append(key, req.headers[key] ?? \"\");\n            }\n        }\n        return new _cookies__WEBPACK_IMPORTED_MODULE_4__.RequestCookies(headers);\n    }\n    async startInteractiveLogin(options) {\n        return this.authClient.startInteractiveLogin(options);\n    }\n    async saveToSession(data, req, res) {\n        if (req && res) {\n            if (req instanceof next_server__WEBPACK_IMPORTED_MODULE_1__.NextRequest && res instanceof next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse) {\n                // middleware usage\n                await this.sessionStore.set(req.cookies, res.cookies, data);\n            }\n            else {\n                // pages router usage\n                const resHeaders = new Headers();\n                const resCookies = new _cookies__WEBPACK_IMPORTED_MODULE_4__.ResponseCookies(resHeaders);\n                const pagesRouterRes = res;\n                await this.sessionStore.set(this.createRequestCookies(req), resCookies, data);\n                for (const [key, value] of resHeaders.entries()) {\n                    pagesRouterRes.setHeader(key, value);\n                }\n            }\n        }\n        else {\n            // app router usage: Server Components, Server Actions, Route Handlers\n            try {\n                await this.sessionStore.set(await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)(), await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)(), data);\n            }\n            catch (e) {\n                if (true) {\n                    console.warn(\"Failed to persist the updated token set. `getAccessToken()` was likely called from a Server Component which cannot set cookies.\");\n                }\n            }\n        }\n    }\n    /**\n     * Validates and extracts required configuration options.\n     * @param options The client options\n     * @returns The validated required options\n     * @throws ConfigurationError if any required option is missing\n     */\n    validateAndExtractRequiredOptions(options) {\n        // Base required options that are always needed\n        const requiredOptions = {\n            domain: options.domain ?? process.env.AUTH0_DOMAIN,\n            clientId: options.clientId ?? process.env.AUTH0_CLIENT_ID,\n            appBaseUrl: options.appBaseUrl ?? process.env.APP_BASE_URL,\n            secret: options.secret ?? process.env.AUTH0_SECRET\n        };\n        // Check client authentication options - either clientSecret OR clientAssertionSigningKey must be provided\n        const clientSecret = options.clientSecret ?? process.env.AUTH0_CLIENT_SECRET;\n        const clientAssertionSigningKey = options.clientAssertionSigningKey ??\n            process.env.AUTH0_CLIENT_ASSERTION_SIGNING_KEY;\n        const hasClientAuthentication = !!(clientSecret || clientAssertionSigningKey);\n        const missing = Object.entries(requiredOptions)\n            .filter(([, value]) => !value)\n            .map(([key]) => key);\n        // Add client authentication error if neither option is provided\n        if (!hasClientAuthentication) {\n            missing.push(\"clientAuthentication\");\n        }\n        if (missing.length) {\n            // Map of option keys to their exact environment variable names\n            const envVarNames = {\n                domain: \"AUTH0_DOMAIN\",\n                clientId: \"AUTH0_CLIENT_ID\",\n                appBaseUrl: \"APP_BASE_URL\",\n                secret: \"AUTH0_SECRET\"\n            };\n            // Standard intro message explaining the issue\n            let errorMessage = \"WARNING: Not all required options where provided when creating an instance of Auth0Client. Ensure to provide all missing options, either by passing it to the Auth0Client constructor, or by setting the corresponding environment variable.\\n\";\n            // Add specific details for each missing option\n            missing.forEach((key) => {\n                if (key === \"clientAuthentication\") {\n                    errorMessage += `Missing: clientAuthentication: Set either AUTH0_CLIENT_SECRET env var or AUTH0_CLIENT_ASSERTION_SIGNING_KEY env var, or pass clientSecret or clientAssertionSigningKey in options\\n`;\n                }\n                else if (envVarNames[key]) {\n                    errorMessage += `Missing: ${key}: Set ${envVarNames[key]} env var or pass ${key} in options\\n`;\n                }\n                else {\n                    errorMessage += `Missing: ${key}\\n`;\n                }\n            });\n            console.error(errorMessage.trim());\n        }\n        // Prepare the result object with all validated options\n        const result = {\n            ...requiredOptions,\n            clientSecret,\n            clientAssertionSigningKey\n        };\n        // Type-safe assignment after validation\n        return result;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequestCookies: () => (/* reexport safe */ _edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__.RequestCookies),\n/* harmony export */   ResponseCookies: () => (/* reexport safe */ _edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies),\n/* harmony export */   addCacheControlHeadersForSession: () => (/* binding */ addCacheControlHeadersForSession),\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   deleteChunkedCookie: () => (/* binding */ deleteChunkedCookie),\n/* harmony export */   encrypt: () => (/* binding */ encrypt),\n/* harmony export */   getChunkedCookie: () => (/* binding */ getChunkedCookie),\n/* harmony export */   setChunkedCookie: () => (/* binding */ setChunkedCookie),\n/* harmony export */   sign: () => (/* binding */ sign),\n/* harmony export */   verifySigned: () => (/* binding */ verifySigned)\n/* harmony export */ });\n/* harmony import */ var _edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @edge-runtime/cookies */ \"(rsc)/./node_modules/@edge-runtime/cookies/dist/index.mjs\");\n/* harmony import */ var _panva_hkdf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @panva/hkdf */ \"(rsc)/./node_modules/@panva/hkdf/dist/node/esm/index.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/encrypt.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/decrypt.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/verify.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/sign.js\");\n\n\n\nconst ENC = \"A256GCM\";\nconst ALG = \"dir\";\nconst DIGEST = \"sha256\";\nconst BYTE_LENGTH = 32;\nconst ENCRYPTION_INFO = \"JWE CEK\";\nasync function encrypt(payload, secret, expiration, additionalHeaders) {\n    const encryptionSecret = await (0,_panva_hkdf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DIGEST, secret, \"\", ENCRYPTION_INFO, BYTE_LENGTH);\n    const encryptedCookie = await new jose__WEBPACK_IMPORTED_MODULE_2__.EncryptJWT(payload)\n        .setProtectedHeader({ enc: ENC, alg: ALG, ...additionalHeaders })\n        .setExpirationTime(expiration)\n        .encrypt(encryptionSecret);\n    return encryptedCookie.toString();\n}\nasync function decrypt(cookieValue, secret, options) {\n    const encryptionSecret = await (0,_panva_hkdf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DIGEST, secret, \"\", ENCRYPTION_INFO, BYTE_LENGTH);\n    const cookie = await jose__WEBPACK_IMPORTED_MODULE_3__.jwtDecrypt(cookieValue, encryptionSecret, {\n        ...options,\n        ...{ clockTolerance: 15 }\n    });\n    return cookie;\n}\n/**\n * Derive a signing key from a given secret.\n * This method is used solely to migrate signed, legacy cookies to the new encrypted cookie format (v4+).\n */\nconst signingSecret = (secret) => (0,_panva_hkdf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"sha256\", secret, \"\", \"JWS Cookie Signing\", BYTE_LENGTH);\n/**\n * Verify a signed cookie. If the cookie is valid, the value is returned. Otherwise, undefined is returned.\n * This method is used solely to migrate signed, legacy cookies to the new encrypted cookie format (v4+).\n */\nasync function verifySigned(k, v, secret) {\n    if (!v) {\n        return undefined;\n    }\n    const [value, signature] = v.split(\".\");\n    const flattenedJWS = {\n        protected: jose__WEBPACK_IMPORTED_MODULE_4__.encode(JSON.stringify({ alg: \"HS256\", b64: false, crit: [\"b64\"] })),\n        payload: `${k}=${value}`,\n        signature\n    };\n    const key = await signingSecret(secret);\n    try {\n        await jose__WEBPACK_IMPORTED_MODULE_5__.flattenedVerify(flattenedJWS, key, {\n            algorithms: [\"HS256\"]\n        });\n        return value;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\n/**\n * Sign a cookie value using a secret.\n * This method is used solely to migrate signed, legacy cookies to the new encrypted cookie format (v4+).\n */\nasync function sign(name, value, secret) {\n    const key = await signingSecret(secret);\n    const { signature } = await new jose__WEBPACK_IMPORTED_MODULE_6__.FlattenedSign(new TextEncoder().encode(`${name}=${value}`))\n        .setProtectedHeader({ alg: \"HS256\", b64: false, crit: [\"b64\"] })\n        .sign(key);\n    return `${value}.${signature}`;\n}\n\n\n// Chunked cookies Configuration\nconst MAX_CHUNK_SIZE = 3500; // Slightly under 4KB\nconst CHUNK_PREFIX = \"__\";\nconst CHUNK_INDEX_REGEX = new RegExp(`${CHUNK_PREFIX}(\\\\d+)$`);\nconst LEGACY_CHUNK_INDEX_REGEX = /\\.(\\d+)$/;\n/**\n * Retrieves the index of a cookie based on its name.\n * Supports current format `{name}__{index}` and legacy format `{name}.{index}`.\n *\n * @param name - The name of the cookie.\n * @returns The index of the cookie. Returns undefined if no index is found.\n */\nconst getChunkedCookieIndex = (name, isLegacyCookie) => {\n    const match = isLegacyCookie\n        ? LEGACY_CHUNK_INDEX_REGEX.exec(name)\n        : CHUNK_INDEX_REGEX.exec(name);\n    if (!match) {\n        return undefined;\n    }\n    return parseInt(match[1], 10);\n};\n/**\n * Retrieves all cookies from the request that have names starting with a specific prefix.\n *\n * @param reqCookies - The cookies from the request.\n * @param name - The base name of the cookies to retrieve.\n * @returns An array of cookies that have names starting with the specified prefix.\n */\nconst getAllChunkedCookies = (reqCookies, name, isLegacyCookie) => {\n    const chunkedCookieRegex = new RegExp(isLegacyCookie\n        ? `^${name}${LEGACY_CHUNK_INDEX_REGEX.source}$`\n        : `^${name}${CHUNK_PREFIX}\\\\d+$`);\n    return reqCookies\n        .getAll()\n        .filter((cookie) => chunkedCookieRegex.test(cookie.name));\n};\n/**\n * Sets a cookie with the given name and value, splitting it into chunks if necessary.\n *\n * If the value exceeds the maximum chunk size, it will be split into multiple cookies\n * with names suffixed by a chunk index.\n *\n * @param name - The name of the cookie.\n * @param value - The value to be stored in the cookie.\n * @param options - Options for setting the cookie.\n * @param reqCookies - The request cookies object, used to enable read-after-write in the same request for middleware.\n * @param resCookies - The response cookies object, used to set the cookies in the response.\n */\nfunction setChunkedCookie(name, value, options, reqCookies, resCookies) {\n    const { transient, ...restOptions } = options;\n    const finalOptions = { ...restOptions };\n    if (transient) {\n        delete finalOptions.maxAge;\n    }\n    const valueBytes = new TextEncoder().encode(value).length;\n    // If value fits in a single cookie, set it directly\n    if (valueBytes <= MAX_CHUNK_SIZE) {\n        resCookies.set(name, value, finalOptions);\n        // to enable read-after-write in the same request for middleware\n        reqCookies.set(name, value);\n        // When we are writing a non-chunked cookie, we should remove the chunked cookies\n        getAllChunkedCookies(reqCookies, name).forEach((cookieChunk) => {\n            resCookies.delete(cookieChunk.name);\n            reqCookies.delete(cookieChunk.name);\n        });\n        return;\n    }\n    // Split value into chunks\n    let position = 0;\n    let chunkIndex = 0;\n    while (position < value.length) {\n        const chunk = value.slice(position, position + MAX_CHUNK_SIZE);\n        const chunkName = `${name}${CHUNK_PREFIX}${chunkIndex}`;\n        resCookies.set(chunkName, chunk, finalOptions);\n        // to enable read-after-write in the same request for middleware\n        reqCookies.set(chunkName, chunk);\n        position += MAX_CHUNK_SIZE;\n        chunkIndex++;\n    }\n    // clear unused chunks\n    const chunks = getAllChunkedCookies(reqCookies, name);\n    const chunksToRemove = chunks.length - chunkIndex;\n    if (chunksToRemove > 0) {\n        for (let i = 0; i < chunksToRemove; i++) {\n            const chunkIndexToRemove = chunkIndex + i;\n            const chunkName = `${name}${CHUNK_PREFIX}${chunkIndexToRemove}`;\n            resCookies.delete(chunkName);\n            reqCookies.delete(chunkName);\n        }\n    }\n    // When we have written chunked cookies, we should remove the non-chunked cookie\n    resCookies.delete(name);\n    reqCookies.delete(name);\n}\n/**\n * Retrieves a chunked cookie by its name from the request cookies.\n * If a regular cookie with the given name exists, it returns its value.\n * Otherwise, it attempts to retrieve and combine all chunks of the cookie.\n *\n * @param name - The name of the cookie to retrieve.\n * @param reqCookies - The request cookies object.\n * @returns The combined value of the chunked cookie, or `undefined` if the cookie does not exist or is incomplete.\n */\nfunction getChunkedCookie(name, reqCookies, isLegacyCookie) {\n    // Check if regular cookie exists\n    const cookie = reqCookies.get(name);\n    if (cookie?.value) {\n        // If the base cookie exists, return its value (handles non-chunked case)\n        return cookie.value;\n    }\n    const chunks = getAllChunkedCookies(reqCookies, name, isLegacyCookie).sort(\n    // Extract index from cookie name and sort numerically\n    (first, second) => {\n        return (getChunkedCookieIndex(first.name, isLegacyCookie) -\n            getChunkedCookieIndex(second.name, isLegacyCookie));\n    });\n    if (chunks.length === 0) {\n        return undefined;\n    }\n    // Validate sequence integrity - check for missing chunks\n    const highestIndex = getChunkedCookieIndex(chunks[chunks.length - 1].name, isLegacyCookie);\n    if (chunks.length !== highestIndex + 1) {\n        console.warn(`Incomplete chunked cookie '${name}': Found ${chunks.length} chunks, expected ${highestIndex + 1}`);\n        return undefined;\n    }\n    // Combine all chunks\n    return chunks.map((c) => c.value).join(\"\");\n}\n/**\n * Deletes a chunked cookie and all its associated chunks from the response cookies.\n *\n * @param name - The name of the main cookie to delete.\n * @param reqCookies - The request cookies object containing all cookies from the request.\n * @param resCookies - The response cookies object to manipulate the cookies in the response.\n */\nfunction deleteChunkedCookie(name, reqCookies, resCookies, isLegacyCookie) {\n    // Delete main cookie\n    resCookies.delete(name);\n    getAllChunkedCookies(reqCookies, name, isLegacyCookie).forEach((cookie) => {\n        resCookies.delete(cookie.name); // Delete each filtered cookie\n    });\n}\n/**\n * Unconditionally adds strict cache-control headers to the response.\n *\n * This ensures the response is not cached by CDNs or other shared caches.\n * It is now the caller's responsibility to decide when to call this function.\n *\n * Usage:\n * Call this function whenever a `Set-Cookie` header is being written\n * for session management or any other sensitive data that must not be cached.\n */\nfunction addCacheControlHeadersForSession(res) {\n    res.headers.set(\"Cache-Control\", \"private, no-cache, no-store, must-revalidate, max-age=0\");\n    res.headers.set(\"Pragma\", \"no-cache\");\n    res.headers.set(\"Expires\", \"0\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractSessionStore: () => (/* reexport safe */ _session_abstract_session_store__WEBPACK_IMPORTED_MODULE_3__.AbstractSessionStore),\n/* harmony export */   Auth0Client: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.Auth0Client),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _auth_client__WEBPACK_IMPORTED_MODULE_1__.AuthClient),\n/* harmony export */   TransactionStore: () => (/* reexport safe */ _transaction_store__WEBPACK_IMPORTED_MODULE_2__.TransactionStore)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/client.js\");\n/* harmony import */ var _auth_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth-client */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/auth-client.js\");\n/* harmony import */ var _transaction_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transaction-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/transaction-store.js\");\n/* harmony import */ var _session_abstract_session_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./session/abstract-session-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/abstract-session-store.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L3NlcnZlci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QztBQUNJO0FBQ1k7QUFDaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxAYXV0aDBcXG5leHRqcy1hdXRoMFxcZGlzdFxcc2VydmVyXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBBdXRoMENsaWVudCB9IGZyb20gXCIuL2NsaWVudFwiO1xuZXhwb3J0IHsgQXV0aENsaWVudCB9IGZyb20gXCIuL2F1dGgtY2xpZW50XCI7XG5leHBvcnQgeyBUcmFuc2FjdGlvblN0b3JlIH0gZnJvbSBcIi4vdHJhbnNhY3Rpb24tc3RvcmVcIjtcbmV4cG9ydCB7IEFic3RyYWN0U2Vzc2lvblN0b3JlIH0gZnJvbSBcIi4vc2Vzc2lvbi9hYnN0cmFjdC1zZXNzaW9uLXN0b3JlXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/abstract-session-store.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/session/abstract-session-store.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractSessionStore: () => (/* binding */ AbstractSessionStore)\n/* harmony export */ });\nconst SESSION_COOKIE_NAME = \"__session\";\nclass AbstractSessionStore {\n    constructor({ secret, rolling = true, absoluteDuration = 60 * 60 * 24 * 3, // 3 days in seconds\n    inactivityDuration = 60 * 60 * 24 * 1, // 1 day in seconds\n    store, cookieOptions }) {\n        this.secret = secret;\n        this.rolling = rolling;\n        this.absoluteDuration = absoluteDuration;\n        this.inactivityDuration = inactivityDuration;\n        this.store = store;\n        this.sessionCookieName = cookieOptions?.name ?? SESSION_COOKIE_NAME;\n        this.cookieConfig = {\n            httpOnly: true,\n            sameSite: cookieOptions?.sameSite ?? \"lax\",\n            secure: cookieOptions?.secure ?? false,\n            path: cookieOptions?.path ?? \"/\",\n            domain: cookieOptions?.domain,\n            transient: cookieOptions?.transient\n        };\n    }\n    /**\n     * epoch returns the time since unix epoch in seconds.\n     */\n    epoch() {\n        return (Date.now() / 1000) | 0;\n    }\n    /**\n     * calculateMaxAge calculates the max age of the session based on createdAt and the rolling and absolute durations.\n     */\n    calculateMaxAge(createdAt) {\n        if (!this.rolling) {\n            return this.absoluteDuration;\n        }\n        const updatedAt = this.epoch();\n        const expiresAt = Math.min(updatedAt + this.inactivityDuration, createdAt + this.absoluteDuration);\n        const maxAge = expiresAt - this.epoch();\n        return maxAge > 0 ? maxAge : 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L3NlcnZlci9zZXNzaW9uL2Fic3RyYWN0LXNlc3Npb24tc3RvcmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCxrQkFBa0I7QUFDbEI7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxAYXV0aDBcXG5leHRqcy1hdXRoMFxcZGlzdFxcc2VydmVyXFxzZXNzaW9uXFxhYnN0cmFjdC1zZXNzaW9uLXN0b3JlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFNFU1NJT05fQ09PS0lFX05BTUUgPSBcIl9fc2Vzc2lvblwiO1xuZXhwb3J0IGNsYXNzIEFic3RyYWN0U2Vzc2lvblN0b3JlIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHNlY3JldCwgcm9sbGluZyA9IHRydWUsIGFic29sdXRlRHVyYXRpb24gPSA2MCAqIDYwICogMjQgKiAzLCAvLyAzIGRheXMgaW4gc2Vjb25kc1xuICAgIGluYWN0aXZpdHlEdXJhdGlvbiA9IDYwICogNjAgKiAyNCAqIDEsIC8vIDEgZGF5IGluIHNlY29uZHNcbiAgICBzdG9yZSwgY29va2llT3B0aW9ucyB9KSB7XG4gICAgICAgIHRoaXMuc2VjcmV0ID0gc2VjcmV0O1xuICAgICAgICB0aGlzLnJvbGxpbmcgPSByb2xsaW5nO1xuICAgICAgICB0aGlzLmFic29sdXRlRHVyYXRpb24gPSBhYnNvbHV0ZUR1cmF0aW9uO1xuICAgICAgICB0aGlzLmluYWN0aXZpdHlEdXJhdGlvbiA9IGluYWN0aXZpdHlEdXJhdGlvbjtcbiAgICAgICAgdGhpcy5zdG9yZSA9IHN0b3JlO1xuICAgICAgICB0aGlzLnNlc3Npb25Db29raWVOYW1lID0gY29va2llT3B0aW9ucz8ubmFtZSA/PyBTRVNTSU9OX0NPT0tJRV9OQU1FO1xuICAgICAgICB0aGlzLmNvb2tpZUNvbmZpZyA9IHtcbiAgICAgICAgICAgIGh0dHBPbmx5OiB0cnVlLFxuICAgICAgICAgICAgc2FtZVNpdGU6IGNvb2tpZU9wdGlvbnM/LnNhbWVTaXRlID8/IFwibGF4XCIsXG4gICAgICAgICAgICBzZWN1cmU6IGNvb2tpZU9wdGlvbnM/LnNlY3VyZSA/PyBmYWxzZSxcbiAgICAgICAgICAgIHBhdGg6IGNvb2tpZU9wdGlvbnM/LnBhdGggPz8gXCIvXCIsXG4gICAgICAgICAgICBkb21haW46IGNvb2tpZU9wdGlvbnM/LmRvbWFpbixcbiAgICAgICAgICAgIHRyYW5zaWVudDogY29va2llT3B0aW9ucz8udHJhbnNpZW50XG4gICAgICAgIH07XG4gICAgfVxuICAgIC8qKlxuICAgICAqIGVwb2NoIHJldHVybnMgdGhlIHRpbWUgc2luY2UgdW5peCBlcG9jaCBpbiBzZWNvbmRzLlxuICAgICAqL1xuICAgIGVwb2NoKCkge1xuICAgICAgICByZXR1cm4gKERhdGUubm93KCkgLyAxMDAwKSB8IDA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIGNhbGN1bGF0ZU1heEFnZSBjYWxjdWxhdGVzIHRoZSBtYXggYWdlIG9mIHRoZSBzZXNzaW9uIGJhc2VkIG9uIGNyZWF0ZWRBdCBhbmQgdGhlIHJvbGxpbmcgYW5kIGFic29sdXRlIGR1cmF0aW9ucy5cbiAgICAgKi9cbiAgICBjYWxjdWxhdGVNYXhBZ2UoY3JlYXRlZEF0KSB7XG4gICAgICAgIGlmICghdGhpcy5yb2xsaW5nKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5hYnNvbHV0ZUR1cmF0aW9uO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRBdCA9IHRoaXMuZXBvY2goKTtcbiAgICAgICAgY29uc3QgZXhwaXJlc0F0ID0gTWF0aC5taW4odXBkYXRlZEF0ICsgdGhpcy5pbmFjdGl2aXR5RHVyYXRpb24sIGNyZWF0ZWRBdCArIHRoaXMuYWJzb2x1dGVEdXJhdGlvbik7XG4gICAgICAgIGNvbnN0IG1heEFnZSA9IGV4cGlyZXNBdCAtIHRoaXMuZXBvY2goKTtcbiAgICAgICAgcmV0dXJuIG1heEFnZSA+IDAgPyBtYXhBZ2UgOiAwO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/abstract-session-store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/normalize-session.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/session/normalize-session.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LEGACY_COOKIE_NAME: () => (/* binding */ LEGACY_COOKIE_NAME),\n/* harmony export */   LegacySession: () => (/* binding */ LegacySession),\n/* harmony export */   normalizeStatefulSession: () => (/* binding */ normalizeStatefulSession),\n/* harmony export */   normalizeStatelessSession: () => (/* binding */ normalizeStatelessSession)\n/* harmony export */ });\nconst LEGACY_COOKIE_NAME = \"appSession\";\n/**\n * The user's session.\n */\nclass LegacySession {\n    constructor(user) {\n        this.user = user;\n    }\n}\nfunction normalizeStatelessSession(sessionCookie) {\n    // if the session cookie has an `iat` claim in the protected header, it's a legacy cookie\n    // otherwise, it's the new session cookie format and no transformation is needed\n    if (sessionCookie.protectedHeader.iat) {\n        const legacySession = sessionCookie;\n        return convertFromLegacy(legacySession.protectedHeader, legacySession.payload);\n    }\n    return sessionCookie.payload;\n}\nfunction normalizeStatefulSession(sessionData) {\n    if (sessionData.header?.iat) {\n        const legacySession = sessionData;\n        return convertFromLegacy(legacySession.header, legacySession.data);\n    }\n    return sessionData;\n}\nfunction convertFromLegacy(header, session) {\n    const userClaims = session.user;\n    return {\n        user: userClaims,\n        tokenSet: {\n            idToken: session.idToken ?? undefined,\n            accessToken: session.accessToken ?? undefined,\n            scope: session.accessTokenScope,\n            refreshToken: session.refreshToken,\n            expiresAt: session.accessTokenExpiresAt\n        },\n        internal: {\n            sid: userClaims.sid,\n            createdAt: header.iat\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/normalize-session.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/stateful-session-store.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/session/stateful-session-store.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatefulSessionStore: () => (/* binding */ StatefulSessionStore)\n/* harmony export */ });\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cookies */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js\");\n/* harmony import */ var _abstract_session_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./abstract-session-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/abstract-session-store.js\");\n/* harmony import */ var _normalize_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./normalize-session */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/normalize-session.js\");\n\n\n\nconst generateId = () => {\n    const bytes = new Uint8Array(16);\n    crypto.getRandomValues(bytes);\n    return Array.from(bytes)\n        .map((b) => b.toString(16).padStart(2, \"0\"))\n        .join(\"\");\n};\nclass StatefulSessionStore extends _abstract_session_store__WEBPACK_IMPORTED_MODULE_1__.AbstractSessionStore {\n    constructor({ secret, store, rolling, absoluteDuration, inactivityDuration, cookieOptions }) {\n        super({\n            secret,\n            rolling,\n            absoluteDuration,\n            inactivityDuration,\n            cookieOptions\n        });\n        this.store = store;\n    }\n    async get(reqCookies) {\n        const cookie = reqCookies.get(this.sessionCookieName) ||\n            reqCookies.get(_normalize_session__WEBPACK_IMPORTED_MODULE_2__.LEGACY_COOKIE_NAME);\n        if (!cookie || !cookie.value) {\n            return null;\n        }\n        // we attempt to extract the session ID by decrypting the cookie value (assuming it's a JWE, v4+) first\n        // if that fails, we attempt to verify the cookie value as a signed cookie (legacy, v3-)\n        // if both fail, we return null\n        // this ensures that v3 sessions are respected and can be transparently rolled over to v4+ sessions\n        let sessionId = null;\n        try {\n            const { payload: sessionCookie } = await _cookies__WEBPACK_IMPORTED_MODULE_0__.decrypt(cookie.value, this.secret);\n            sessionId = sessionCookie.id;\n        }\n        catch (e) {\n            // the session cookie could not be decrypted, try to verify if it's a legacy session\n            if (e.code === \"ERR_JWE_INVALID\") {\n                const legacySessionId = await _cookies__WEBPACK_IMPORTED_MODULE_0__.verifySigned(cookie.name, cookie.value, this.secret);\n                if (!legacySessionId) {\n                    return null;\n                }\n                sessionId = legacySessionId;\n            }\n        }\n        if (!sessionId) {\n            return null;\n        }\n        const session = await this.store.get(sessionId);\n        if (!session) {\n            return null;\n        }\n        return (0,_normalize_session__WEBPACK_IMPORTED_MODULE_2__.normalizeStatefulSession)(session);\n    }\n    async set(reqCookies, resCookies, session, isNew = false) {\n        // check if a session already exists. If so, maintain the existing session ID\n        let sessionId = null;\n        const cookieValue = reqCookies.get(this.sessionCookieName)?.value;\n        if (cookieValue) {\n            const { payload: sessionCookie } = await _cookies__WEBPACK_IMPORTED_MODULE_0__.decrypt(cookieValue, this.secret);\n            sessionId = sessionCookie.id;\n        }\n        // if this is a new session created by a new login we need to remove the old session\n        // from the store and regenerate the session ID to prevent session fixation.\n        if (sessionId && isNew) {\n            await this.store.delete(sessionId);\n            sessionId = generateId();\n        }\n        if (!sessionId) {\n            sessionId = generateId();\n        }\n        const maxAge = this.calculateMaxAge(session.internal.createdAt);\n        const expiration = Date.now() / 1000 + maxAge;\n        const jwe = await _cookies__WEBPACK_IMPORTED_MODULE_0__.encrypt({\n            id: sessionId\n        }, this.secret, expiration);\n        resCookies.set(this.sessionCookieName, jwe.toString(), {\n            ...this.cookieConfig,\n            maxAge\n        });\n        await this.store.set(sessionId, session);\n        // to enable read-after-write in the same request for middleware\n        reqCookies.set(this.sessionCookieName, jwe.toString());\n        // Any existing v3 cookie can also be deleted once we have set a v4 cookie.\n        // In stateful sessions, we do not have to worry about chunking.\n        if (this.sessionCookieName !== _normalize_session__WEBPACK_IMPORTED_MODULE_2__.LEGACY_COOKIE_NAME &&\n            reqCookies.has(_normalize_session__WEBPACK_IMPORTED_MODULE_2__.LEGACY_COOKIE_NAME)) {\n            resCookies.delete(_normalize_session__WEBPACK_IMPORTED_MODULE_2__.LEGACY_COOKIE_NAME);\n        }\n    }\n    async delete(reqCookies, resCookies) {\n        const cookieValue = reqCookies.get(this.sessionCookieName)?.value;\n        await resCookies.delete(this.sessionCookieName);\n        if (!cookieValue) {\n            return;\n        }\n        const { payload: session } = await _cookies__WEBPACK_IMPORTED_MODULE_0__.decrypt(cookieValue, this.secret);\n        await this.store.delete(session.id);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/stateful-session-store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/stateless-session-store.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/session/stateless-session-store.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatelessSessionStore: () => (/* binding */ StatelessSessionStore)\n/* harmony export */ });\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cookies */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js\");\n/* harmony import */ var _abstract_session_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./abstract-session-store */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/abstract-session-store.js\");\n/* harmony import */ var _normalize_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./normalize-session */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/normalize-session.js\");\n\n\n\nclass StatelessSessionStore extends _abstract_session_store__WEBPACK_IMPORTED_MODULE_1__.AbstractSessionStore {\n    constructor({ secret, rolling, absoluteDuration, inactivityDuration, cookieOptions }) {\n        super({\n            secret,\n            rolling,\n            absoluteDuration,\n            inactivityDuration,\n            cookieOptions\n        });\n        this.connectionTokenSetsCookieName = \"__FC\";\n    }\n    async get(reqCookies) {\n        const cookieValue = _cookies__WEBPACK_IMPORTED_MODULE_0__.getChunkedCookie(this.sessionCookieName, reqCookies) ??\n            _cookies__WEBPACK_IMPORTED_MODULE_0__.getChunkedCookie(_normalize_session__WEBPACK_IMPORTED_MODULE_2__.LEGACY_COOKIE_NAME, reqCookies, true);\n        if (!cookieValue) {\n            return null;\n        }\n        const originalSession = await _cookies__WEBPACK_IMPORTED_MODULE_0__.decrypt(cookieValue, this.secret);\n        const normalizedStatelessSession = (0,_normalize_session__WEBPACK_IMPORTED_MODULE_2__.normalizeStatelessSession)(originalSession);\n        // As connection access tokens are stored in seperate cookies,\n        // we need to get all cookies and only use those that are prefixed with `this.connectionTokenSetsCookieName`\n        const connectionTokenSets = await Promise.all(this.getConnectionTokenSetsCookies(reqCookies).map((cookie) => _cookies__WEBPACK_IMPORTED_MODULE_0__.decrypt(cookie.value, this.secret)));\n        return {\n            ...normalizedStatelessSession,\n            // Ensure that when there are no connection token sets, we omit the property.\n            ...(connectionTokenSets.length\n                ? {\n                    connectionTokenSets: connectionTokenSets.map((tokenSet) => tokenSet.payload)\n                }\n                : {})\n        };\n    }\n    /**\n     * save adds the encrypted session cookie as a `Set-Cookie` header.\n     */\n    async set(reqCookies, resCookies, session) {\n        const { connectionTokenSets, ...originalSession } = session;\n        const maxAge = this.calculateMaxAge(session.internal.createdAt);\n        const expiration = Math.floor(Date.now() / 1000) + maxAge;\n        const jwe = await _cookies__WEBPACK_IMPORTED_MODULE_0__.encrypt(originalSession, this.secret, expiration);\n        const cookieValue = jwe.toString();\n        const options = {\n            ...this.cookieConfig,\n            maxAge\n        };\n        _cookies__WEBPACK_IMPORTED_MODULE_0__.setChunkedCookie(this.sessionCookieName, cookieValue, options, reqCookies, resCookies);\n        // Store connection access tokens, each in its own cookie\n        if (connectionTokenSets?.length) {\n            await Promise.all(connectionTokenSets.map((connectionTokenSet, index) => this.storeInCookie(reqCookies, resCookies, connectionTokenSet, `${this.connectionTokenSetsCookieName}_${index}`, maxAge)));\n        }\n        // Any existing v3 cookie can be deleted as soon as we have set a v4 cookie.\n        // In stateless sessions, we do have to ensure we delete all chunks.\n        _cookies__WEBPACK_IMPORTED_MODULE_0__.deleteChunkedCookie(_normalize_session__WEBPACK_IMPORTED_MODULE_2__.LEGACY_COOKIE_NAME, reqCookies, resCookies, true);\n    }\n    async delete(reqCookies, resCookies) {\n        _cookies__WEBPACK_IMPORTED_MODULE_0__.deleteChunkedCookie(this.sessionCookieName, reqCookies, resCookies);\n        this.getConnectionTokenSetsCookies(reqCookies).forEach((cookie) => resCookies.delete(cookie.name));\n    }\n    async storeInCookie(reqCookies, resCookies, session, cookieName, maxAge) {\n        const expiration = Math.floor(Date.now() / 1000 + maxAge);\n        const jwe = await _cookies__WEBPACK_IMPORTED_MODULE_0__.encrypt(session, this.secret, expiration);\n        const cookieValue = jwe.toString();\n        resCookies.set(cookieName, jwe.toString(), {\n            ...this.cookieConfig,\n            maxAge\n        });\n        // to enable read-after-write in the same request for middleware\n        reqCookies.set(cookieName, cookieValue);\n        // check if the session cookie size exceeds 4096 bytes, and if so, log a warning\n        const cookieJarSizeTest = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(new Headers());\n        cookieJarSizeTest.set(cookieName, cookieValue, {\n            ...this.cookieConfig,\n            maxAge\n        });\n        if (new TextEncoder().encode(cookieJarSizeTest.toString()).length >= 4096) {\n            // if the cookie is the session cookie, log a warning with additional information about the claims and user profile.\n            if (cookieName === this.sessionCookieName) {\n                console.warn(`The ${cookieName} cookie size exceeds 4096 bytes, which may cause issues in some browsers. ` +\n                    \"Consider removing any unnecessary custom claims from the access token or the user profile. \" +\n                    \"Alternatively, you can use a stateful session implementation to store the session data in a data store.\");\n            }\n            else {\n                console.warn(`The ${cookieName} cookie size exceeds 4096 bytes, which may cause issues in some browsers. ` +\n                    \"You can use a stateful session implementation to store the session data in a data store.\");\n            }\n        }\n    }\n    getConnectionTokenSetsCookies(cookies) {\n        return cookies\n            .getAll()\n            .filter((cookie) => cookie.name.startsWith(this.connectionTokenSetsCookieName));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/session/stateless-session-store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/transaction-store.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/transaction-store.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionStore: () => (/* binding */ TransactionStore)\n/* harmony export */ });\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cookies */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/cookies.js\");\n\nconst TRANSACTION_COOKIE_PREFIX = \"__txn_\";\n/**\n * TransactionStore is responsible for storing the state required to successfully complete\n * an authentication transaction. The store relies on encrypted, stateless cookies to store\n * the transaction state.\n */\nclass TransactionStore {\n    constructor({ secret, cookieOptions }) {\n        this.secret = secret;\n        this.transactionCookiePrefix =\n            cookieOptions?.prefix ?? TRANSACTION_COOKIE_PREFIX;\n        this.cookieConfig = {\n            httpOnly: true,\n            sameSite: cookieOptions?.sameSite ?? \"lax\", // required to allow the cookie to be sent on the callback request\n            secure: cookieOptions?.secure ?? false,\n            path: cookieOptions?.path ?? \"/\",\n            maxAge: 60 * 60 // 1 hour in seconds\n        };\n    }\n    /**\n     * Returns the name of the cookie used to store the transaction state.\n     * The cookie name is derived from the state parameter to prevent collisions\n     * between different transactions.\n     */\n    getTransactionCookieName(state) {\n        return `${this.transactionCookiePrefix}${state}`;\n    }\n    /**\n     * Returns the configured prefix for transaction cookies.\n     */\n    getCookiePrefix() {\n        return this.transactionCookiePrefix;\n    }\n    async save(resCookies, transactionState) {\n        const expiration = Math.floor(Date.now() / 1000 + this.cookieConfig.maxAge);\n        const jwe = await _cookies__WEBPACK_IMPORTED_MODULE_0__.encrypt(transactionState, this.secret, expiration);\n        if (!transactionState.state) {\n            throw new Error(\"Transaction state is required\");\n        }\n        resCookies.set(this.getTransactionCookieName(transactionState.state), jwe.toString(), this.cookieConfig);\n    }\n    async get(reqCookies, state) {\n        const cookieName = this.getTransactionCookieName(state);\n        const cookieValue = reqCookies.get(cookieName)?.value;\n        if (!cookieValue) {\n            return null;\n        }\n        return _cookies__WEBPACK_IMPORTED_MODULE_0__.decrypt(cookieValue, this.secret);\n    }\n    async delete(resCookies, state) {\n        await resCookies.delete(this.getTransactionCookieName(state));\n    }\n    /**\n     * Deletes all transaction cookies based on the configured prefix.\n     */\n    async deleteAll(reqCookies, resCookies) {\n        const txnPrefix = this.getCookiePrefix();\n        reqCookies.getAll().forEach((cookie) => {\n            if (cookie.name.startsWith(txnPrefix)) {\n                resCookies.delete(cookie.name);\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/transaction-store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/user.js":
/*!**************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/server/user.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterClaims: () => (/* binding */ filterClaims)\n/* harmony export */ });\nconst DEFAULT_ALLOWED_CLAIMS = [\n    \"sub\",\n    \"name\",\n    \"nickname\",\n    \"given_name\",\n    \"family_name\",\n    \"picture\",\n    \"email\",\n    \"email_verified\",\n    \"org_id\"\n];\nfunction filterClaims(claims) {\n    return Object.keys(claims).reduce((acc, key) => {\n        if (DEFAULT_ALLOWED_CLAIMS.includes(key)) {\n            acc[key] = claims[key];\n        }\n        return acc;\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L3NlcnZlci91c2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSyxJQUFJO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxAYXV0aDBcXG5leHRqcy1hdXRoMFxcZGlzdFxcc2VydmVyXFx1c2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IERFRkFVTFRfQUxMT1dFRF9DTEFJTVMgPSBbXG4gICAgXCJzdWJcIixcbiAgICBcIm5hbWVcIixcbiAgICBcIm5pY2tuYW1lXCIsXG4gICAgXCJnaXZlbl9uYW1lXCIsXG4gICAgXCJmYW1pbHlfbmFtZVwiLFxuICAgIFwicGljdHVyZVwiLFxuICAgIFwiZW1haWxcIixcbiAgICBcImVtYWlsX3ZlcmlmaWVkXCIsXG4gICAgXCJvcmdfaWRcIlxuXTtcbmV4cG9ydCBmdW5jdGlvbiBmaWx0ZXJDbGFpbXMoY2xhaW1zKSB7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKGNsYWltcykucmVkdWNlKChhY2MsIGtleSkgPT4ge1xuICAgICAgICBpZiAoREVGQVVMVF9BTExPV0VEX0NMQUlNUy5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgICAgICBhY2Nba2V5XSA9IGNsYWltc1trZXldO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhY2M7XG4gICAgfSwge30pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/user.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/utils/pathUtils.js":
/*!******************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/utils/pathUtils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureNoLeadingSlash: () => (/* binding */ ensureNoLeadingSlash),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   removeTrailingSlash: () => (/* binding */ removeTrailingSlash)\n/* harmony export */ });\nfunction ensureTrailingSlash(value) {\n    return value && !value.endsWith(\"/\") ? `${value}/` : value;\n}\nfunction ensureNoLeadingSlash(value) {\n    return value && value.startsWith(\"/\")\n        ? value.substring(1, value.length)\n        : value;\n}\nconst removeTrailingSlash = (path) => path.endsWith(\"/\") ? path.slice(0, -1) : path;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L3V0aWxzL3BhdGhVdGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQLDhDQUE4QyxNQUFNO0FBQ3BEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcQGF1dGgwXFxuZXh0anMtYXV0aDBcXGRpc3RcXHV0aWxzXFxwYXRoVXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGVuc3VyZVRyYWlsaW5nU2xhc2godmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUgJiYgIXZhbHVlLmVuZHNXaXRoKFwiL1wiKSA/IGAke3ZhbHVlfS9gIDogdmFsdWU7XG59XG5leHBvcnQgZnVuY3Rpb24gZW5zdXJlTm9MZWFkaW5nU2xhc2godmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUgJiYgdmFsdWUuc3RhcnRzV2l0aChcIi9cIilcbiAgICAgICAgPyB2YWx1ZS5zdWJzdHJpbmcoMSwgdmFsdWUubGVuZ3RoKVxuICAgICAgICA6IHZhbHVlO1xufVxuZXhwb3J0IGNvbnN0IHJlbW92ZVRyYWlsaW5nU2xhc2ggPSAocGF0aCkgPT4gcGF0aC5lbmRzV2l0aChcIi9cIikgPyBwYXRoLnNsaWNlKDAsIC0xKSA6IHBhdGg7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/utils/pathUtils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/dist/utils/url-helpers.js":
/*!********************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/utils/url-helpers.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toSafeRedirect: () => (/* binding */ toSafeRedirect)\n/* harmony export */ });\nfunction toSafeRedirect(dangerousRedirect, safeBaseUrl) {\n    let url;\n    try {\n        url = new URL(dangerousRedirect, safeBaseUrl);\n    }\n    catch (e) {\n        return undefined;\n    }\n    if (url.origin === safeBaseUrl.origin) {\n        return url.toString();\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L3V0aWxzL3VybC1oZWxwZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXEBhdXRoMFxcbmV4dGpzLWF1dGgwXFxkaXN0XFx1dGlsc1xcdXJsLWhlbHBlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHRvU2FmZVJlZGlyZWN0KGRhbmdlcm91c1JlZGlyZWN0LCBzYWZlQmFzZVVybCkge1xuICAgIGxldCB1cmw7XG4gICAgdHJ5IHtcbiAgICAgICAgdXJsID0gbmV3IFVSTChkYW5nZXJvdXNSZWRpcmVjdCwgc2FmZUJhc2VVcmwpO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgICBpZiAodXJsLm9yaWdpbiA9PT0gc2FmZUJhc2VVcmwub3JpZ2luKSB7XG4gICAgICAgIHJldHVybiB1cmwudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth0/nextjs-auth0/dist/utils/url-helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth0/nextjs-auth0/package.json":
/*!*******************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/package.json ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"@auth0/nextjs-auth0","version":"4.6.1","description":"Auth0 Next.js SDK","repository":{"type":"git","url":"git+https://github.com/auth0/nextjs-auth0.git"},"keywords":["auth0","next.js","react","oidc","authentication","vercel"],"author":"Auth0 (https://auth0.com)","license":"MIT","bugs":{"url":"https://github.com/auth0/nextjs-auth0/issues"},"homepage":"https://github.com/auth0/nextjs-auth0#readme","devDependencies":{"msw":"^2.7.5","@eslint/js":"^9.20.0","@ianvs/prettier-plugin-sort-imports":"^4.3.1","@playwright/test":"^1.48.2","@stylistic/eslint-plugin-ts":"^3.1.0","@testing-library/react":"^16.3.0","@types/node":"^22.8.6","@types/react":"*","@types/react-dom":"*","@vitest/coverage-v8":"2.1.9","eslint":"^9.20.0","eslint-config-prettier":"^10.0.1","eslint-plugin-prettier":"^5.2.3","eslint-plugin-react":"^7.37.4","globals":"^15.14.0","jsdom":"^26.1.0","next":"15.2.3","prettier":"^3.3.3","typedoc":"^0.28.4","typescript":"^5.6.3","typescript-eslint":"^8.23.0","vite":"^5.4.11","vitest":"^2.1.4"},"peerDependencies":{"next":"^14.2.25 || ^15.2.3","react":"^18.0.0 || ^19.0.0 || ^19.0.0-0","react-dom":"^18.0.0 || ^19.0.0 || ^19.0.0-0"},"exports":{".":{"import":"./dist/client/index.js"},"./server":{"import":"./dist/server/index.js"},"./errors":{"import":"./dist/errors/index.js"},"./types":{"import":"./dist/types/index.d.ts"},"./testing":{"import":"./dist/testing/index.js"}},"dependencies":{"@edge-runtime/cookies":"^5.0.1","@panva/hkdf":"^1.2.1","jose":"^5.9.6","oauth4webapi":"^3.1.2","swr":"^2.2.5"},"publishConfig":{"access":"public"},"typesVersions":{"*":{"testing":["./dist/testing/index.d.ts"],"types":["./dist/types/index.d.ts"],"server":["./dist/server/index.d.ts"],"errors":["./dist/errors/index.d.ts"],"*":["./dist/client/*","./dist/client/index.d.ts"]}},"files":["dist"],"scripts":{"build":"tsc","build:watch":"tsc -w","test:unit":"vitest","test:coverage":"vitest run --coverage","test:e2e":"playwright test","install:examples":"pnpm install --filter ./examples/with-next-intl --shamefully-hoist && pnpm install --filter ./examples/with-shadcn --shamefully-hoist","docs":"typedoc","lint":"tsc --noEmit && eslint --fix ./src"}}');

/***/ }),

/***/ "(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js":
/*!************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useUser auto */ \nfunction useUser() {\n    const { data, error, isLoading, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(process.env.NEXT_PUBLIC_PROFILE_ROUTE || \"/auth/profile\", {\n        \"useUser.useSWR\": (...args)=>fetch(...args).then({\n                \"useUser.useSWR\": (res)=>{\n                    if (!res.ok) {\n                        throw new Error(\"Unauthorized\");\n                    }\n                    return res.json();\n                }\n            }[\"useUser.useSWR\"])\n    }[\"useUser.useSWR\"]);\n    if (error) {\n        return {\n            user: null,\n            isLoading: false,\n            error,\n            invalidate: ()=>mutate()\n        };\n    }\n    if (data) {\n        return {\n            user: data,\n            isLoading: false,\n            error: null,\n            invalidate: ()=>mutate()\n        };\n    }\n    return {\n        user: data,\n        isLoading,\n        error,\n        invalidate: ()=>mutate()\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/hooks/use-user.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Auth0Provider: () => (/* binding */ Auth0Provider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Auth0Provider auto */ \n\nfunction Auth0Provider({ user, children }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(swr__WEBPACK_IMPORTED_MODULE_1__.SWRConfig, {\n        value: {\n            fallback: {\n                [process.env.NEXT_PUBLIC_PROFILE_ROUTE || \"/auth/profile\"]: user\n            }\n        }\n    }, children);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF1dGgwL25leHRqcy1hdXRoMC9kaXN0L2NsaWVudC9wcm92aWRlcnMvYXV0aDAtcHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzttRUFDMEI7QUFDTTtBQUN6QixTQUFTRSxjQUFjLEVBQUVDLElBQUksRUFBRUMsUUFBUSxFQUFFO0lBQzVDLHFCQUFRSiwwREFBbUIsQ0FBQ0MsMENBQVNBLEVBQUU7UUFBRUssT0FBTztZQUN4Q0MsVUFBVTtnQkFDTixDQUFDQyxRQUFRQyxHQUFHLENBQUNDLHlCQUF5QixJQUFJLGdCQUFnQixFQUFFUDtZQUNoRTtRQUNKO0lBQUUsR0FBR0M7QUFDYiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXEBhdXRoMFxcbmV4dGpzLWF1dGgwXFxkaXN0XFxjbGllbnRcXHByb3ZpZGVyc1xcYXV0aDAtcHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBTV1JDb25maWcgfSBmcm9tIFwic3dyXCI7XG5leHBvcnQgZnVuY3Rpb24gQXV0aDBQcm92aWRlcih7IHVzZXIsIGNoaWxkcmVuIH0pIHtcbiAgICByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoU1dSQ29uZmlnLCB7IHZhbHVlOiB7XG4gICAgICAgICAgICBmYWxsYmFjazoge1xuICAgICAgICAgICAgICAgIFtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19QUk9GSUxFX1JPVVRFIHx8IFwiL2F1dGgvcHJvZmlsZVwiXTogdXNlclxuICAgICAgICAgICAgfVxuICAgICAgICB9IH0sIGNoaWxkcmVuKSk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTV1JDb25maWciLCJBdXRoMFByb3ZpZGVyIiwidXNlciIsImNoaWxkcmVuIiwiY3JlYXRlRWxlbWVudCIsInZhbHVlIiwiZmFsbGJhY2siLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfUFJPRklMRV9ST1VURSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/providers/auth0-provider.js\n");

/***/ })

};
;