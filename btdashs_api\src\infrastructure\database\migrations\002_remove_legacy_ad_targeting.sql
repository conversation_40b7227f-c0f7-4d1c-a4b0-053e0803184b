-- Migration: Remove legacy ad-level targeting system
-- File: 002_remove_legacy_ad_targeting.sql
-- Description: Removes the deprecated ad_targets table and related infrastructure
--              since targeting is now handled at the campaign level via the 
--              ad_campaigns.targeting JSON column

-- ============================================================================
-- BACKUP EXISTING DATA (Optional - for safety)
-- ============================================================================

-- Create backup table for ad_targets data (optional, can be removed after migration)
CREATE TABLE IF NOT EXISTS dtm_ads.ad_targets_backup AS 
SELECT * FROM dtm_ads.ad_targets;

-- ============================================================================
-- REMOVE LEGACY TARGETING INFRASTRUCTURE
-- ============================================================================

-- Drop indexes related to ad_targets table
DROP INDEX IF EXISTS dtm_ads.idx_ad_targets_ad_id;
DROP INDEX IF EXISTS dtm_ads.idx_ad_targets_key;
DROP INDEX IF EXISTS dtm_ads.idx_ad_targets_value;
DROP INDEX IF EXISTS dtm_ads.idx_ad_targets_key_value;

-- Drop foreign key constraints
ALTER TABLE dtm_ads.ad_targets DROP CONSTRAINT IF EXISTS fk_ad_targets_ad_id;

-- Drop check constraints
ALTER TABLE dtm_ads.ad_targets DROP CONSTRAINT IF EXISTS chk_ad_targets_operator;
ALTER TABLE dtm_ads.ad_targets DROP CONSTRAINT IF EXISTS chk_ad_targets_key;

-- Drop the ad_targets table entirely
DROP TABLE IF EXISTS dtm_ads.ad_targets;

-- ============================================================================
-- CLEANUP MIGRATION ARTIFACTS
-- ============================================================================

-- Remove any migration code that was adding columns to ad_targets
-- (These were in the original migration but are no longer needed)

-- ============================================================================
-- VERIFY CAMPAIGN TARGETING STRUCTURE
-- ============================================================================

-- Ensure ad_campaigns table has the targeting column with proper type
DO $$
BEGIN
    -- Check if targeting column exists and is JSONB
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'dtm_ads' 
        AND table_name = 'ad_campaigns' 
        AND column_name = 'targeting'
        AND data_type = 'jsonb'
    ) THEN
        -- Add targeting column if it doesn't exist
        ALTER TABLE dtm_ads.ad_campaigns 
        ADD COLUMN targeting JSONB DEFAULT NULL;
    END IF;
END $$;

-- Add index for campaign targeting queries (if not exists)
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_targeting 
    ON dtm_ads.ad_campaigns USING GIN (targeting);

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log migration completion
INSERT INTO dtm_ads.admin_actions (admin_id, action, entity_id, metadata, created_at)
SELECT 
    1, -- System admin ID
    'migration_completed',
    0, -- No specific entity
    '{"migration": "002_remove_legacy_ad_targeting", "description": "Removed deprecated ad_targets table, targeting now campaign-level only"}'::jsonb,
    CURRENT_TIMESTAMP
WHERE EXISTS (SELECT 1 FROM dtm_ads.admin_actions LIMIT 1); -- Only if admin_actions table exists

-- Add comment to document the change
COMMENT ON COLUMN dtm_ads.ad_campaigns.targeting IS 
'Campaign-level targeting rules stored as JSON. All ads in the campaign inherit these targeting settings. Replaces the deprecated ad_targets table.';
