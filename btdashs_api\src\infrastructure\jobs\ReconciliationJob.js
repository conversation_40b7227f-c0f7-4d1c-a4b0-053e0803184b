const cron = require('node-cron');
const PaymentReconciliationService = require('../../application/services/PaymentReconciliationService');
const logger = require('../../../logger');

class ReconciliationJob {
	constructor() {
		this.reconciliationService = new PaymentReconciliationService();
		this.isRunning = false;
		this.jobs = new Map();
	}

	/**
	 * Start reconciliation jobs
	 */
	start() {
		logger.info("Starting payment reconciliation jobs");

		// Daily reconciliation (every day at 2 AM)
		this.scheduleJob('daily', '0 2 * * *', async () => {
			await this.runDailyReconciliation();
		});

		// Weekly reconciliation (every Monday at 3 AM)
		this.scheduleJob('weekly', '0 3 * * 1', async () => {
			await this.runWeeklyReconciliation();
		});

		// Monthly reconciliation (first day of month at 4 AM)
		this.scheduleJob('monthly', '0 4 1 * *', async () => {
			await this.runMonthlyReconciliation();
		});

		this.isRunning = true;
		logger.info("Payment reconciliation jobs started successfully");
	}

	/**
	 * Stop reconciliation jobs
	 */
	stop() {
		logger.info("Stopping payment reconciliation jobs");

		for (const [name, job] of this.jobs) {
			job.destroy();
			logger.info(`Stopped reconciliation job: ${name}`);
		}

		this.jobs.clear();
		this.isRunning = false;
		logger.info("Payment reconciliation jobs stopped");
	}

	/**
	 * Schedule a reconciliation job
	 * @param {string} name - Job name
	 * @param {string} schedule - Cron schedule
	 * @param {Function} task - Task function
	 */
	scheduleJob(name, schedule, task) {
		const job = cron.schedule(schedule, async () => {
			try {
				logger.info(`Running reconciliation job: ${name}`);
				await task();
				logger.info(`Completed reconciliation job: ${name}`);
			} catch (error) {
				logger.error(`Reconciliation job failed: ${name}`, {
					error: error.message,
					stack: error.stack,
				});
			}
		}, {
			scheduled: false,
			timezone: process.env.TZ || 'UTC',
		});

		this.jobs.set(name, job);
		job.start();
		
		logger.info(`Scheduled reconciliation job: ${name} (${schedule})`);
	}

	/**
	 * Run daily reconciliation (last 24 hours)
	 */
	async runDailyReconciliation() {
		const endDate = new Date();
		const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);

		try {
			const report = await this.reconciliationService.runReconciliation({
				startDate,
				endDate,
				includeSuccessful: true,
				includeFailed: true,
			});

			logger.info("Daily reconciliation completed", {
				reconciliationId: report.reconciliation_id,
				totalDiscrepancies: report.summary.total_discrepancies,
				matchRate: report.summary.match_rate,
			});

			// Alert on high discrepancy rate
			if (report.summary.total_discrepancies > 0) {
				const discrepancyRate = (report.summary.total_discrepancies / report.summary.total_comparisons) * 100;
				if (discrepancyRate > 5) { // Alert if more than 5% discrepancies
					logger.warn("High discrepancy rate in daily reconciliation", {
						reconciliationId: report.reconciliation_id,
						discrepancyRate: discrepancyRate.toFixed(2) + '%',
						totalDiscrepancies: report.summary.total_discrepancies,
					});
				}
			}

			return report;

		} catch (error) {
			logger.error("Daily reconciliation failed", {
				error: error.message,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			});
			throw error;
		}
	}

	/**
	 * Run weekly reconciliation (last 7 days)
	 */
	async runWeeklyReconciliation() {
		const endDate = new Date();
		const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);

		try {
			const report = await this.reconciliationService.runReconciliation({
				startDate,
				endDate,
				includeSuccessful: true,
				includeFailed: true,
			});

			logger.info("Weekly reconciliation completed", {
				reconciliationId: report.reconciliation_id,
				totalDiscrepancies: report.summary.total_discrepancies,
				matchRate: report.summary.match_rate,
			});

			return report;

		} catch (error) {
			logger.error("Weekly reconciliation failed", {
				error: error.message,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			});
			throw error;
		}
	}

	/**
	 * Run monthly reconciliation (last 30 days)
	 */
	async runMonthlyReconciliation() {
		const endDate = new Date();
		const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);

		try {
			const report = await this.reconciliationService.runReconciliation({
				startDate,
				endDate,
				includeSuccessful: true,
				includeFailed: true,
			});

			logger.info("Monthly reconciliation completed", {
				reconciliationId: report.reconciliation_id,
				totalDiscrepancies: report.summary.total_discrepancies,
				matchRate: report.summary.match_rate,
			});

			return report;

		} catch (error) {
			logger.error("Monthly reconciliation failed", {
				error: error.message,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			});
			throw error;
		}
	}

	/**
	 * Get job status
	 * @returns {Object} Job status information
	 */
	getStatus() {
		const jobStatuses = {};
		
		for (const [name, job] of this.jobs) {
			jobStatuses[name] = {
				running: job.running,
				scheduled: job.scheduled,
			};
		}

		return {
			isRunning: this.isRunning,
			jobCount: this.jobs.size,
			jobs: jobStatuses,
		};
	}

	/**
	 * Run job manually
	 * @param {string} jobName - Name of job to run
	 */
	async runJobManually(jobName) {
		logger.info(`Manually triggering reconciliation job: ${jobName}`);

		switch (jobName) {
			case 'daily':
				return await this.runDailyReconciliation();
			case 'weekly':
				return await this.runWeeklyReconciliation();
			case 'monthly':
				return await this.runMonthlyReconciliation();
			default:
				throw new Error(`Unknown reconciliation job: ${jobName}`);
		}
	}

	/**
	 * Update job schedule
	 * @param {string} jobName - Name of job to update
	 * @param {string} newSchedule - New cron schedule
	 */
	updateJobSchedule(jobName, newSchedule) {
		const job = this.jobs.get(jobName);
		if (!job) {
			throw new Error(`Reconciliation job not found: ${jobName}`);
		}

		// Stop current job
		job.destroy();
		this.jobs.delete(jobName);

		// Create new job with updated schedule
		const taskMap = {
			'daily': () => this.runDailyReconciliation(),
			'weekly': () => this.runWeeklyReconciliation(),
			'monthly': () => this.runMonthlyReconciliation(),
		};

		const task = taskMap[jobName];
		if (!task) {
			throw new Error(`No task defined for reconciliation job: ${jobName}`);
		}

		this.scheduleJob(jobName, newSchedule, task);
		
		logger.info(`Updated schedule for reconciliation job: ${jobName} (${newSchedule})`);
	}

	/**
	 * Get reconciliation statistics
	 * @returns {Promise<Object>} Reconciliation statistics
	 */
	async getReconciliationStatistics() {
		try {
			const [dailyReport, weeklyReport] = await Promise.all([
				this.runDailyReconciliation(),
				this.runWeeklyReconciliation(),
			]);

			return {
				daily: {
					reconciliation_id: dailyReport.reconciliation_id,
					match_rate: dailyReport.summary.match_rate,
					total_discrepancies: dailyReport.summary.total_discrepancies,
				},
				weekly: {
					reconciliation_id: weeklyReport.reconciliation_id,
					match_rate: weeklyReport.summary.match_rate,
					total_discrepancies: weeklyReport.summary.total_discrepancies,
				},
				jobStatus: this.getStatus(),
			};

		} catch (error) {
			logger.error("Failed to get reconciliation statistics", {
				error: error.message,
			});
			throw error;
		}
	}
}

// Create singleton instance
const reconciliationJob = new ReconciliationJob();

// Graceful shutdown handling
process.on('SIGTERM', () => {
	logger.info("Received SIGTERM, stopping reconciliation jobs");
	reconciliationJob.stop();
});

process.on('SIGINT', () => {
	logger.info("Received SIGINT, stopping reconciliation jobs");
	reconciliationJob.stop();
});

module.exports = reconciliationJob;
