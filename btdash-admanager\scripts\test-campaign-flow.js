#!/usr/bin/env node

/**
 * Campaign Payment Flow Testing Script
 * 
 * This script helps test the complete campaign payment flow:
 * 1. Campaign creation → pending status
 * 2. Admin approval → approved status  
 * 3. User payment → active status
 */

const colors = {
	reset: '\x1b[0m',
	bright: '\x1b[1m',
	red: '\x1b[31m',
	green: '\x1b[32m',
	yellow: '\x1b[33m',
	blue: '\x1b[34m',
	magenta: '\x1b[35m',
	cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
	console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
	log('\n' + '='.repeat(60), 'cyan');
	log(`  ${title}`, 'bright');
	log('='.repeat(60), 'cyan');
}

function logStep(step, description) {
	log(`\n${step}. ${description}`, 'yellow');
}

function logSuccess(message) {
	log(`✅ ${message}`, 'green');
}

function logError(message) {
	log(`❌ ${message}`, 'red');
}

function logInfo(message) {
	log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
	log(`⚠️  ${message}`, 'yellow');
}

// Test URLs and endpoints
const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:5000/api';

const testUrls = {
	// Frontend URLs
	login: `${BASE_URL}/`,
	dashboard: `${BASE_URL}/dashboard`,
	placements: `${BASE_URL}/dashboard/placements`,
	campaigns: `${BASE_URL}/dashboard/campaigns`,
	createCampaign: `${BASE_URL}/dashboard/campaigns/create`,
	admin: `${BASE_URL}/admin`,
	
	// API Endpoints
	healthCheck: `${API_BASE_URL}/health`,
	userCampaigns: `${API_BASE_URL}/user/campaigns`,
	adminCampaigns: `${API_BASE_URL}/admin/campaigns`,
	placements: `${API_BASE_URL}/placements`,
	stripeWebhook: `${BASE_URL}/api/stripe/webhook`,
};

logSection('BTDASH-ADMANAGER CAMPAIGN PAYMENT FLOW TESTING GUIDE');

log('This guide will help you test the complete campaign payment flow implementation.', 'bright');

logSection('1. PRE-TESTING CHECKLIST');

logStep('1.1', 'Environment Variables Check');
logInfo('Verify these environment variables are set:');
console.log(`
Frontend (.env.local):
  ✓ NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  ✓ STRIPE_SECRET_KEY  
  ✓ STRIPE_WEBHOOK_SECRET
  ✓ API_BASE_URL
  ✓ INTERNAL_API_KEY

Backend (.env):
  ✓ PORT=5000
  ✓ DATABASE_URL
  ✓ INTERNAL_API_KEY
  ✓ AUTH0_DOMAIN
  ✓ AUTH0_AUDIENCE
`);

logStep('1.2', 'Database Schema Check');
logInfo('Ensure these tables exist with correct schema:');
console.log(`
  ✓ ad_campaigns (with status, total_budget, targeting columns)
  ✓ billing_transactions (for payment tracking)
  ✓ admin_users (for admin authentication)
  ✓ webhook_logs (for Stripe webhook tracking)
`);

logStep('1.3', 'Stripe Dashboard Configuration');
logInfo('Verify Stripe settings:');
console.log(`
  ✓ Test mode enabled
  ✓ Webhook endpoint configured: [ngrok-url]/api/stripe/webhook
  ✓ Webhook events: payment_intent.succeeded, payment_intent.payment_failed
  ✓ API keys match environment variables
`);

logSection('2. DEVELOPMENT ENVIRONMENT SETUP');

logStep('2.1', 'Start Backend API');
logInfo('In btdashs_api directory:');
console.log(`
  cd btdashs_api
  npm install
  npm run dev
  
  Verify: curl ${testUrls.healthCheck}
`);

logStep('2.2', 'Start Frontend');
logInfo('In btdash-admanager directory:');
console.log(`
  cd btdash-admanager
  npm install  
  npm run dev
  
  Verify: Open ${testUrls.dashboard}
`);

logStep('2.3', 'Setup ngrok for Webhooks');
logInfo('For Stripe webhook testing:');
console.log(`
  npm install -g ngrok
  ngrok http 3000
  
  Copy ngrok URL and update Stripe webhook endpoint
`);

logSection('3. END-TO-END TESTING SCENARIOS');

logStep('3.1', 'PAYMENT SUCCESS FLOW');
logSuccess('Test Case: Complete campaign creation with successful payment');

console.log(`
📋 Steps:
1. Login: ${testUrls.login}
2. Navigate to: ${testUrls.placements}
3. Click "Select Placement" on any placement
4. Complete campaign wizard:
   - Name: "Test Campaign Success"
   - Budget: $100.00
   - Schedule: Today to +30 days
   - Creative: Test ad with placeholder image
5. Submit campaign (should create with "pending" status)

🔍 Verification Points:
- Campaign appears in "Pending" tab
- Status badge shows "Pending Review" (yellow)
- Status card shows timeline and next steps
- No payment button visible yet

📊 Database Check:
SELECT id, name, status, total_budget, created_at 
FROM ad_campaigns 
WHERE name = 'Test Campaign Success';
`);

logStep('3.2', 'ADMIN APPROVAL FLOW');
logSuccess('Test Case: Admin reviews and approves campaign');

console.log(`
📋 Steps:
1. Access admin interface: ${testUrls.admin}
2. Find pending campaign in review queue
3. Click "Review" or campaign name
4. Review campaign details and creative
5. Click "Approve Campaign"
6. Add approval notes (optional)

🔍 Verification Points:
- Campaign status changes to "approved"
- Campaign moves to "Approved" tab in user dashboard
- Status badge shows "Approved" (blue)
- "Pay Now" button appears in status card
- Payment component is visible

📊 Database Check:
SELECT id, name, status, approved_at, approved_by
FROM ad_campaigns 
WHERE name = 'Test Campaign Success';
`);

logStep('3.3', 'PAYMENT PROCESSING FLOW');
logSuccess('Test Case: User completes payment for approved campaign');

console.log(`
📋 Steps:
1. Navigate to campaign details page
2. Click "Pay Now" button in status card
3. OR click "Pay $100.00" in payment component
4. Complete Stripe checkout with test card: 4242 4242 4242 4242
5. Verify webhook processing

🔍 Verification Points:
- Redirected to Stripe Checkout
- Payment processes successfully
- Webhook received and processed
- Campaign status changes to "active"
- Status badge shows "Active" (green)
- Campaign appears in "Active" tab

📊 Database Checks:
-- Campaign status updated
SELECT id, name, status, updated_at 
FROM ad_campaigns 
WHERE name = 'Test Campaign Success';

-- Payment transaction recorded
SELECT id, campaign_id, amount, status, payment_intent_id
FROM billing_transactions 
WHERE campaign_id = [campaign_id];

-- Webhook processed
SELECT id, event_type, status, processed_at
FROM webhook_logs 
WHERE event_type = 'payment_intent.succeeded';
`);

logStep('3.4', 'PAYMENT FAILURE FLOW');
logError('Test Case: Payment fails with declined card');

console.log(`
📋 Steps:
1. Create another test campaign following steps 3.1-3.2
2. Use declined test card: 4000 0000 0000 0002
3. Attempt payment

🔍 Verification Points:
- Payment fails with error message
- Campaign status remains "approved"
- User can retry payment
- Error handling displays properly
- No campaign activation occurs

📊 Database Check:
SELECT id, campaign_id, status, failure_reason
FROM billing_transactions 
WHERE status = 'failed';
`);

logStep('3.5', 'ADMIN REJECTION FLOW');
logError('Test Case: Admin rejects campaign');

console.log(`
📋 Steps:
1. Create campaign with inappropriate content
2. Admin reviews and rejects with reason
3. User sees rejection details

🔍 Verification Points:
- Campaign status changes to "rejected"
- Status badge shows "Rejected" (red)
- Rejection reason displayed in status card
- "Edit Campaign" button available
- Campaign appears in "Rejected" tab

📊 Database Check:
SELECT id, name, status, rejection_reason, admin_notes
FROM ad_campaigns 
WHERE status = 'rejected';
`);

logSection('4. UI COMPONENT VERIFICATION');

logStep('4.1', 'Status Badge Components');
logInfo('Verify status badges display correctly:');
console.log(`
✓ Pending: Yellow badge with clock icon
✓ Approved: Blue badge with checkmark icon  
✓ Rejected: Red badge with X icon
✓ Active: Green badge with play icon
✓ Payment Required: Orange badge with credit card icon
`);

logStep('4.2', 'Campaign Status Card');
logInfo('Verify status card shows:');
console.log(`
✓ Current status with description
✓ Timeline of events (created, approved/rejected)
✓ Next steps list
✓ Rejection details (if rejected)
✓ Payment section (if approved)
✓ Last updated timestamp
`);

logStep('4.3', 'Payment Component');
logInfo('For approved campaigns, verify:');
console.log(`
✓ Campaign summary (name, placement, duration, budget)
✓ Secure payment button with Stripe branding
✓ Payment processing states (loading, success, error)
✓ Clear next steps information
✓ Error handling and retry functionality
`);

logSection('5. MONITORING AND DEBUGGING');

logStep('5.1', 'Real-time Monitoring');
logInfo('Monitor logs during testing:');
console.log(`
Backend logs:
  cd btdashs_api && npm run dev | grep -E "(payment|stripe|webhook)"

Frontend logs:
  cd btdash-admanager && npm run dev | grep -E "(payment|stripe)"

Stripe Dashboard:
  Monitor webhook deliveries and payment events
`);

logStep('5.2', 'Common Issues');
logWarning('Troubleshooting guide:');
console.log(`
❌ Webhooks not received:
   → Check ngrok URL in Stripe Dashboard
   → Verify webhook endpoint is accessible

❌ Payment intent creation fails:
   → Verify Stripe keys are correct
   → Check browser console for errors

❌ Admin access denied:
   → Verify user exists in admin_users table
   → Check Auth0 user ID mapping

❌ Database connection errors:
   → Verify DATABASE_URL is accessible
   → Check API server logs for connection issues
`);

logSection('6. SUCCESS CRITERIA');

logSuccess('Testing is complete when:');
console.log(`
✅ Campaign creation flow works end-to-end
✅ Admin approval/rejection workflow functions
✅ Payment processing completes successfully  
✅ Status badges display correctly throughout
✅ Error handling works for failed payments
✅ Database state is consistent at each step
✅ Webhooks are received and processed
✅ UI provides clear feedback at each stage
`);

logSection('READY TO START TESTING!');

log('Follow the steps above systematically to test the complete campaign payment flow.', 'bright');
log('Report any issues found during testing for immediate resolution.', 'yellow');

console.log('\n');
