import { auth0 } from "@/lib/auth0";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		// Get query parameters
		const { searchParams } = new URL(request.url);
		const status = searchParams.get("status");
		const limit = searchParams.get("limit") || "50";
		const offset = searchParams.get("offset") || "0";

		// Build query string
		const queryParams = new URLSearchParams();
		if (status) queryParams.append("status", status);
		queryParams.append("limit", limit);
		queryParams.append("offset", offset);

		// Call the backend API to get user invoices
		const response = await fetch(`${process.env.API_BASE_URL}/invoices?${queryParams.toString()}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error) {
		console.error("Error fetching user invoices:", error);
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch invoices",
			},
			{ status: 500 }
		);
	}
}
