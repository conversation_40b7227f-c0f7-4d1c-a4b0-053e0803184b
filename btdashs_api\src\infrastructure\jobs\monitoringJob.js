const cron = require("node-cron");
const PaymentMonitoringService = require("../monitoring/PaymentMonitoringService");
const logger = require("../logging/logger");

/**
 * Scheduled job for system monitoring and alerting
 */
class MonitoringJob {
	constructor() {
		this.paymentMonitoring = new PaymentMonitoringService();
		this.isRunning = false;
	}

	/**
	 * Start the monitoring jobs
	 */
	start() {
		// Run payment monitoring every 15 minutes
		cron.schedule("*/15 * * * *", async () => {
			if (this.isRunning) {
				logger.warn("Monitoring job already running, skipping");
				return;
			}

			this.isRunning = true;
			try {
				logger.info("Starting payment monitoring check");
				const result = await this.paymentMonitoring.monitorPaymentFailures();
				
				logger.info("Payment monitoring completed", {
					alertsTriggered: result.alertsTriggered,
					hourlyFailureRate: result.hourlyStats.failureRate,
					systemHealthy: result.systemHealth.healthy
				});

				// Store monitoring results for dashboard
				await this.storeMonitoringResults(result);

			} catch (error) {
				logger.error("Payment monitoring job failed", { error: error.message });
				
				// Send critical alert about monitoring failure
				await this.sendCriticalAlert({
					type: "monitoring_system_failure",
					message: "Payment monitoring system has failed",
					error: error.message
				});
			} finally {
				this.isRunning = false;
			}
		});

		// Run system health check every 5 minutes
		cron.schedule("*/5 * * * *", async () => {
			try {
				const systemHealth = await this.paymentMonitoring.checkSystemHealth();
				
				if (!systemHealth.healthy) {
					logger.warn("System health check failed", systemHealth);
					
					await this.sendCriticalAlert({
						type: "system_health_failure",
						message: "System health check failed",
						data: systemHealth
					});
				}

				// Store health metrics
				await this.storeHealthMetrics(systemHealth);

			} catch (error) {
				logger.error("System health check failed", { error: error.message });
			}
		});

		// Run daily monitoring summary at 9 AM
		cron.schedule("0 9 * * *", async () => {
			try {
				logger.info("Generating daily monitoring summary");
				const summary = await this.generateDailySummary();
				
				await this.sendDailySummary(summary);

			} catch (error) {
				logger.error("Daily summary generation failed", { error: error.message });
			}
		});

		// Cleanup old monitoring data weekly
		cron.schedule("0 2 * * 0", async () => {
			try {
				logger.info("Cleaning up old monitoring data");
				await this.cleanupOldData();
				
			} catch (error) {
				logger.error("Monitoring data cleanup failed", { error: error.message });
			}
		});

		logger.info("Monitoring jobs started");
	}

	/**
	 * Store monitoring results for dashboard
	 * @param {Object} results - Monitoring results
	 */
	async storeMonitoringResults(results) {
		try {
			const db = require("../database/knex");
			
			// Check if monitoring_results table exists
			const tableExists = await db.schema.hasTable("dtm_ads.monitoring_results");
			
			if (tableExists) {
				await db("dtm_ads.monitoring_results").insert({
					timestamp: new Date(),
					hourly_stats: JSON.stringify(results.hourlyStats),
					daily_stats: JSON.stringify(results.dailyStats),
					system_health: JSON.stringify(results.systemHealth),
					alerts_triggered: results.alertsTriggered,
					created_at: new Date()
				});

				// Keep only last 7 days of results
				const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
				await db("dtm_ads.monitoring_results")
					.where("created_at", "<", sevenDaysAgo)
					.del();
			}

		} catch (error) {
			logger.error("Error storing monitoring results", { error: error.message });
		}
	}

	/**
	 * Store health metrics for trending
	 * @param {Object} healthMetrics - Health metrics
	 */
	async storeHealthMetrics(healthMetrics) {
		try {
			const db = require("../database/knex");
			
			// Check if health_metrics table exists
			const tableExists = await db.schema.hasTable("dtm_ads.health_metrics");
			
			if (tableExists) {
				await db("dtm_ads.health_metrics").insert({
					timestamp: new Date(),
					healthy: healthMetrics.healthy,
					error_rate: healthMetrics.errorRate,
					database_response_time: healthMetrics.database?.responseTime || null,
					api_response_time: healthMetrics.api?.averageResponseTime || null,
					ad_serving_fallback_rate: healthMetrics.adServing?.fallbackRate || null,
					created_at: new Date()
				});

				// Keep only last 24 hours of health metrics
				const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
				await db("dtm_ads.health_metrics")
					.where("created_at", "<", oneDayAgo)
					.del();
			}

		} catch (error) {
			logger.error("Error storing health metrics", { error: error.message });
		}
	}

	/**
	 * Generate daily monitoring summary
	 * @returns {Promise<Object>} Daily summary
	 */
	async generateDailySummary() {
		try {
			const now = new Date();
			const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

			// Get payment stats for the last 24 hours
			const dailyStats = await this.paymentMonitoring.getPaymentStats(yesterday, now);

			// Get alerts from the last 24 hours
			const db = require("../database/knex");
			const alerts = await db("dtm_ads.system_alerts")
				.where("created_at", ">=", yesterday)
				.select("*");

			// Get system health trends
			const healthMetrics = await db("dtm_ads.health_metrics")
				.where("created_at", ">=", yesterday)
				.select("*");

			const avgErrorRate = healthMetrics.length > 0 
				? healthMetrics.reduce((sum, m) => sum + (m.error_rate || 0), 0) / healthMetrics.length
				: 0;

			const uptimePercentage = healthMetrics.length > 0
				? (healthMetrics.filter(m => m.healthy).length / healthMetrics.length) * 100
				: 100;

			return {
				date: yesterday.toISOString().split('T')[0],
				paymentStats: dailyStats,
				alerts: {
					total: alerts.length,
					critical: alerts.filter(a => a.severity === 'critical').length,
					high: alerts.filter(a => a.severity === 'high').length,
					byType: alerts.reduce((acc, alert) => {
						acc[alert.type] = (acc[alert.type] || 0) + 1;
						return acc;
					}, {})
				},
				systemHealth: {
					uptimePercentage: uptimePercentage.toFixed(2),
					averageErrorRate: (avgErrorRate * 100).toFixed(2),
					healthChecks: healthMetrics.length
				},
				recommendations: this.generateRecommendations(dailyStats, alerts, avgErrorRate)
			};

		} catch (error) {
			logger.error("Error generating daily summary", { error: error.message });
			throw error;
		}
	}

	/**
	 * Generate recommendations based on monitoring data
	 * @param {Object} paymentStats - Payment statistics
	 * @param {Array} alerts - Alerts from the period
	 * @param {number} errorRate - Average error rate
	 * @returns {Array} Recommendations
	 */
	generateRecommendations(paymentStats, alerts, errorRate) {
		const recommendations = [];

		if (paymentStats.failureRate > 0.1) {
			recommendations.push("High payment failure rate detected. Review payment gateway configuration and error patterns.");
		}

		if (alerts.length > 10) {
			recommendations.push("High number of alerts triggered. Consider adjusting alert thresholds or investigating underlying issues.");
		}

		if (errorRate > 0.05) {
			recommendations.push("System error rate is elevated. Review application logs and consider scaling resources.");
		}

		const criticalAlerts = alerts.filter(a => a.severity === 'critical');
		if (criticalAlerts.length > 0) {
			recommendations.push(`${criticalAlerts.length} critical alerts require immediate attention.`);
		}

		if (recommendations.length === 0) {
			recommendations.push("System is operating within normal parameters.");
		}

		return recommendations;
	}

	/**
	 * Send daily summary to stakeholders
	 * @param {Object} summary - Daily summary
	 */
	async sendDailySummary(summary) {
		try {
			logger.info("Daily monitoring summary", summary);

			// Send to webhook if configured
			if (process.env.DAILY_SUMMARY_WEBHOOK_URL) {
				await fetch(process.env.DAILY_SUMMARY_WEBHOOK_URL, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"Authorization": `Bearer ${process.env.DAILY_SUMMARY_WEBHOOK_TOKEN}`
					},
					body: JSON.stringify({
						source: "btdash-daily-monitoring",
						summary
					})
				});
			}

			// Send Slack summary if configured
			if (process.env.SLACK_WEBHOOK_URL) {
				await this.sendSlackSummary(summary);
			}

		} catch (error) {
			logger.error("Error sending daily summary", { error: error.message });
		}
	}

	/**
	 * Send Slack daily summary
	 * @param {Object} summary - Daily summary
	 */
	async sendSlackSummary(summary) {
		try {
			const color = summary.alerts.critical > 0 ? "danger" : 
						 summary.alerts.high > 0 ? "warning" : "good";

			const payload = {
				text: `📊 BTDash Daily Monitoring Summary - ${summary.date}`,
				attachments: [{
					color,
					fields: [
						{ title: "Payment Success Rate", value: `${(summary.paymentStats.successRate * 100).toFixed(1)}%`, short: true },
						{ title: "System Uptime", value: `${summary.systemHealth.uptimePercentage}%`, short: true },
						{ title: "Total Alerts", value: summary.alerts.total.toString(), short: true },
						{ title: "Critical Alerts", value: summary.alerts.critical.toString(), short: true }
					],
					text: summary.recommendations.join("\n")
				}]
			};

			await fetch(process.env.SLACK_WEBHOOK_URL, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(payload)
			});

		} catch (error) {
			logger.error("Error sending Slack summary", { error: error.message });
		}
	}

	/**
	 * Send critical alert
	 * @param {Object} alertData - Alert data
	 */
	async sendCriticalAlert(alertData) {
		try {
			const alert = {
				...alertData,
				severity: "critical",
				timestamp: new Date().toISOString()
			};

			await this.paymentMonitoring.sendAlert(alert);

		} catch (error) {
			logger.error("Error sending critical alert", { error: error.message });
		}
	}

	/**
	 * Cleanup old monitoring data
	 */
	async cleanupOldData() {
		try {
			const db = require("../database/knex");
			const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

			// Cleanup old alerts
			const alertsDeleted = await db("dtm_ads.system_alerts")
				.where("created_at", "<", thirtyDaysAgo)
				.del();

			// Cleanup old payment logs (keep 90 days)
			const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
			const logsDeleted = await db("dtm_ads.payment_logs")
				.where("created_at", "<", ninetyDaysAgo)
				.del();

			logger.info("Monitoring data cleanup completed", {
				alertsDeleted,
				logsDeleted
			});

		} catch (error) {
			logger.error("Error cleaning up monitoring data", { error: error.message });
		}
	}

	/**
	 * Stop the monitoring jobs
	 */
	stop() {
		logger.info("Monitoring jobs stopped");
	}
}

module.exports = MonitoringJob;
