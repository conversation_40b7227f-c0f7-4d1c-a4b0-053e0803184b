const CampaignExpirationService = require("../../application/services/CampaignExpirationService");
const { sendSuccess, sendError, sendUnauthorized, sendForbidden } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");
const { isAdminUser } = require("../../application/services/AdminService");

const campaignExpirationService = new CampaignExpirationService();

/**
 * Manually trigger campaign expiration check
 * Admin only endpoint
 */
const processExpiredCampaigns = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const result = await campaignExpirationService.processExpiredCampaigns();
		return sendSuccess(res, result, "Campaign expiration check completed");
	} catch (error) {
		return sendError(res, "Failed to process expired campaigns", error.message, 500);
	}
});

/**
 * Manually trigger campaign activation check
 * Admin only endpoint
 */
const processStartingCampaigns = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const result = await campaignExpirationService.processStartingCampaigns();
		return sendSuccess(res, result, "Campaign activation check completed");
	} catch (error) {
		return sendError(res, "Failed to process starting campaigns", error.message, 500);
	}
});

/**
 * Get campaigns expiring soon
 * Admin only endpoint
 */
const getCampaignsExpiringSoon = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const { days = 7 } = req.query;
		const daysNumber = parseInt(days, 10);

		if (isNaN(daysNumber) || daysNumber < 1 || daysNumber > 365) {
			return sendError(res, "Invalid days parameter", "Days must be between 1 and 365", 400);
		}

		const campaigns = await campaignExpirationService.getCampaignsExpiringSoon(daysNumber);
		return sendSuccess(
			res,
			{
				campaigns,
				count: campaigns.length,
				days: daysNumber,
			},
			`Found ${campaigns.length} campaigns expiring within ${daysNumber} days`
		);
	} catch (error) {
		return sendError(res, "Failed to get campaigns expiring soon", error.message, 500);
	}
});

/**
 * Validate campaign dates
 * Available to authenticated users
 */
const validateCampaignDates = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	try {
		const { start_date, end_date } = req.body;

		if (!start_date || !end_date) {
			return sendError(res, "Both start_date and end_date are required", null, 400);
		}

		const validation = campaignExpirationService.validateCampaignDates(start_date, end_date);

		if (validation.valid) {
			return sendSuccess(
				res,
				{
					valid: true,
					duration: validation.duration,
					durationDays: validation.durationDays,
				},
				"Campaign dates are valid"
			);
		} else {
			return sendError(res, "Invalid campaign dates", validation.errors, 400);
		}
	} catch (error) {
		return sendError(res, "Failed to validate campaign dates", error.message, 500);
	}
});

/**
 * Get campaign expiration status
 * Available to campaign owners and admins
 */
const getCampaignExpirationStatus = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	try {
		const { campaignId } = req.params;

		if (!campaignId) {
			return sendError(res, "Campaign ID is required", null, 400);
		}

		const db = require("../../infrastructure/database/knex");

		// Get campaign details
		const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId }).first();

		if (!campaign) {
			return sendError(res, "Campaign not found", null, 404);
		}

		// Check if user has access to this campaign
		const admin = await isAdminUser(auth0_id);
		const AdsService = require("../../application/services/AdsService");
		const user = await AdsService.getUserByAuth0Id(auth0_id);

		if (!admin && campaign.advertiser_id !== user?.id && campaign.manager_id !== user?.id) {
			return sendForbidden(res, "Access denied");
		}

		// Calculate expiration status
		const now = new Date();
		const startDate = new Date(campaign.start_date);
		const endDate = new Date(campaign.end_date);

		const status = {
			campaign_id: campaign.id,
			campaign_name: campaign.name,
			current_status: campaign.status,
			start_date: campaign.start_date,
			end_date: campaign.end_date,
			is_expired: endDate < now,
			is_active: startDate <= now && endDate > now,
			is_future: startDate > now,
			days_until_start: startDate > now ? Math.ceil((startDate - now) / (24 * 60 * 60 * 1000)) : 0,
			days_until_expiry: endDate > now ? Math.ceil((endDate - now) / (24 * 60 * 60 * 1000)) : 0,
			total_duration_days: Math.ceil((endDate - startDate) / (24 * 60 * 60 * 1000)),
			validation: campaignExpirationService.validateCampaignDates(campaign.start_date, campaign.end_date),
		};

		return sendSuccess(res, status, "Campaign expiration status retrieved");
	} catch (error) {
		return sendError(res, "Failed to get campaign expiration status", error.message, 500);
	}
});

/**
 * Extend campaign end date
 * Available to campaign owners and admins
 */
const extendCampaign = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	try {
		const { campaignId } = req.params;
		const { new_end_date } = req.body;

		if (!campaignId || !new_end_date) {
			return sendError(res, "Campaign ID and new_end_date are required", null, 400);
		}

		const db = require("../../infrastructure/database/knex");

		// Get campaign details
		const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId }).first();

		if (!campaign) {
			return sendError(res, "Campaign not found", null, 404);
		}

		// Check if user has access to this campaign
		const admin = await isAdminUser(auth0_id);
		const AdsService = require("../../application/services/AdsService");
		const user = await AdsService.getUserByAuth0Id(auth0_id);

		if (!admin && campaign.advertiser_id !== user?.id && campaign.manager_id !== user?.id) {
			return sendForbidden(res, "Access denied");
		}

		// Validate new end date
		const validation = campaignExpirationService.validateCampaignDates(campaign.start_date, new_end_date);

		if (!validation.valid) {
			return sendError(res, "Invalid new end date", validation.errors, 400);
		}

		// Check if campaign can be extended (not completed or rejected)
		if (["completed", "rejected"].includes(campaign.status)) {
			return sendError(res, "Cannot extend completed or rejected campaigns", null, 400);
		}

		// Update campaign end date
		const [updatedCampaign] = await db("dtm_ads.ad_campaigns")
			.where({ id: campaignId })
			.update({
				end_date: new Date(new_end_date),
				updated_at: new Date(),
			})
			.returning("*");

		return sendSuccess(
			res,
			{
				campaign: updatedCampaign,
				previous_end_date: campaign.end_date,
				new_end_date: new_end_date,
				extension_days: Math.ceil(
					(new Date(new_end_date) - new Date(campaign.end_date)) / (24 * 60 * 60 * 1000)
				),
			},
			"Campaign end date extended successfully"
		);
	} catch (error) {
		return sendError(res, "Failed to extend campaign", error.message, 500);
	}
});

module.exports = {
	processExpiredCampaigns,
	processStartingCampaigns,
	getCampaignsExpiringSoon,
	validateCampaignDates,
	getCampaignExpirationStatus,
	extendCampaign,
};
