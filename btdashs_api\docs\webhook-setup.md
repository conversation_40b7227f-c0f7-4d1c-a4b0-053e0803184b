# Webhook Configuration Guide

This guide explains how to set up and configure webhook endpoints for the BTDash payment system in both local development and production environments.

## Quick Start

### Local Development with ngrok

1. **Install ngrok** (if not already installed):
   ```bash
   # Download from https://ngrok.com/download
   # Or install via package manager
   npm install -g ngrok
   ```

2. **Start your API server**:
   ```bash
   npm run dev
   ```

3. **Start ngrok in another terminal**:
   ```bash
   ngrok http 3001
   ```

4. **Copy the HTTPS URL** from ngrok output (e.g., `https://abc123.ngrok.io`)

5. **Setup webhooks with ngrok URL**:
   ```bash
   npm run webhooks:setup -- --ngrok https://abc123.ngrok.io
   ```

### Production Setup

1. **Configure environment variables**:
   ```bash
   WEBHOOK_BASE_URL=https://yourdomain.com
   STRIPE_SECRET_KEY=sk_live_your_key
   STRIPE_WEBHOOK_SECRET=whsec_your_secret
   NODE_ENV=production
   ```

2. **Setup webhooks**:
   ```bash
   npm run webhooks:setup -- --env production
   ```

## Environment Configuration

### Required Environment Variables

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_or_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_from_stripe_dashboard

# Webhook Base URL
WEBHOOK_BASE_URL=https://yourdomain.com  # Production
# OR
NGROK_URL=https://abc123.ngrok.io        # Development with ngrok

# Security
INTERNAL_API_KEY=your_secure_internal_api_key

# Environment
NODE_ENV=development|staging|production
```

### Optional Configuration

```bash
# Webhook Retry Settings
WEBHOOK_MAX_RETRIES=5
WEBHOOK_BASE_DELAY=1000
WEBHOOK_MAX_DELAY=30000
```

## Webhook Endpoints

The system automatically generates these webhook endpoints:

| Endpoint | Purpose | Authentication |
|----------|---------|----------------|
| `/api/billing/webhooks/stripe` | Stripe payment webhooks | Stripe signature |
| `/api/billing/webhooks/health` | Health check | None |
| `/api/billing/webhooks/internal` | Internal webhooks | Internal API key |

## Setup Commands

### Basic Setup
```bash
# Setup webhooks for current environment
npm run webhooks:setup

# Setup with specific environment
npm run webhooks:setup -- --env production

# Setup with ngrok URL
npm run webhooks:setup -- --ngrok https://abc123.ngrok.io
```

### Testing
```bash
# Test webhook endpoints
npm run webhooks:test

# Test with specific URL
npm run webhooks:test -- --ngrok https://abc123.ngrok.io
```

### Management
```bash
# List existing webhook endpoints
npm run webhooks:list

# Delete a webhook endpoint
npm run webhooks:setup -- --delete we_1234567890
```

## Stripe Webhook Events

The system automatically subscribes to these Stripe events:

- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `payment_intent.processing`
- `payment_intent.requires_action`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`

## API Endpoints

### Webhook Management

```http
GET /api/billing/webhooks/status
Authorization: Bearer <jwt_token>
```
Get webhook configuration and status.

```http
POST /api/billing/webhooks/stripe/setup
Authorization: Internal-Key <internal_api_key>
Content-Type: application/json

{
  "url": "https://yourdomain.com/api/billing/webhooks/stripe",
  "events": ["payment_intent.succeeded", "payment_intent.payment_failed"],
  "description": "BTDash Payment Webhooks",
  "enabled": true
}
```
Setup or update Stripe webhook endpoint.

```http
GET /api/billing/webhooks/stripe/list
Authorization: Internal-Key <internal_api_key>
```
List all Stripe webhook endpoints.

```http
DELETE /api/billing/webhooks/stripe/{endpoint_id}
Authorization: Internal-Key <internal_api_key>
```
Delete a Stripe webhook endpoint.

### Testing

```http
POST /api/billing/webhooks/test
Authorization: Internal-Key <internal_api_key>
Content-Type: application/json

{
  "url": "https://yourdomain.com/api/billing/webhooks/stripe"
}
```
Test webhook endpoint connectivity.

```http
GET /api/billing/webhooks/health
```
Health check endpoint (no authentication required).

## Development Workflow

### 1. Local Development Setup

```bash
# Terminal 1: Start API server
npm run dev

# Terminal 2: Start ngrok
ngrok http 3001

# Terminal 3: Setup webhooks
npm run webhooks:setup -- --ngrok https://abc123.ngrok.io

# Test webhooks
npm run webhooks:test
```

### 2. Testing with Stripe CLI

```bash
# Install Stripe CLI
# https://stripe.com/docs/stripe-cli

# Login to Stripe
stripe login

# Forward events to local webhook
stripe listen --forward-to localhost:3001/api/billing/webhooks/stripe

# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed
```

### 3. Production Deployment

```bash
# Set production environment variables
export NODE_ENV=production
export WEBHOOK_BASE_URL=https://yourdomain.com
export STRIPE_SECRET_KEY=sk_live_your_key
export STRIPE_WEBHOOK_SECRET=whsec_your_secret

# Setup production webhooks
npm run webhooks:setup -- --env production

# Verify webhook status
npm run webhooks:test
```

## Troubleshooting

### Common Issues

1. **Webhook endpoint not reachable**
   - Ensure ngrok is running and URL is correct
   - Check firewall settings for production
   - Verify SSL certificate for HTTPS endpoints

2. **Stripe webhook signature verification fails**
   - Ensure `STRIPE_WEBHOOK_SECRET` is correctly set
   - Check that the webhook secret matches Stripe dashboard
   - Verify raw body is being passed to verification

3. **Webhook events not being processed**
   - Check webhook logs in application
   - Verify event types are subscribed in Stripe
   - Check webhook retry logic and failure handling

### Debug Commands

```bash
# Get detailed webhook status
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:3001/api/billing/webhooks/status

# Test webhook endpoint manually
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Internal-Key: <internal_api_key>" \
     -d '{"url": "https://abc123.ngrok.io/api/billing/webhooks/stripe"}' \
     http://localhost:3001/api/billing/webhooks/test

# Check webhook health
curl http://localhost:3001/api/billing/webhooks/health
```

### Monitoring

Monitor webhook health using:

- Application logs for webhook processing
- Stripe dashboard for webhook delivery status
- `/api/billing/webhooks/status` endpoint for system status
- `/api/billing/webhook-stats` for retry statistics

## Security Considerations

### Development
- Use test Stripe keys only
- ngrok URLs are temporary and public
- Don't commit webhook secrets to version control

### Production
- Use HTTPS endpoints only
- Implement proper SSL certificate validation
- Verify webhook signatures from Stripe
- Use environment-specific webhook secrets
- Monitor webhook endpoints for availability
- Set up alerting for webhook failures
- Implement rate limiting for webhook endpoints

## Best Practices

1. **Always verify webhook signatures** in production
2. **Use HTTPS endpoints** for all webhook URLs
3. **Implement idempotency** for webhook processing
4. **Log all webhook events** for debugging
5. **Set up monitoring** for webhook health
6. **Use retry logic** with exponential backoff
7. **Handle webhook failures gracefully**
8. **Test webhook endpoints** before deployment
