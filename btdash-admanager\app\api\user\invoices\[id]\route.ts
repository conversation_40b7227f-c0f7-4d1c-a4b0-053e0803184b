import { auth0 } from "@/lib/auth0";
import { NextRequest, NextResponse } from "next/server";

interface RouteParams {
	params: { id: string };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
	try {
		const paramsData = await params;
		const invoiceId = paramsData.id;

		if (!invoiceId) {
			return NextResponse.json({ success: false, message: "Invoice ID is required" }, { status: 400 });
		}

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		// Call the backend API to get invoice details
		const response = await fetch(`${process.env.API_BASE_URL}/invoices/${invoiceId}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error) {
		console.error("Error fetching invoice details:", error);
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch invoice details",
			},
			{ status: 500 }
		);
	}
}
