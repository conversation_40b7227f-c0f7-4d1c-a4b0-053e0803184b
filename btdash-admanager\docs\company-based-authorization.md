# Company-Based Authorization System

## Overview

The BTDash Ad Manager now implements a comprehensive company-based authorization system that allows users to access invoices and campaigns for any companies they are associated with, not just campaigns they personally manage.

## ✅ **New Authorization Logic**

### **Previous System (Removed)**
- Users could only access invoices for campaigns they personally managed (`manager_id = userId`)
- Limited collaboration within companies
- Backward compatibility constraints

### **New System (Implemented)**
- Users can access invoices for ANY campaigns belonging to companies they are associated with
- Multi-user company collaboration supported
- Clean architecture with no backward compatibility

## 🏢 **Company Association Model**

### **Database Relationships**
```
dtm_base.user_company table:
├── user_id → dtm_base.users.id
├── company_id → dtm_base.companies.id
├── role → 'owner', 'admin', 'member'
└── created_at, updated_at

Campaign Structure:
├── manager_id → dtm_base.users.id (who manages)
├── advertiser_id → dtm_base.companies.id (who gets advertised)

Invoice Structure:
├── manager_id → dtm_base.users.id (who pays)
├── advertiser_id → dtm_base.companies.id (who gets advertised)
```

### **Access Scenarios**

**Scenario 1: Direct Manager Access**
- User A creates Campaign X for Company Y
- User A can access invoices for Campaign X (direct manager)

**Scenario 2: Company Member Access**
- User A creates Campaign X for Company Y
- User B is associated with Company Y
- User B can access invoices for Campaign X (company association)

**Scenario 3: Multi-Campaign Access**
- Company Y has multiple campaigns managed by different users
- Any user associated with Company Y can access ALL invoices for Company Y campaigns

## 🔧 **Implementation Details**

### **CompanyAuthService**

The new `CompanyAuthService` provides centralized authorization logic:

```javascript
// Check if user has access to a company
await companyAuthService.hasCompanyAccess(userId, companyId, ['owner', 'admin', 'member']);

// Check if user can access specific invoice
await companyAuthService.canAccessInvoice(userId, invoiceId);

// Get all invoices user can access
await companyAuthService.getUserAccessibleInvoices(userId, options);

// Check campaign access
await companyAuthService.canAccessCampaign(userId, campaignId);
```

### **Authorization Flow**

```javascript
// Invoice access check:
1. Is user the campaign manager? → Allow
2. Is user associated with the advertiser company? → Allow
3. Otherwise → Deny

// Company access check:
1. Query user_company table for user_id + company_id
2. Check if role is in allowed roles
3. Return access decision
```

## 📋 **API Changes**

### **Updated Endpoints**

All invoice endpoints now use company-based authorization:

```
GET /api/invoices                    # Returns invoices for all user's companies
GET /api/invoices/:id                # Checks company association
GET /api/invoices/:id/pdf            # Checks company association  
GET /api/invoices/:id/download       # Checks company association
```

### **Authorization Headers**

All endpoints require JWT authentication:
```
Authorization: Bearer <jwt_token>
```

The user ID is extracted from the JWT and used for company association checks.

## 🗄️ **Database Schema Changes**

### **Removed (No Backward Compatibility)**
- `billing_transactions.advertiser_id` (confusing naming)
- `billing_transactions.invoice_id` (old reference)
- `billing_transactions.payment_intent_reference` (renamed)
- `billing_transactions.invoice_reference_id` (deprecated)

### **Added (Clean Schema)**
- `billing_transactions.user_id` → `dtm_base.users.id`
- `billing_transactions.payment_intent_id` (clean naming)
- `billing_transactions.invoice_id` → `dtm_ads.invoices.id`
- `invoices.manager_id` → `dtm_base.users.id`
- `invoices.advertiser_id` → `dtm_base.companies.id`

### **Migration Required**
```bash
# Run clean migration (removes test data)
psql -d your_database -f btdashs_api/database_clean_invoice_migration.sql
```

## 🎯 **User Experience**

### **Multi-User Company Workflow**

1. **Company Setup**
   - Company owner invites team members
   - Members get associated with company in `user_company` table

2. **Campaign Creation**
   - Any company member can create campaigns for the company
   - Campaign manager pays for the campaign
   - All company members can view campaign invoices

3. **Invoice Management**
   - All company members can download invoices
   - All company members can view payment history
   - Billing is still tied to the campaign manager

### **Role-Based Access**

```javascript
// Owner: Full access to company data
role: 'owner' → Can manage company, view all invoices, create campaigns

// Admin: Management access
role: 'admin' → Can view all invoices, create campaigns

// Member: View access
role: 'member' → Can view invoices, create campaigns
```

## 🔍 **Testing**

### **Automated Tests**

Run the authorization test suite:
```bash
cd btdashs_api
node test_company_authorization.js
```

### **Test Scenarios**
- ✅ Company owner can access all company invoices
- ✅ Company member can access all company invoices  
- ✅ Campaign manager has direct access to their invoices
- ✅ Non-company users cannot access company invoices
- ✅ Invoice retrieval returns correct invoices for each user

### **Manual Testing**

1. Create two users and associate them with the same company
2. User A creates a campaign for the company
3. Verify User B can access the invoice for User A's campaign
4. Verify both users see the same invoices in the billing page

## 🚨 **Security Considerations**

### **Access Control**
- All authorization checks happen server-side
- JWT tokens validated on every request
- Company associations verified against database
- No client-side authorization logic

### **Data Isolation**
- Users can only access data for companies they're associated with
- Cross-company data access is prevented
- Invoice access requires valid company association

### **Audit Trail**
- All authorization decisions are logged
- Company association changes are tracked
- Invoice access is monitored

## 📊 **Performance Optimizations**

### **Database Indexes**
```sql
-- Optimized for company-based queries
CREATE INDEX idx_invoices_advertiser_id ON dtm_ads.invoices(advertiser_id);
CREATE INDEX idx_user_company_user_id ON dtm_base.user_company(user_id);
CREATE INDEX idx_user_company_company_id ON dtm_base.user_company(company_id);
```

### **Query Optimization**
- Company associations cached per request
- Bulk invoice queries use JOIN operations
- Efficient filtering by company membership

## 🔄 **Migration Guide**

### **Breaking Changes**
- ❌ Old invoice authorization logic removed
- ❌ Backward compatibility code removed
- ❌ Deprecated database columns removed
- ❌ Test data will be cleared

### **Required Actions**
1. **Run database migration** to clean schema
2. **Update any custom code** that relied on old authorization
3. **Test company associations** are properly configured
4. **Verify invoice access** works for all company members

### **Post-Migration Verification**
- [ ] All company members can access company invoices
- [ ] Campaign managers retain direct access
- [ ] Non-company users cannot access company data
- [ ] PDF generation works for all authorized users
- [ ] Billing page shows correct invoices for each user

## ✅ **Benefits Achieved**

- **Enhanced Collaboration**: Multiple users can manage company campaigns
- **Simplified Access**: Company-based authorization is intuitive
- **Clean Architecture**: No backward compatibility constraints
- **Better Security**: Proper role-based access control
- **Scalability**: Supports large companies with many users
- **Maintainability**: Clean, well-documented authorization logic

This new system provides a solid foundation for multi-user company management while maintaining security and performance.
