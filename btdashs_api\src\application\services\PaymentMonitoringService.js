const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const PaymentAnalyticsService = require("./PaymentAnalyticsService");
const AlertNotificationService = require("./AlertNotificationService");

class PaymentMonitoringService {
	constructor() {
		this.notificationService = new AlertNotificationService();
		this.alertThresholds = {
			successRate: {
				warning: 95,
				critical: 90,
			},
			failureRate: {
				warning: 5,
				critical: 10,
			},
			webhookSuccessRate: {
				warning: 98,
				critical: 95,
			},
			failedAmount: {
				warning: 1000,
				critical: 5000,
			},
			responseTime: {
				warning: 5000,
				critical: 10000,
			},
		};

		this.alertCooldown = 15 * 60 * 1000; // 15 minutes
		this.lastAlerts = new Map();
	}

	/**
	 * Monitor payment system health and trigger alerts
	 * @param {Object} options - Monitoring options
	 * @returns {Promise<Object>} Monitoring results
	 */
	async monitorPaymentHealth(options = {}) {
		const { timeWindow = "1h", includeWebhooks = true } = options;

		try {
			const startTime = new Date(Date.now() - this.parseTimeWindow(timeWindow));
			const endTime = new Date();

			const [paymentMetrics, webhookMetrics, systemHealth] = await Promise.all([
				this.getPaymentMetrics(startTime, endTime),
				includeWebhooks ? this.getWebhookMetrics(startTime, endTime) : null,
				this.getSystemHealth(),
			]);

			const alerts = await this.evaluateAlerts({
				payment: paymentMetrics,
				webhook: webhookMetrics,
				system: systemHealth,
				timeWindow,
			});

			const monitoringResult = {
				timestamp: new Date().toISOString(),
				timeWindow,
				metrics: {
					payment: paymentMetrics,
					webhook: webhookMetrics,
					system: systemHealth,
				},
				alerts,
				status: this.determineOverallStatus(alerts),
			};

			// Process alerts
			if (alerts.length > 0) {
				await this.processAlerts(alerts, monitoringResult);
			}

			// Log monitoring results
			logger.info("Payment monitoring completed", {
				status: monitoringResult.status,
				alertCount: alerts.length,
				timeWindow,
			});

			return monitoringResult;
		} catch (error) {
			logger.error("Payment monitoring failed", {
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Get payment metrics for monitoring
	 * @param {Date} startTime - Start time
	 * @param {Date} endTime - End time
	 * @returns {Promise<Object>} Payment metrics
	 */
	async getPaymentMetrics(startTime, endTime) {
		const [successMetrics, failureMetrics, recentFailures] = await Promise.all([
			// Successful payments
			db("dtm_ads.billing_transactions")
				.where({ status: "completed" })
				.whereBetween("created_at", [startTime, endTime])
				.select(
					db.raw("COUNT(*) as count"),
					db.raw("SUM(amount) as total_amount"),
					db.raw("AVG(amount) as avg_amount")
				)
				.first(),

			// Failed payments
			db("dtm_ads.payment_failures")
				.whereBetween("created_at", [startTime, endTime])
				.select(
					db.raw("COUNT(*) as count"),
					db.raw("SUM(amount) as total_amount"),
					db.raw("COUNT(DISTINCT failure_code) as unique_failure_codes")
				)
				.first(),

			// Recent critical failures
			db("dtm_ads.payment_failures")
				.whereBetween("created_at", [startTime, endTime])
				.where("amount", ">=", this.alertThresholds.failedAmount.warning)
				.orderBy("created_at", "desc")
				.limit(5)
				.select("*"),
		]);

		const totalAttempts = (successMetrics.count || 0) + (failureMetrics.count || 0);
		const successRate = totalAttempts > 0 ? (successMetrics.count / totalAttempts) * 100 : 100;
		const failureRate = totalAttempts > 0 ? (failureMetrics.count / totalAttempts) * 100 : 0;

		return {
			total_attempts: totalAttempts,
			successful_payments: successMetrics.count || 0,
			failed_payments: failureMetrics.count || 0,
			success_rate: parseFloat(successRate.toFixed(2)),
			failure_rate: parseFloat(failureRate.toFixed(2)),
			total_revenue: parseFloat(successMetrics.total_amount || 0),
			total_failed_amount: parseFloat(failureMetrics.total_amount || 0),
			avg_payment_amount: parseFloat(successMetrics.avg_amount || 0),
			unique_failure_codes: failureMetrics.unique_failure_codes || 0,
			recent_critical_failures: recentFailures,
		};
	}

	/**
	 * Get webhook metrics for monitoring
	 * @param {Date} startTime - Start time
	 * @param {Date} endTime - End time
	 * @returns {Promise<Object>} Webhook metrics
	 */
	async getWebhookMetrics(startTime, endTime) {
		const [webhookStats, recentFailures] = await Promise.all([
			// Webhook processing stats
			db("dtm_ads.webhook_logs")
				.whereBetween("created_at", [startTime, endTime])
				.select(
					db.raw("COUNT(*) as total_webhooks"),
					db.raw("COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_webhooks"),
					db.raw("COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_webhooks"),
					db.raw("AVG(attempts) as avg_attempts"),
					db.raw("MAX(attempts) as max_attempts")
				)
				.first(),

			// Recent webhook failures
			db("dtm_ads.webhook_logs")
				.where({ status: "failed" })
				.whereBetween("created_at", [startTime, endTime])
				.orderBy("created_at", "desc")
				.limit(5)
				.select("*"),
		]);

		const successRate =
			webhookStats.total_webhooks > 0
				? (webhookStats.successful_webhooks / webhookStats.total_webhooks) * 100
				: 100;

		return {
			total_webhooks: webhookStats.total_webhooks || 0,
			successful_webhooks: webhookStats.successful_webhooks || 0,
			failed_webhooks: webhookStats.failed_webhooks || 0,
			success_rate: parseFloat(successRate.toFixed(2)),
			avg_attempts: parseFloat(webhookStats.avg_attempts || 0),
			max_attempts: webhookStats.max_attempts || 0,
			recent_failures: recentFailures,
		};
	}

	/**
	 * Get system health metrics
	 * @returns {Promise<Object>} System health
	 */
	async getSystemHealth() {
		try {
			const [dbHealth, stripeHealth] = await Promise.all([this.checkDatabaseHealth(), this.checkStripeHealth()]);

			return {
				database: dbHealth,
				stripe: stripeHealth,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			logger.error("System health check failed", { error: error.message });
			return {
				database: { status: "unknown", error: error.message },
				stripe: { status: "unknown", error: error.message },
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Check database connectivity and performance
	 * @returns {Promise<Object>} Database health
	 */
	async checkDatabaseHealth() {
		const startTime = Date.now();

		try {
			await db.raw("SELECT 1");
			const responseTime = Date.now() - startTime;

			return {
				status: "healthy",
				response_time_ms: responseTime,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				status: "unhealthy",
				error: error.message,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Check Stripe API connectivity
	 * @returns {Promise<Object>} Stripe health
	 */
	async checkStripeHealth() {
		const startTime = Date.now();

		try {
			const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
			await stripe.balance.retrieve();
			const responseTime = Date.now() - startTime;

			return {
				status: "healthy",
				response_time_ms: responseTime,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				status: "unhealthy",
				error: error.message,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Evaluate alerts based on metrics
	 * @param {Object} data - Monitoring data
	 * @returns {Promise<Array>} List of alerts
	 */
	async evaluateAlerts(data) {
		const alerts = [];
		const { payment, webhook, system, timeWindow } = data;

		// Payment success rate alerts
		if (payment.success_rate < this.alertThresholds.successRate.critical) {
			alerts.push({
				type: "payment_success_rate",
				severity: "critical",
				message: `Payment success rate is critically low: ${payment.success_rate}%`,
				value: payment.success_rate,
				threshold: this.alertThresholds.successRate.critical,
				timeWindow,
			});
		} else if (payment.success_rate < this.alertThresholds.successRate.warning) {
			alerts.push({
				type: "payment_success_rate",
				severity: "warning",
				message: `Payment success rate is below warning threshold: ${payment.success_rate}%`,
				value: payment.success_rate,
				threshold: this.alertThresholds.successRate.warning,
				timeWindow,
			});
		}

		// Failed payment amount alerts
		if (payment.total_failed_amount >= this.alertThresholds.failedAmount.critical) {
			alerts.push({
				type: "failed_payment_amount",
				severity: "critical",
				message: `High failed payment amount: $${payment.total_failed_amount}`,
				value: payment.total_failed_amount,
				threshold: this.alertThresholds.failedAmount.critical,
				timeWindow,
			});
		} else if (payment.total_failed_amount >= this.alertThresholds.failedAmount.warning) {
			alerts.push({
				type: "failed_payment_amount",
				severity: "warning",
				message: `Elevated failed payment amount: $${payment.total_failed_amount}`,
				value: payment.total_failed_amount,
				threshold: this.alertThresholds.failedAmount.warning,
				timeWindow,
			});
		}

		// Webhook success rate alerts
		if (webhook && webhook.success_rate < this.alertThresholds.webhookSuccessRate.critical) {
			alerts.push({
				type: "webhook_success_rate",
				severity: "critical",
				message: `Webhook success rate is critically low: ${webhook.success_rate}%`,
				value: webhook.success_rate,
				threshold: this.alertThresholds.webhookSuccessRate.critical,
				timeWindow,
			});
		} else if (webhook && webhook.success_rate < this.alertThresholds.webhookSuccessRate.warning) {
			alerts.push({
				type: "webhook_success_rate",
				severity: "warning",
				message: `Webhook success rate is below warning threshold: ${webhook.success_rate}%`,
				value: webhook.success_rate,
				threshold: this.alertThresholds.webhookSuccessRate.warning,
				timeWindow,
			});
		}

		// System health alerts
		if (system.database.status === "unhealthy") {
			alerts.push({
				type: "database_health",
				severity: "critical",
				message: `Database is unhealthy: ${system.database.error}`,
				value: system.database.status,
				timeWindow,
			});
		}

		if (system.stripe.status === "unhealthy") {
			alerts.push({
				type: "stripe_health",
				severity: "critical",
				message: `Stripe API is unhealthy: ${system.stripe.error}`,
				value: system.stripe.status,
				timeWindow,
			});
		}

		return alerts;
	}

	/**
	 * Process and send alerts
	 * @param {Array} alerts - List of alerts
	 * @param {Object} context - Monitoring context
	 */
	async processAlerts(alerts, context) {
		for (const alert of alerts) {
			const alertKey = `${alert.type}_${alert.severity}`;
			const lastAlert = this.lastAlerts.get(alertKey);

			// Check cooldown period
			if (lastAlert && Date.now() - lastAlert < this.alertCooldown) {
				continue;
			}

			// Send alert
			await this.sendAlert(alert, context);

			// Update last alert time
			this.lastAlerts.set(alertKey, Date.now());
		}
	}

	/**
	 * Send alert notification
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 */
	async sendAlert(alert, context) {
		try {
			// Log alert
			logger.warn("Payment monitoring alert", {
				alert,
				context: {
					timestamp: context.timestamp,
					timeWindow: context.timeWindow,
					status: context.status,
				},
			});

			// Store alert in database
			await this.storeAlert(alert, context);

			// Send notifications (email, Slack, etc.)
			await this.sendNotifications(alert, context);
		} catch (error) {
			logger.error("Failed to send alert", {
				error: error.message,
				alert,
			});
		}
	}

	/**
	 * Store alert in database
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 */
	async storeAlert(alert, context) {
		try {
			await db("dtm_ads.monitoring_alerts").insert({
				alert_type: alert.type,
				severity: alert.severity,
				message: alert.message,
				alert_data: JSON.stringify(alert),
				context_data: JSON.stringify(context),
				created_at: new Date(),
			});
		} catch (error) {
			logger.error("Failed to store alert", {
				error: error.message,
				alert,
			});
		}
	}

	/**
	 * Send alert notifications
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 */
	async sendNotifications(alert, context) {
		// Email notifications
		if (process.env.ALERT_EMAIL_ENABLED === "true") {
			await this.notificationService.sendEmailAlert(alert, context);
		}

		// Slack notifications
		if (process.env.ALERT_SLACK_ENABLED === "true") {
			await this.notificationService.sendSlackAlert(alert, context);
		}

		// Webhook notifications
		if (process.env.ALERT_WEBHOOK_URL) {
			await this.notificationService.sendWebhookAlert(alert, context);
		}
	}

	/**
	 * Determine overall system status
	 * @param {Array} alerts - List of alerts
	 * @returns {string} Overall status
	 */
	determineOverallStatus(alerts) {
		if (alerts.some((alert) => alert.severity === "critical")) {
			return "critical";
		}
		if (alerts.some((alert) => alert.severity === "warning")) {
			return "warning";
		}
		return "healthy";
	}

	/**
	 * Parse time window string to milliseconds
	 * @param {string} timeWindow - Time window (e.g., '1h', '30m', '24h')
	 * @returns {number} Milliseconds
	 */
	parseTimeWindow(timeWindow) {
		const match = timeWindow.match(/^(\d+)([hm])$/);
		if (!match) {
			throw new Error(`Invalid time window format: ${timeWindow}`);
		}

		const value = parseInt(match[1]);
		const unit = match[2];

		switch (unit) {
			case "h":
				return value * 60 * 60 * 1000;
			case "m":
				return value * 60 * 1000;
			default:
				throw new Error(`Unsupported time unit: ${unit}`);
		}
	}
}

module.exports = PaymentMonitoringService;
