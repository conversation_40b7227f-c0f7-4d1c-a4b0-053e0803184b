import { auth0 } from "@/lib/auth0";
import { NextRequest, NextResponse } from "next/server";

interface RouteParams {
	params: { id: string };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
	try {
		const paramsData = await params;
		const invoiceId = paramsData.id;

		if (!invoiceId) {
			return NextResponse.json({ success: false, message: "Invoice ID is required" }, { status: 400 });
		}

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		// Call the backend API to download invoice PDF
		const response = await fetch(`${process.env.API_BASE_URL}/invoices/${invoiceId}/download`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		// Get the PDF content
		const pdfBuffer = await response.arrayBuffer();
		
		// Get filename from response headers or create default
		const contentDisposition = response.headers.get('content-disposition');
		let filename = 'invoice.pdf';
		if (contentDisposition) {
			const filenameMatch = contentDisposition.match(/filename="(.+)"/);
			if (filenameMatch) {
				filename = filenameMatch[1];
			}
		}

		// Return the PDF with appropriate headers
		return new NextResponse(pdfBuffer, {
			headers: {
				'Content-Type': 'application/pdf',
				'Content-Disposition': `attachment; filename="${filename}"`,
			},
		});

	} catch (error) {
		console.error("Error downloading invoice PDF:", error);
		return NextResponse.json(
			{
				success: false,
				message: "Failed to download invoice PDF",
			},
			{ status: 500 }
		);
	}
}
