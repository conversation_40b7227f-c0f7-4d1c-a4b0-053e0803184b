"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jose";
exports.ids = ["vendor-chunks/jose"];
exports.modules = {

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compactDecrypt: () => (/* binding */ compactDecrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../flattened/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\n\nasync function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await (0,_flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__.flattenedDecrypt)({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactEncrypt: () => (/* binding */ CompactEncrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../flattened/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js\");\n\nclass CompactEncrypt {\n    _flattened;\n    constructor(plaintext) {\n        this._flattened = new _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__.FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this._flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this._flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this._flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this._flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2p3ZS9jb21wYWN0L2VuY3J5cHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkQ7QUFDcEQ7QUFDUDtBQUNBO0FBQ0EsOEJBQThCLG1FQUFnQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcandlXFxjb21wYWN0XFxlbmNyeXB0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZsYXR0ZW5lZEVuY3J5cHQgfSBmcm9tICcuLi9mbGF0dGVuZWQvZW5jcnlwdC5qcyc7XG5leHBvcnQgY2xhc3MgQ29tcGFjdEVuY3J5cHQge1xuICAgIF9mbGF0dGVuZWQ7XG4gICAgY29uc3RydWN0b3IocGxhaW50ZXh0KSB7XG4gICAgICAgIHRoaXMuX2ZsYXR0ZW5lZCA9IG5ldyBGbGF0dGVuZWRFbmNyeXB0KHBsYWludGV4dCk7XG4gICAgfVxuICAgIHNldENvbnRlbnRFbmNyeXB0aW9uS2V5KGNlaykge1xuICAgICAgICB0aGlzLl9mbGF0dGVuZWQuc2V0Q29udGVudEVuY3J5cHRpb25LZXkoY2VrKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHNldEluaXRpYWxpemF0aW9uVmVjdG9yKGl2KSB7XG4gICAgICAgIHRoaXMuX2ZsYXR0ZW5lZC5zZXRJbml0aWFsaXphdGlvblZlY3Rvcihpdik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBzZXRQcm90ZWN0ZWRIZWFkZXIocHJvdGVjdGVkSGVhZGVyKSB7XG4gICAgICAgIHRoaXMuX2ZsYXR0ZW5lZC5zZXRQcm90ZWN0ZWRIZWFkZXIocHJvdGVjdGVkSGVhZGVyKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHNldEtleU1hbmFnZW1lbnRQYXJhbWV0ZXJzKHBhcmFtZXRlcnMpIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkLnNldEtleU1hbmFnZW1lbnRQYXJhbWV0ZXJzKHBhcmFtZXRlcnMpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgYXN5bmMgZW5jcnlwdChrZXksIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgandlID0gYXdhaXQgdGhpcy5fZmxhdHRlbmVkLmVuY3J5cHQoa2V5LCBvcHRpb25zKTtcbiAgICAgICAgcmV0dXJuIFtqd2UucHJvdGVjdGVkLCBqd2UuZW5jcnlwdGVkX2tleSwgandlLml2LCBqd2UuY2lwaGVydGV4dCwgandlLnRhZ10uam9pbignLicpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenedDecrypt: () => (/* binding */ flattenedDecrypt)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../runtime/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/decrypt_key_management.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n/* harmony import */ var _lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/validate_algorithms.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js\");\n\n\n\n\n\n\n\n\n\n\nasync function flattenedDecrypt(jwe, key, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.protected);\n            parsedProt = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.encrypted_key);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    let cek;\n    try {\n        cek = await (0,_lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(alg, key, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported) {\n            throw err;\n        }\n        cek = (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.iv);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.tag);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.ciphertext);\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await (0,_runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.aad);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlattenedEncrypt: () => (/* binding */ FlattenedEncrypt)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/private_symbols.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/private_symbols.js\");\n/* harmony import */ var _runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../runtime/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js\");\n/* harmony import */ var _lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/encrypt_key_management.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n\n\n\n\n\n\n\n\nclass FlattenedEncrypt {\n    _plaintext;\n    _protectedHeader;\n    _sharedUnprotectedHeader;\n    _unprotectedHeader;\n    _aad;\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this._plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader && !this._sharedUnprotectedHeader) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._protectedHeader, this._unprotectedHeader, this._sharedUnprotectedHeader)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n            ...this._sharedUnprotectedHeader,\n        };\n        (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid, new Map(), options?.crit, this._protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this._cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        let cek;\n        {\n            let parameters;\n            ({ cek, encryptedKey, parameters } = await (0,_lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg, enc, key, this._cek, this._keyManagementParameters));\n            if (parameters) {\n                if (options && _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_4__.unprotected in options) {\n                    if (!this._unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this._unprotectedHeader = { ...this._unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this._protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this._protectedHeader = { ...this._protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this._protectedHeader) {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode('');\n        }\n        if (this._aad) {\n            aadMember = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(this._aad);\n            additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await (0,_runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(enc, this._plaintext, cek, this._iv, additionalData);\n        const jwe = {\n            ciphertext: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(iv);\n        }\n        if (tag) {\n            jwe.tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this._protectedHeader) {\n            jwe.protected = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.decoder.decode(protectedHeader);\n        }\n        if (this._sharedUnprotectedHeader) {\n            jwe.unprotected = this._sharedUnprotectedHeader;\n        }\n        if (this._unprotectedHeader) {\n            jwe.header = this._unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwks/local.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwks/local.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLocalJWKSet: () => (/* binding */ createLocalJWKSet)\n/* harmony export */ });\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../key/import.js */ \"(rsc)/./node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\nfunction getKtyFromAlg(alg) {\n    switch (typeof alg === 'string' && alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            return 'RSA';\n        case 'ES':\n            return 'EC';\n        case 'Ed':\n            return 'OKP';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('Unsupported \"alg\" value for a JSON Web Key Set');\n    }\n}\nfunction isJWKSLike(jwks) {\n    return (jwks &&\n        typeof jwks === 'object' &&\n        Array.isArray(jwks.keys) &&\n        jwks.keys.every(isJWKLike));\n}\nfunction isJWKLike(key) {\n    return (0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key);\n}\nfunction clone(obj) {\n    if (typeof structuredClone === 'function') {\n        return structuredClone(obj);\n    }\n    return JSON.parse(JSON.stringify(obj));\n}\nclass LocalJWKSet {\n    _jwks;\n    _cached = new WeakMap();\n    constructor(jwks) {\n        if (!isJWKSLike(jwks)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWKSInvalid('JSON Web Key Set malformed');\n        }\n        this._jwks = clone(jwks);\n    }\n    async getKey(protectedHeader, token) {\n        const { alg, kid } = { ...protectedHeader, ...token?.header };\n        const kty = getKtyFromAlg(alg);\n        const candidates = this._jwks.keys.filter((jwk) => {\n            let candidate = kty === jwk.kty;\n            if (candidate && typeof kid === 'string') {\n                candidate = kid === jwk.kid;\n            }\n            if (candidate && typeof jwk.alg === 'string') {\n                candidate = alg === jwk.alg;\n            }\n            if (candidate && typeof jwk.use === 'string') {\n                candidate = jwk.use === 'sig';\n            }\n            if (candidate && Array.isArray(jwk.key_ops)) {\n                candidate = jwk.key_ops.includes('verify');\n            }\n            if (candidate) {\n                switch (alg) {\n                    case 'ES256':\n                        candidate = jwk.crv === 'P-256';\n                        break;\n                    case 'ES256K':\n                        candidate = jwk.crv === 'secp256k1';\n                        break;\n                    case 'ES384':\n                        candidate = jwk.crv === 'P-384';\n                        break;\n                    case 'ES512':\n                        candidate = jwk.crv === 'P-521';\n                        break;\n                    case 'Ed25519':\n                        candidate = jwk.crv === 'Ed25519';\n                        break;\n                    case 'EdDSA':\n                        candidate = jwk.crv === 'Ed25519' || jwk.crv === 'Ed448';\n                        break;\n                }\n            }\n            return candidate;\n        });\n        const { 0: jwk, length } = candidates;\n        if (length === 0) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWKSNoMatchingKey();\n        }\n        if (length !== 1) {\n            const error = new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWKSMultipleMatchingKeys();\n            const { _cached } = this;\n            error[Symbol.asyncIterator] = async function* () {\n                for (const jwk of candidates) {\n                    try {\n                        yield await importWithAlgCache(_cached, jwk, alg);\n                    }\n                    catch { }\n                }\n            };\n            throw error;\n        }\n        return importWithAlgCache(this._cached, jwk, alg);\n    }\n}\nasync function importWithAlgCache(cache, jwk, alg) {\n    const cached = cache.get(jwk) || cache.set(jwk, {}).get(jwk);\n    if (cached[alg] === undefined) {\n        const key = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_2__.importJWK)({ ...jwk, ext: true }, alg);\n        if (key instanceof Uint8Array || key.type !== 'public') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWKSInvalid('JSON Web Key Set members must be public keys');\n        }\n        cached[alg] = key;\n    }\n    return cached[alg];\n}\nfunction createLocalJWKSet(jwks) {\n    const set = new LocalJWKSet(jwks);\n    const localJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(localJWKSet, {\n        jwks: {\n            value: () => clone(set._jwks),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return localJWKSet;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwks/local.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwks/remote.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwks/remote.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRemoteJWKSet: () => (/* binding */ createRemoteJWKSet),\n/* harmony export */   experimental_jwksCache: () => (/* binding */ experimental_jwksCache),\n/* harmony export */   jwksCache: () => (/* binding */ jwksCache)\n/* harmony export */ });\n/* harmony import */ var _runtime_fetch_jwks_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../runtime/fetch_jwks.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/fetch_jwks.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _local_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./local.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwks/local.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\nfunction isCloudflareWorkers() {\n    return (typeof WebSocketPair !== 'undefined' ||\n        (typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers') ||\n        (typeof EdgeRuntime !== 'undefined' && EdgeRuntime === 'vercel'));\n}\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'jose';\n    const VERSION = 'v5.10.0';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nconst jwksCache = Symbol();\nfunction isFreshJwksCache(input, cacheMaxAge) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || Date.now() - input.uat >= cacheMaxAge) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])) {\n        return false;\n    }\n    return true;\n}\nclass RemoteJWKSet {\n    _url;\n    _timeoutDuration;\n    _cooldownDuration;\n    _cacheMaxAge;\n    _jwksTimestamp;\n    _pendingFetch;\n    _options;\n    _local;\n    _cache;\n    constructor(url, options) {\n        if (!(url instanceof URL)) {\n            throw new TypeError('url must be an instance of URL');\n        }\n        this._url = new URL(url.href);\n        this._options = { agent: options?.agent, headers: options?.headers };\n        this._timeoutDuration =\n            typeof options?.timeoutDuration === 'number' ? options?.timeoutDuration : 5000;\n        this._cooldownDuration =\n            typeof options?.cooldownDuration === 'number' ? options?.cooldownDuration : 30000;\n        this._cacheMaxAge = typeof options?.cacheMaxAge === 'number' ? options?.cacheMaxAge : 600000;\n        if (options?.[jwksCache] !== undefined) {\n            this._cache = options?.[jwksCache];\n            if (isFreshJwksCache(options?.[jwksCache], this._cacheMaxAge)) {\n                this._jwksTimestamp = this._cache.uat;\n                this._local = (0,_local_js__WEBPACK_IMPORTED_MODULE_1__.createLocalJWKSet)(this._cache.jwks);\n            }\n        }\n    }\n    coolingDown() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cooldownDuration\n            : false;\n    }\n    fresh() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cacheMaxAge\n            : false;\n    }\n    async getKey(protectedHeader, token) {\n        if (!this._local || !this.fresh()) {\n            await this.reload();\n        }\n        try {\n            return await this._local(protectedHeader, token);\n        }\n        catch (err) {\n            if (err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWKSNoMatchingKey) {\n                if (this.coolingDown() === false) {\n                    await this.reload();\n                    return this._local(protectedHeader, token);\n                }\n            }\n            throw err;\n        }\n    }\n    async reload() {\n        if (this._pendingFetch && isCloudflareWorkers()) {\n            this._pendingFetch = undefined;\n        }\n        const headers = new Headers(this._options.headers);\n        if (USER_AGENT && !headers.has('User-Agent')) {\n            headers.set('User-Agent', USER_AGENT);\n            this._options.headers = Object.fromEntries(headers.entries());\n        }\n        this._pendingFetch ||= (0,_runtime_fetch_jwks_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this._url, this._timeoutDuration, this._options)\n            .then((json) => {\n            this._local = (0,_local_js__WEBPACK_IMPORTED_MODULE_1__.createLocalJWKSet)(json);\n            if (this._cache) {\n                this._cache.uat = Date.now();\n                this._cache.jwks = json;\n            }\n            this._jwksTimestamp = Date.now();\n            this._pendingFetch = undefined;\n        })\n            .catch((err) => {\n            this._pendingFetch = undefined;\n            throw err;\n        });\n        await this._pendingFetch;\n    }\n}\nfunction createRemoteJWKSet(url, options) {\n    const set = new RemoteJWKSet(url, options);\n    const remoteJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(remoteJWKSet, {\n        coolingDown: {\n            get: () => set.coolingDown(),\n            enumerable: true,\n            configurable: false,\n        },\n        fresh: {\n            get: () => set.fresh(),\n            enumerable: true,\n            configurable: false,\n        },\n        reload: {\n            value: () => set.reload(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n        reloading: {\n            get: () => !!set._pendingFetch,\n            enumerable: true,\n            configurable: false,\n        },\n        jwks: {\n            value: () => set._local?.jwks(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return remoteJWKSet;\n}\nconst experimental_jwksCache = jwksCache;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwks/remote.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jws/compact/verify.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jws/compact/verify.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compactVerify: () => (/* binding */ compactVerify)\n/* harmony export */ });\n/* harmony import */ var _flattened_verify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../flattened/verify.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/verify.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\n\nasync function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await (0,_flattened_verify_js__WEBPACK_IMPORTED_MODULE_2__.flattenedVerify)({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2p3cy9jb21wYWN0L3ZlcmlmeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlEO0FBQ1A7QUFDRTtBQUM3QztBQUNQO0FBQ0EsY0FBYyx5REFBTztBQUNyQjtBQUNBO0FBQ0Esa0JBQWtCLHVEQUFVO0FBQzVCO0FBQ0EsWUFBWSx1REFBdUQ7QUFDbkU7QUFDQSxrQkFBa0IsdURBQVU7QUFDNUI7QUFDQSwyQkFBMkIscUVBQWUsR0FBRyxnREFBZ0Q7QUFDN0YscUJBQXFCO0FBQ3JCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcandzXFxjb21wYWN0XFx2ZXJpZnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZmxhdHRlbmVkVmVyaWZ5IH0gZnJvbSAnLi4vZmxhdHRlbmVkL3ZlcmlmeS5qcyc7XG5pbXBvcnQgeyBKV1NJbnZhbGlkIH0gZnJvbSAnLi4vLi4vdXRpbC9lcnJvcnMuanMnO1xuaW1wb3J0IHsgZGVjb2RlciB9IGZyb20gJy4uLy4uL2xpYi9idWZmZXJfdXRpbHMuanMnO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNvbXBhY3RWZXJpZnkoandzLCBrZXksIG9wdGlvbnMpIHtcbiAgICBpZiAoandzIGluc3RhbmNlb2YgVWludDhBcnJheSkge1xuICAgICAgICBqd3MgPSBkZWNvZGVyLmRlY29kZShqd3MpO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIGp3cyAhPT0gJ3N0cmluZycpIHtcbiAgICAgICAgdGhyb3cgbmV3IEpXU0ludmFsaWQoJ0NvbXBhY3QgSldTIG11c3QgYmUgYSBzdHJpbmcgb3IgVWludDhBcnJheScpO1xuICAgIH1cbiAgICBjb25zdCB7IDA6IHByb3RlY3RlZEhlYWRlciwgMTogcGF5bG9hZCwgMjogc2lnbmF0dXJlLCBsZW5ndGggfSA9IGp3cy5zcGxpdCgnLicpO1xuICAgIGlmIChsZW5ndGggIT09IDMpIHtcbiAgICAgICAgdGhyb3cgbmV3IEpXU0ludmFsaWQoJ0ludmFsaWQgQ29tcGFjdCBKV1MnKTtcbiAgICB9XG4gICAgY29uc3QgdmVyaWZpZWQgPSBhd2FpdCBmbGF0dGVuZWRWZXJpZnkoeyBwYXlsb2FkLCBwcm90ZWN0ZWQ6IHByb3RlY3RlZEhlYWRlciwgc2lnbmF0dXJlIH0sIGtleSwgb3B0aW9ucyk7XG4gICAgY29uc3QgcmVzdWx0ID0geyBwYXlsb2FkOiB2ZXJpZmllZC5wYXlsb2FkLCBwcm90ZWN0ZWRIZWFkZXI6IHZlcmlmaWVkLnByb3RlY3RlZEhlYWRlciB9O1xuICAgIGlmICh0eXBlb2Yga2V5ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiB7IC4uLnJlc3VsdCwga2V5OiB2ZXJpZmllZC5rZXkgfTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jws/compact/verify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/sign.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jws/flattened/sign.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlattenedSign: () => (/* binding */ FlattenedSign)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_sign_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../runtime/sign.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/sign.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/check_key_type.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n\n\n\n\n\n\n\nclass FlattenedSign {\n    _payload;\n    _protectedHeader;\n    _unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this._payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._protectedHeader, this._unprotectedHeader)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n        };\n        const extensions = (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid, new Map([['b64', true]]), options?.crit, this._protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this._protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_3__.checkKeyTypeWithJwk)(alg, key, 'sign');\n        let payload = this._payload;\n        if (b64) {\n            payload = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__.encode)(payload));\n        }\n        let protectedHeader;\n        if (this._protectedHeader) {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode('');\n        }\n        const data = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode('.'), payload);\n        const signature = await (0,_runtime_sign_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(alg, key, data);\n        const jws = {\n            signature: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__.encode)(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.decoder.decode(payload);\n        }\n        if (this._unprotectedHeader) {\n            jws.header = this._unprotectedHeader;\n        }\n        if (this._protectedHeader) {\n            jws.protected = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/verify.js":
/*!*****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jws/flattened/verify.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenedVerify: () => (/* binding */ flattenedVerify)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_verify_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../runtime/verify.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/verify.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/check_key_type.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n/* harmony import */ var _lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/validate_algorithms.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/is_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../key/import.js */ \"(rsc)/./node_modules/jose/dist/node/esm/key/import.js\");\n\n\n\n\n\n\n\n\n\n\n\nasync function flattenedVerify(jws, key, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jws)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jws.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jws.protected);\n            parsedProt = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parsedProt, jws.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__.checkKeyTypeWithJwk)(alg, key, 'verify');\n        if ((0,_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_8__.isJWK)(key)) {\n            key = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_9__.importJWK)(key, alg);\n        }\n    }\n    else {\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__.checkKeyTypeWithJwk)(alg, key, 'verify');\n    }\n    const data = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.concat)(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jws.protected ?? ''), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode('.'), typeof jws.payload === 'string' ? _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jws.signature);\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Failed to base64url decode the signature');\n    }\n    const verified = await (0,_runtime_verify_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(alg, key, signature, data);\n    if (!verified) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jws.payload);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jws/flattened/verify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/decrypt.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/decrypt.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jwtDecrypt: () => (/* binding */ jwtDecrypt)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jwe/compact/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js\");\n/* harmony import */ var _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/jwt_claims_set.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\n\n\nasync function jwtDecrypt(jwt, key, options) {\n    const decrypted = await (0,_jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__.compactDecrypt)(jwt, key, options);\n    const payload = (0,_lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/encrypt.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/encrypt.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EncryptJWT: () => (/* binding */ EncryptJWT)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jwe/compact/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _produce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./produce.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/produce.js\");\n\n\n\nclass EncryptJWT extends _produce_js__WEBPACK_IMPORTED_MODULE_0__.ProduceJWT {\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    _protectedHeader;\n    _replicateIssuerAsHeader;\n    _replicateSubjectAsHeader;\n    _replicateAudienceAsHeader;\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this._replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this._replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this._replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__.CompactEncrypt(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__.encoder.encode(JSON.stringify(this._payload)));\n        if (this._replicateIssuerAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, iss: this._payload.iss };\n        }\n        if (this._replicateSubjectAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, sub: this._payload.sub };\n        }\n        if (this._replicateAudienceAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, aud: this._payload.aud };\n        }\n        enc.setProtectedHeader(this._protectedHeader);\n        if (this._iv) {\n            enc.setInitializationVector(this._iv);\n        }\n        if (this._cek) {\n            enc.setContentEncryptionKey(this._cek);\n        }\n        if (this._keyManagementParameters) {\n            enc.setKeyManagementParameters(this._keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/produce.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/produce.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProduceJWT: () => (/* binding */ ProduceJWT)\n/* harmony export */ });\n/* harmony import */ var _lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/epoch.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_secs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/secs.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js\");\n\n\n\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nclass ProduceJWT {\n    _payload;\n    constructor(payload = {}) {\n        if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, exp: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else if (typeof input === 'string') {\n            this._payload = {\n                ...this._payload,\n                iat: validateInput('setIssuedAt', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input)),\n            };\n        }\n        else {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', input) };\n        }\n        return this;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/produce.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/verify.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/verify.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jwtVerify: () => (/* binding */ jwtVerify)\n/* harmony export */ });\n/* harmony import */ var _jws_compact_verify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jws/compact/verify.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jws/compact/verify.js\");\n/* harmony import */ var _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/jwt_claims_set.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\n\n\nasync function jwtVerify(jwt, key, options) {\n    const verified = await (0,_jws_compact_verify_js__WEBPACK_IMPORTED_MODULE_0__.compactVerify)(jwt, key, options);\n    if (verified.protectedHeader.crit?.includes('b64') && verified.protectedHeader.b64 === false) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = (0,_lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2p3dC92ZXJpZnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5RDtBQUNQO0FBQ0g7QUFDeEM7QUFDUCwyQkFBMkIscUVBQWE7QUFDeEM7QUFDQSxrQkFBa0IsdURBQVU7QUFDNUI7QUFDQSxvQkFBb0Isa0VBQVU7QUFDOUIscUJBQXFCO0FBQ3JCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcand0XFx2ZXJpZnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29tcGFjdFZlcmlmeSB9IGZyb20gJy4uL2p3cy9jb21wYWN0L3ZlcmlmeS5qcyc7XG5pbXBvcnQgand0UGF5bG9hZCBmcm9tICcuLi9saWIvand0X2NsYWltc19zZXQuanMnO1xuaW1wb3J0IHsgSldUSW52YWxpZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBqd3RWZXJpZnkoand0LCBrZXksIG9wdGlvbnMpIHtcbiAgICBjb25zdCB2ZXJpZmllZCA9IGF3YWl0IGNvbXBhY3RWZXJpZnkoand0LCBrZXksIG9wdGlvbnMpO1xuICAgIGlmICh2ZXJpZmllZC5wcm90ZWN0ZWRIZWFkZXIuY3JpdD8uaW5jbHVkZXMoJ2I2NCcpICYmIHZlcmlmaWVkLnByb3RlY3RlZEhlYWRlci5iNjQgPT09IGZhbHNlKSB7XG4gICAgICAgIHRocm93IG5ldyBKV1RJbnZhbGlkKCdKV1RzIE1VU1QgTk9UIHVzZSB1bmVuY29kZWQgcGF5bG9hZCcpO1xuICAgIH1cbiAgICBjb25zdCBwYXlsb2FkID0gand0UGF5bG9hZCh2ZXJpZmllZC5wcm90ZWN0ZWRIZWFkZXIsIHZlcmlmaWVkLnBheWxvYWQsIG9wdGlvbnMpO1xuICAgIGNvbnN0IHJlc3VsdCA9IHsgcGF5bG9hZCwgcHJvdGVjdGVkSGVhZGVyOiB2ZXJpZmllZC5wcm90ZWN0ZWRIZWFkZXIgfTtcbiAgICBpZiAodHlwZW9mIGtleSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICByZXR1cm4geyAuLi5yZXN1bHQsIGtleTogdmVyaWZpZWQua2V5IH07XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/verify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/key/export.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/key/export.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportJWK: () => (/* binding */ exportJWK),\n/* harmony export */   exportPKCS8: () => (/* binding */ exportPKCS8),\n/* harmony export */   exportSPKI: () => (/* binding */ exportSPKI)\n/* harmony export */ });\n/* harmony import */ var _runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/asn1.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js\");\n/* harmony import */ var _runtime_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/key_to_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js\");\n\n\n\nasync function exportSPKI(key) {\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toSPKI)(key);\n}\nasync function exportPKCS8(key) {\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toPKCS8)(key);\n}\nasync function exportJWK(key) {\n    return (0,_runtime_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2tleS9leHBvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEQ7QUFDRTtBQUNkO0FBQ3pDO0FBQ1AsV0FBVyx3REFBWTtBQUN2QjtBQUNPO0FBQ1AsV0FBVyx5REFBYTtBQUN4QjtBQUNPO0FBQ1AsV0FBVyxrRUFBUTtBQUNuQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxca2V5XFxleHBvcnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdG9TUEtJIGFzIGV4cG9ydFB1YmxpYyB9IGZyb20gJy4uL3J1bnRpbWUvYXNuMS5qcyc7XG5pbXBvcnQgeyB0b1BLQ1M4IGFzIGV4cG9ydFByaXZhdGUgfSBmcm9tICcuLi9ydW50aW1lL2FzbjEuanMnO1xuaW1wb3J0IGtleVRvSldLIGZyb20gJy4uL3J1bnRpbWUva2V5X3RvX2p3ay5qcyc7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXhwb3J0U1BLSShrZXkpIHtcbiAgICByZXR1cm4gZXhwb3J0UHVibGljKGtleSk7XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXhwb3J0UEtDUzgoa2V5KSB7XG4gICAgcmV0dXJuIGV4cG9ydFByaXZhdGUoa2V5KTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBleHBvcnRKV0soa2V5KSB7XG4gICAgcmV0dXJuIGtleVRvSldLKGtleSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/key/export.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/key/import.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/key/import.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   importJWK: () => (/* binding */ importJWK),\n/* harmony export */   importPKCS8: () => (/* binding */ importPKCS8),\n/* harmony export */   importSPKI: () => (/* binding */ importSPKI),\n/* harmony export */   importX509: () => (/* binding */ importX509)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/asn1.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js\");\n/* harmony import */ var _runtime_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/jwk_to_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nasync function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromSPKI)(spki, alg, options);\n}\nasync function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromX509)(x509, alg, options);\n}\nasync function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromPKCS8)(pkcs8, alg, options);\n}\nasync function importJWK(jwk, alg) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    alg ||= jwk.alg;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return (0,_runtime_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({ ...jwk, alg });\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/key/import.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/aesgcmkw.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js\");\n/* harmony import */ var _runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\n\n\nasync function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await (0,_runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.iv),\n        tag: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.tag),\n    };\n}\nasync function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return (0,_runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9hZXNnY21rdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QztBQUNBO0FBQ2tCO0FBQ3ZEO0FBQ1A7QUFDQSwwQkFBMEIsK0RBQU87QUFDakM7QUFDQTtBQUNBLFlBQVksNkRBQVM7QUFDckIsYUFBYSw2REFBUztBQUN0QjtBQUNBO0FBQ087QUFDUDtBQUNBLFdBQVcsK0RBQU87QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcYWVzZ2Nta3cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGVuY3J5cHQgZnJvbSAnLi4vcnVudGltZS9lbmNyeXB0LmpzJztcbmltcG9ydCBkZWNyeXB0IGZyb20gJy4uL3J1bnRpbWUvZGVjcnlwdC5qcyc7XG5pbXBvcnQgeyBlbmNvZGUgYXMgYmFzZTY0dXJsIH0gZnJvbSAnLi4vcnVudGltZS9iYXNlNjR1cmwuanMnO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHdyYXAoYWxnLCBrZXksIGNlaywgaXYpIHtcbiAgICBjb25zdCBqd2VBbGdvcml0aG0gPSBhbGcuc2xpY2UoMCwgNyk7XG4gICAgY29uc3Qgd3JhcHBlZCA9IGF3YWl0IGVuY3J5cHQoandlQWxnb3JpdGhtLCBjZWssIGtleSwgaXYsIG5ldyBVaW50OEFycmF5KDApKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBlbmNyeXB0ZWRLZXk6IHdyYXBwZWQuY2lwaGVydGV4dCxcbiAgICAgICAgaXY6IGJhc2U2NHVybCh3cmFwcGVkLml2KSxcbiAgICAgICAgdGFnOiBiYXNlNjR1cmwod3JhcHBlZC50YWcpLFxuICAgIH07XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdW53cmFwKGFsZywga2V5LCBlbmNyeXB0ZWRLZXksIGl2LCB0YWcpIHtcbiAgICBjb25zdCBqd2VBbGdvcml0aG0gPSBhbGcuc2xpY2UoMCwgNyk7XG4gICAgcmV0dXJuIGRlY3J5cHQoandlQWxnb3JpdGhtLCBrZXksIGVuY3J5cHRlZEtleSwgaXYsIHRhZywgbmV3IFVpbnQ4QXJyYXkoMCkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/buffer_utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatKdf: () => (/* binding */ concatKdf),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   lengthAndInput: () => (/* binding */ lengthAndInput),\n/* harmony export */   p2s: () => (/* binding */ p2s),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\");\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js":
/*!****************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/cek.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _runtime_random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/random.js */ \"node:crypto\");\n\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => (0,_runtime_random_js__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9jZWsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFxRDtBQUNYO0FBQ25DO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQWdCLCtCQUErQixJQUFJO0FBQ3pFO0FBQ0E7QUFDQSxpRUFBZSxTQUFTLGtFQUFNLHFDQUFxQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGNlay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuaW1wb3J0IHJhbmRvbSBmcm9tICcuLi9ydW50aW1lL3JhbmRvbS5qcyc7XG5leHBvcnQgZnVuY3Rpb24gYml0TGVuZ3RoKGFsZykge1xuICAgIHN3aXRjaCAoYWxnKSB7XG4gICAgICAgIGNhc2UgJ0ExMjhHQ00nOlxuICAgICAgICAgICAgcmV0dXJuIDEyODtcbiAgICAgICAgY2FzZSAnQTE5MkdDTSc6XG4gICAgICAgICAgICByZXR1cm4gMTkyO1xuICAgICAgICBjYXNlICdBMjU2R0NNJzpcbiAgICAgICAgY2FzZSAnQTEyOENCQy1IUzI1Nic6XG4gICAgICAgICAgICByZXR1cm4gMjU2O1xuICAgICAgICBjYXNlICdBMTkyQ0JDLUhTMzg0JzpcbiAgICAgICAgICAgIHJldHVybiAzODQ7XG4gICAgICAgIGNhc2UgJ0EyNTZDQkMtSFM1MTInOlxuICAgICAgICAgICAgcmV0dXJuIDUxMjtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKGBVbnN1cHBvcnRlZCBKV0UgQWxnb3JpdGhtOiAke2FsZ31gKTtcbiAgICB9XG59XG5leHBvcnQgZGVmYXVsdCAoYWxnKSA9PiByYW5kb20obmV3IFVpbnQ4QXJyYXkoYml0TGVuZ3RoKGFsZykgPj4gMykpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/check_iv_length.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _iv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./iv.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js\");\n\n\nconst checkIvLength = (enc, iv) => {\n    if (iv.length << 3 !== (0,_iv_js__WEBPACK_IMPORTED_MODULE_0__.bitLength)(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Initialization Vector length');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkIvLength);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9jaGVja19pdl9sZW5ndGguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStDO0FBQ1g7QUFDcEM7QUFDQSwyQkFBMkIsaURBQVM7QUFDcEMsa0JBQWtCLHVEQUFVO0FBQzVCO0FBQ0E7QUFDQSxpRUFBZSxhQUFhLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcY2hlY2tfaXZfbGVuZ3RoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpXRUludmFsaWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgeyBiaXRMZW5ndGggfSBmcm9tICcuL2l2LmpzJztcbmNvbnN0IGNoZWNrSXZMZW5ndGggPSAoZW5jLCBpdikgPT4ge1xuICAgIGlmIChpdi5sZW5ndGggPDwgMyAhPT0gYml0TGVuZ3RoKGVuYykpIHtcbiAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0ludmFsaWQgSW5pdGlhbGl6YXRpb24gVmVjdG9yIGxlbmd0aCcpO1xuICAgIH1cbn07XG5leHBvcnQgZGVmYXVsdCBjaGVja0l2TGVuZ3RoO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/check_key_type.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkKeyTypeWithJwk: () => (/* binding */ checkKeyTypeWithJwk),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!(0,_runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, ..._runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!(0,_runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, ..._runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkKeyType.bind(undefined, false));\nconst checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/check_p2s.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/check_p2s.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ checkP2s)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction checkP2s(p2s) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9jaGVja19wMnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDaEM7QUFDZjtBQUNBLGtCQUFrQix1REFBVTtBQUM1QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGNoZWNrX3Aycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKV0VJbnZhbGlkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY2hlY2tQMnMocDJzKSB7XG4gICAgaWYgKCEocDJzIGluc3RhbmNlb2YgVWludDhBcnJheSkgfHwgcDJzLmxlbmd0aCA8IDgpIHtcbiAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ1BCRVMyIFNhbHQgSW5wdXQgbXVzdCBiZSA4IG9yIG1vcmUgb2N0ZXRzJyk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/check_p2s.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/crypto_key.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEncCryptoKey: () => (/* binding */ checkEncCryptoKey),\n/* harmony export */   checkSigCryptoKey: () => (/* binding */ checkSigCryptoKey)\n/* harmony export */ });\nfunction unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nfunction checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'Ed25519': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nfunction checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js":
/*!***********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../runtime/aeskw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/ecdhes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js\");\n/* harmony import */ var _runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../runtime/pbes2kw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js\");\n/* harmony import */ var _runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime/rsaes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/normalize_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../key/import.js */ \"(rsc)/./node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var _check_key_type_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check_key_type.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nasync function decryptKeyManagement(alg, key, encryptedKey, joseHeader, options) {\n    (0,_check_key_type_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alg, key, 'decrypt');\n    key = (await _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].normalizePrivateKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(joseHeader.epk))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            if (!_runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__.ecdhAllowed(key))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_5__.importJWK)(joseHeader.epk, alg);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.apu);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.apv);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_7__.bitLength)(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__.unwrap)(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_9__.decrypt)(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.p2s);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return (0,_runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_10__.decrypt)(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__.unwrap)(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.iv);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.tag);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the tag');\n            }\n            return (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_11__.unwrap)(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decryptKeyManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9kZWNyeXB0X2tleV9tYW5hZ2VtZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0Q7QUFDVDtBQUNjO0FBQ0o7QUFDTztBQUNWO0FBQ2E7QUFDVjtBQUNWO0FBQ0U7QUFDVDtBQUNhO0FBQ25EO0FBQ0EsSUFBSSw4REFBWTtBQUNoQixpQkFBaUIsaUVBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix5REFBUTtBQUN6QiwwQkFBMEIsdURBQVU7QUFDcEMsaUJBQWlCLDJEQUFnQjtBQUNqQywwQkFBMEIsNkRBQWdCO0FBQzFDLDhCQUE4Qix5REFBUztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4Qix1REFBVTtBQUN4QztBQUNBLGlDQUFpQyw2REFBUztBQUMxQztBQUNBO0FBQ0EsOEJBQThCLHVEQUFVO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHVEQUFVO0FBQ3hDO0FBQ0EsaUNBQWlDLDZEQUFTO0FBQzFDO0FBQ0E7QUFDQSw4QkFBOEIsdURBQVU7QUFDeEM7QUFDQTtBQUNBLHVDQUF1Qyx5REFBYyx5RUFBeUUsc0RBQVM7QUFDdkk7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDLG1CQUFtQix5REFBSztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQyxtQkFBbUIsMERBQUs7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQVM7QUFDL0I7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBLG1CQUFtQiw2REFBTztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDLG1CQUFtQix5REFBSztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0E7QUFDQSxxQkFBcUIsNkRBQVM7QUFDOUI7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQVM7QUFDL0I7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBLG1CQUFtQixxREFBUTtBQUMzQjtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQSxpRUFBZSxvQkFBb0IsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxkZWNyeXB0X2tleV9tYW5hZ2VtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVud3JhcCBhcyBhZXNLdyB9IGZyb20gJy4uL3J1bnRpbWUvYWVza3cuanMnO1xuaW1wb3J0ICogYXMgRUNESCBmcm9tICcuLi9ydW50aW1lL2VjZGhlcy5qcyc7XG5pbXBvcnQgeyBkZWNyeXB0IGFzIHBiZXMyS3cgfSBmcm9tICcuLi9ydW50aW1lL3BiZXMya3cuanMnO1xuaW1wb3J0IHsgZGVjcnlwdCBhcyByc2FFcyB9IGZyb20gJy4uL3J1bnRpbWUvcnNhZXMuanMnO1xuaW1wb3J0IHsgZGVjb2RlIGFzIGJhc2U2NHVybCB9IGZyb20gJy4uL3J1bnRpbWUvYmFzZTY0dXJsLmpzJztcbmltcG9ydCBub3JtYWxpemUgZnJvbSAnLi4vcnVudGltZS9ub3JtYWxpemVfa2V5LmpzJztcbmltcG9ydCB7IEpPU0VOb3RTdXBwb3J0ZWQsIEpXRUludmFsaWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgeyBiaXRMZW5ndGggYXMgY2VrTGVuZ3RoIH0gZnJvbSAnLi4vbGliL2Nlay5qcyc7XG5pbXBvcnQgeyBpbXBvcnRKV0sgfSBmcm9tICcuLi9rZXkvaW1wb3J0LmpzJztcbmltcG9ydCBjaGVja0tleVR5cGUgZnJvbSAnLi9jaGVja19rZXlfdHlwZS5qcyc7XG5pbXBvcnQgaXNPYmplY3QgZnJvbSAnLi9pc19vYmplY3QuanMnO1xuaW1wb3J0IHsgdW53cmFwIGFzIGFlc0djbUt3IH0gZnJvbSAnLi9hZXNnY21rdy5qcyc7XG5hc3luYyBmdW5jdGlvbiBkZWNyeXB0S2V5TWFuYWdlbWVudChhbGcsIGtleSwgZW5jcnlwdGVkS2V5LCBqb3NlSGVhZGVyLCBvcHRpb25zKSB7XG4gICAgY2hlY2tLZXlUeXBlKGFsZywga2V5LCAnZGVjcnlwdCcpO1xuICAgIGtleSA9IChhd2FpdCBub3JtYWxpemUubm9ybWFsaXplUHJpdmF0ZUtleT8uKGtleSwgYWxnKSkgfHwga2V5O1xuICAgIHN3aXRjaCAoYWxnKSB7XG4gICAgICAgIGNhc2UgJ2Rpcic6IHtcbiAgICAgICAgICAgIGlmIChlbmNyeXB0ZWRLZXkgIT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnRW5jb3VudGVyZWQgdW5leHBlY3RlZCBKV0UgRW5jcnlwdGVkIEtleScpO1xuICAgICAgICAgICAgcmV0dXJuIGtleTtcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdFQ0RILUVTJzpcbiAgICAgICAgICAgIGlmIChlbmNyeXB0ZWRLZXkgIT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnRW5jb3VudGVyZWQgdW5leHBlY3RlZCBKV0UgRW5jcnlwdGVkIEtleScpO1xuICAgICAgICBjYXNlICdFQ0RILUVTK0ExMjhLVyc6XG4gICAgICAgIGNhc2UgJ0VDREgtRVMrQTE5MktXJzpcbiAgICAgICAgY2FzZSAnRUNESC1FUytBMjU2S1cnOiB7XG4gICAgICAgICAgICBpZiAoIWlzT2JqZWN0KGpvc2VIZWFkZXIuZXBrKSlcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZChgSk9TRSBIZWFkZXIgXCJlcGtcIiAoRXBoZW1lcmFsIFB1YmxpYyBLZXkpIG1pc3Npbmcgb3IgaW52YWxpZGApO1xuICAgICAgICAgICAgaWYgKCFFQ0RILmVjZGhBbGxvd2VkKGtleSkpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoJ0VDREggd2l0aCB0aGUgcHJvdmlkZWQga2V5IGlzIG5vdCBhbGxvd2VkIG9yIG5vdCBzdXBwb3J0ZWQgYnkgeW91ciBqYXZhc2NyaXB0IHJ1bnRpbWUnKTtcbiAgICAgICAgICAgIGNvbnN0IGVwayA9IGF3YWl0IGltcG9ydEpXSyhqb3NlSGVhZGVyLmVwaywgYWxnKTtcbiAgICAgICAgICAgIGxldCBwYXJ0eVVJbmZvO1xuICAgICAgICAgICAgbGV0IHBhcnR5VkluZm87XG4gICAgICAgICAgICBpZiAoam9zZUhlYWRlci5hcHUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2Ygam9zZUhlYWRlci5hcHUgIT09ICdzdHJpbmcnKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZChgSk9TRSBIZWFkZXIgXCJhcHVcIiAoQWdyZWVtZW50IFBhcnR5VUluZm8pIGludmFsaWRgKTtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBwYXJ0eVVJbmZvID0gYmFzZTY0dXJsKGpvc2VIZWFkZXIuYXB1KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnRmFpbGVkIHRvIGJhc2U2NHVybCBkZWNvZGUgdGhlIGFwdScpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChqb3NlSGVhZGVyLmFwdiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBqb3NlSGVhZGVyLmFwdiAhPT0gJ3N0cmluZycpXG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKGBKT1NFIEhlYWRlciBcImFwdlwiIChBZ3JlZW1lbnQgUGFydHlWSW5mbykgaW52YWxpZGApO1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIHBhcnR5VkluZm8gPSBiYXNlNjR1cmwoam9zZUhlYWRlci5hcHYpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdGYWlsZWQgdG8gYmFzZTY0dXJsIGRlY29kZSB0aGUgYXB2Jyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgc2hhcmVkU2VjcmV0ID0gYXdhaXQgRUNESC5kZXJpdmVLZXkoZXBrLCBrZXksIGFsZyA9PT0gJ0VDREgtRVMnID8gam9zZUhlYWRlci5lbmMgOiBhbGcsIGFsZyA9PT0gJ0VDREgtRVMnID8gY2VrTGVuZ3RoKGpvc2VIZWFkZXIuZW5jKSA6IHBhcnNlSW50KGFsZy5zbGljZSgtNSwgLTIpLCAxMCksIHBhcnR5VUluZm8sIHBhcnR5VkluZm8pO1xuICAgICAgICAgICAgaWYgKGFsZyA9PT0gJ0VDREgtRVMnKVxuICAgICAgICAgICAgICAgIHJldHVybiBzaGFyZWRTZWNyZXQ7XG4gICAgICAgICAgICBpZiAoZW5jcnlwdGVkS2V5ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0pXRSBFbmNyeXB0ZWQgS2V5IG1pc3NpbmcnKTtcbiAgICAgICAgICAgIHJldHVybiBhZXNLdyhhbGcuc2xpY2UoLTYpLCBzaGFyZWRTZWNyZXQsIGVuY3J5cHRlZEtleSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnUlNBMV81JzpcbiAgICAgICAgY2FzZSAnUlNBLU9BRVAnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC0yNTYnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC0zODQnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC01MTInOiB7XG4gICAgICAgICAgICBpZiAoZW5jcnlwdGVkS2V5ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0pXRSBFbmNyeXB0ZWQgS2V5IG1pc3NpbmcnKTtcbiAgICAgICAgICAgIHJldHVybiByc2FFcyhhbGcsIGtleSwgZW5jcnlwdGVkS2V5KTtcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdQQkVTMi1IUzI1NitBMTI4S1cnOlxuICAgICAgICBjYXNlICdQQkVTMi1IUzM4NCtBMTkyS1cnOlxuICAgICAgICBjYXNlICdQQkVTMi1IUzUxMitBMjU2S1cnOiB7XG4gICAgICAgICAgICBpZiAoZW5jcnlwdGVkS2V5ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0pXRSBFbmNyeXB0ZWQgS2V5IG1pc3NpbmcnKTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygam9zZUhlYWRlci5wMmMgIT09ICdudW1iZXInKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKGBKT1NFIEhlYWRlciBcInAyY1wiIChQQkVTMiBDb3VudCkgbWlzc2luZyBvciBpbnZhbGlkYCk7XG4gICAgICAgICAgICBjb25zdCBwMmNMaW1pdCA9IG9wdGlvbnM/Lm1heFBCRVMyQ291bnQgfHwgMTBfMDAwO1xuICAgICAgICAgICAgaWYgKGpvc2VIZWFkZXIucDJjID4gcDJjTGltaXQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoYEpPU0UgSGVhZGVyIFwicDJjXCIgKFBCRVMyIENvdW50KSBvdXQgaXMgb2YgYWNjZXB0YWJsZSBib3VuZHNgKTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygam9zZUhlYWRlci5wMnMgIT09ICdzdHJpbmcnKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKGBKT1NFIEhlYWRlciBcInAyc1wiIChQQkVTMiBTYWx0KSBtaXNzaW5nIG9yIGludmFsaWRgKTtcbiAgICAgICAgICAgIGxldCBwMnM7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHAycyA9IGJhc2U2NHVybChqb3NlSGVhZGVyLnAycyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0ZhaWxlZCB0byBiYXNlNjR1cmwgZGVjb2RlIHRoZSBwMnMnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBwYmVzMkt3KGFsZywga2V5LCBlbmNyeXB0ZWRLZXksIGpvc2VIZWFkZXIucDJjLCBwMnMpO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ0ExMjhLVyc6XG4gICAgICAgIGNhc2UgJ0ExOTJLVyc6XG4gICAgICAgIGNhc2UgJ0EyNTZLVyc6IHtcbiAgICAgICAgICAgIGlmIChlbmNyeXB0ZWRLZXkgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnSldFIEVuY3J5cHRlZCBLZXkgbWlzc2luZycpO1xuICAgICAgICAgICAgcmV0dXJuIGFlc0t3KGFsZywga2V5LCBlbmNyeXB0ZWRLZXkpO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ0ExMjhHQ01LVyc6XG4gICAgICAgIGNhc2UgJ0ExOTJHQ01LVyc6XG4gICAgICAgIGNhc2UgJ0EyNTZHQ01LVyc6IHtcbiAgICAgICAgICAgIGlmIChlbmNyeXB0ZWRLZXkgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnSldFIEVuY3J5cHRlZCBLZXkgbWlzc2luZycpO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBqb3NlSGVhZGVyLml2ICE9PSAnc3RyaW5nJylcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZChgSk9TRSBIZWFkZXIgXCJpdlwiIChJbml0aWFsaXphdGlvbiBWZWN0b3IpIG1pc3Npbmcgb3IgaW52YWxpZGApO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBqb3NlSGVhZGVyLnRhZyAhPT0gJ3N0cmluZycpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoYEpPU0UgSGVhZGVyIFwidGFnXCIgKEF1dGhlbnRpY2F0aW9uIFRhZykgbWlzc2luZyBvciBpbnZhbGlkYCk7XG4gICAgICAgICAgICBsZXQgaXY7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGl2ID0gYmFzZTY0dXJsKGpvc2VIZWFkZXIuaXYpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdGYWlsZWQgdG8gYmFzZTY0dXJsIGRlY29kZSB0aGUgaXYnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCB0YWc7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHRhZyA9IGJhc2U2NHVybChqb3NlSGVhZGVyLnRhZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0ZhaWxlZCB0byBiYXNlNjR1cmwgZGVjb2RlIHRoZSB0YWcnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBhZXNHY21LdyhhbGcsIGtleSwgZW5jcnlwdGVkS2V5LCBpdiwgdGFnKTtcbiAgICAgICAgfVxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZCgnSW52YWxpZCBvciB1bnN1cHBvcnRlZCBcImFsZ1wiIChKV0UgQWxnb3JpdGhtKSBoZWFkZXIgdmFsdWUnKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbmV4cG9ydCBkZWZhdWx0IGRlY3J5cHRLZXlNYW5hZ2VtZW50O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js":
/*!***********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../runtime/aeskw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/ecdhes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js\");\n/* harmony import */ var _runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime/pbes2kw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js\");\n/* harmony import */ var _runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../runtime/rsaes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/normalize_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _key_export_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../key/export.js */ \"(rsc)/./node_modules/jose/dist/node/esm/key/export.js\");\n/* harmony import */ var _check_key_type_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check_key_type.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js\");\n\n\n\n\n\n\n\n\n\n\n\nasync function encryptKeyManagement(alg, enc, key, providedCek, providedParameters = {}) {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    (0,_check_key_type_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alg, key, 'encrypt');\n    key = (await _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].normalizePublicKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!_runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.ecdhAllowed(key)) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let { epk: ephemeralKey } = providedParameters;\n            ephemeralKey ||= (await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.generateEpk(key)).privateKey;\n            const { x, y, crv, kty } = await (0,_key_export_js__WEBPACK_IMPORTED_MODULE_4__.exportJWK)(ephemeralKey);\n            const sharedSecret = await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__.bitLength)(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apu);\n            if (apv)\n                parameters.apv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap)(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await (0,_runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_8__.encrypt)(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__.encrypt)(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap)(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__.wrap)(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encryptKeyManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/epoch.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((date) => Math.floor(date.getTime() / 1000));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9lcG9jaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsMkNBQTJDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcZXBvY2guanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgKGRhdGUpID0+IE1hdGguZmxvb3IoZGF0ZS5nZXRUaW1lKCkgLyAxMDAwKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/invalid_key_input.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   withAlg: () => (/* binding */ withAlg)\n/* harmony export */ });\nfunction message(msg, actual, ...types) {\n    types = types.filter(Boolean);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n});\nfunction withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/is_disjoint.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isDisjoint);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pc19kaXNqb2ludC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsVUFBVSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGlzX2Rpc2pvaW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzRGlzam9pbnQgPSAoLi4uaGVhZGVycykgPT4ge1xuICAgIGNvbnN0IHNvdXJjZXMgPSBoZWFkZXJzLmZpbHRlcihCb29sZWFuKTtcbiAgICBpZiAoc291cmNlcy5sZW5ndGggPT09IDAgfHwgc291cmNlcy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGxldCBhY2M7XG4gICAgZm9yIChjb25zdCBoZWFkZXIgb2Ygc291cmNlcykge1xuICAgICAgICBjb25zdCBwYXJhbWV0ZXJzID0gT2JqZWN0LmtleXMoaGVhZGVyKTtcbiAgICAgICAgaWYgKCFhY2MgfHwgYWNjLnNpemUgPT09IDApIHtcbiAgICAgICAgICAgIGFjYyA9IG5ldyBTZXQocGFyYW1ldGVycyk7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IHBhcmFtZXRlciBvZiBwYXJhbWV0ZXJzKSB7XG4gICAgICAgICAgICBpZiAoYWNjLmhhcyhwYXJhbWV0ZXIpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYWNjLmFkZChwYXJhbWV0ZXIpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufTtcbmV4cG9ydCBkZWZhdWx0IGlzRGlzam9pbnQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/is_jwk.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   isPrivateJWK: () => (/* binding */ isPrivateJWK),\n/* harmony export */   isPublicJWK: () => (/* binding */ isPublicJWK),\n/* harmony export */   isSecretJWK: () => (/* binding */ isSecretJWK)\n/* harmony export */ });\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\nfunction isJWK(key) {\n    return (0,_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) && typeof key.kty === 'string';\n}\nfunction isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nfunction isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nfunction isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pc19qd2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFDL0I7QUFDUCxXQUFXLHlEQUFRO0FBQ25CO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGlzX2p3ay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaXNPYmplY3QgZnJvbSAnLi9pc19vYmplY3QuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGlzSldLKGtleSkge1xuICAgIHJldHVybiBpc09iamVjdChrZXkpICYmIHR5cGVvZiBrZXkua3R5ID09PSAnc3RyaW5nJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1ByaXZhdGVKV0soa2V5KSB7XG4gICAgcmV0dXJuIGtleS5rdHkgIT09ICdvY3QnICYmIHR5cGVvZiBrZXkuZCA9PT0gJ3N0cmluZyc7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNQdWJsaWNKV0soa2V5KSB7XG4gICAgcmV0dXJuIGtleS5rdHkgIT09ICdvY3QnICYmIHR5cGVvZiBrZXkuZCA9PT0gJ3VuZGVmaW5lZCc7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNTZWNyZXRKV0soa2V5KSB7XG4gICAgcmV0dXJuIGlzSldLKGtleSkgJiYga2V5Lmt0eSA9PT0gJ29jdCcgJiYgdHlwZW9mIGtleS5rID09PSAnc3RyaW5nJztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/is_object.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isObject)\n/* harmony export */ });\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pc19vYmplY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGlzX29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc09iamVjdExpa2UodmFsdWUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiB2YWx1ZSAhPT0gbnVsbDtcbn1cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzT2JqZWN0KGlucHV0KSB7XG4gICAgaWYgKCFpc09iamVjdExpa2UoaW5wdXQpIHx8IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChpbnB1dCkgIT09ICdbb2JqZWN0IE9iamVjdF0nKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKE9iamVjdC5nZXRQcm90b3R5cGVPZihpbnB1dCkgPT09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGxldCBwcm90byA9IGlucHV0O1xuICAgIHdoaWxlIChPYmplY3QuZ2V0UHJvdG90eXBlT2YocHJvdG8pICE9PSBudWxsKSB7XG4gICAgICAgIHByb3RvID0gT2JqZWN0LmdldFByb3RvdHlwZU9mKHByb3RvKTtcbiAgICB9XG4gICAgcmV0dXJuIE9iamVjdC5nZXRQcm90b3R5cGVPZihpbnB1dCkgPT09IHByb3RvO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js":
/*!***************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/iv.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _runtime_random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/random.js */ \"node:crypto\");\n\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => (0,_runtime_random_js__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pdi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFEO0FBQ1g7QUFDbkM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0IsK0JBQStCLElBQUk7QUFDekU7QUFDQTtBQUNBLGlFQUFlLFNBQVMsa0VBQU0scUNBQXFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcaXYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSk9TRU5vdFN1cHBvcnRlZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmltcG9ydCByYW5kb20gZnJvbSAnLi4vcnVudGltZS9yYW5kb20uanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGJpdExlbmd0aChhbGcpIHtcbiAgICBzd2l0Y2ggKGFsZykge1xuICAgICAgICBjYXNlICdBMTI4R0NNJzpcbiAgICAgICAgY2FzZSAnQTEyOEdDTUtXJzpcbiAgICAgICAgY2FzZSAnQTE5MkdDTSc6XG4gICAgICAgIGNhc2UgJ0ExOTJHQ01LVyc6XG4gICAgICAgIGNhc2UgJ0EyNTZHQ00nOlxuICAgICAgICBjYXNlICdBMjU2R0NNS1cnOlxuICAgICAgICAgICAgcmV0dXJuIDk2O1xuICAgICAgICBjYXNlICdBMTI4Q0JDLUhTMjU2JzpcbiAgICAgICAgY2FzZSAnQTE5MkNCQy1IUzM4NCc6XG4gICAgICAgIGNhc2UgJ0EyNTZDQkMtSFM1MTInOlxuICAgICAgICAgICAgcmV0dXJuIDEyODtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKGBVbnN1cHBvcnRlZCBKV0UgQWxnb3JpdGhtOiAke2FsZ31gKTtcbiAgICB9XG59XG5leHBvcnQgZGVmYXVsdCAoYWxnKSA9PiByYW5kb20obmV3IFVpbnQ4QXJyYXkoYml0TGVuZ3RoKGFsZykgPj4gMykpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _epoch_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./epoch.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js\");\n/* harmony import */ var _secs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./secs.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nconst normalizeTyp = (value) => value.toLowerCase().replace(/^application\\//, '');\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((protectedHeader, encodedPayload, options = {}) => {\n    let payload;\n    try {\n        payload = JSON.parse(_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(payload)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9qd3RfY2xhaW1zX3NldC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUY7QUFDekM7QUFDYjtBQUNGO0FBQ1M7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSw4Q0FBOEM7QUFDN0Q7QUFDQTtBQUNBLDZCQUE2QixxREFBTztBQUNwQztBQUNBO0FBQ0E7QUFDQSxTQUFTLHlEQUFRO0FBQ2pCLGtCQUFrQix1REFBVTtBQUM1QjtBQUNBLFlBQVksTUFBTTtBQUNsQjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IscUVBQXdCO0FBQzFDO0FBQ0EsWUFBWSw4REFBOEQ7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixxRUFBd0Isc0JBQXNCLE1BQU07QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IscUVBQXdCO0FBQzFDO0FBQ0E7QUFDQSxrQkFBa0IscUVBQXdCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixxRUFBd0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isb0RBQUk7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGNBQWM7QUFDMUIsZ0JBQWdCLHFEQUFLO0FBQ3JCO0FBQ0Esa0JBQWtCLHFFQUF3QjtBQUMxQztBQUNBO0FBQ0E7QUFDQSxzQkFBc0IscUVBQXdCO0FBQzlDO0FBQ0E7QUFDQSxzQkFBc0IscUVBQXdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHFFQUF3QjtBQUM5QztBQUNBO0FBQ0Esc0JBQXNCLHVEQUFVO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLG9EQUFJO0FBQ3hFO0FBQ0Esc0JBQXNCLHVEQUFVO0FBQ2hDO0FBQ0E7QUFDQSxzQkFBc0IscUVBQXdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxqd3RfY2xhaW1zX3NldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKV1RDbGFpbVZhbGlkYXRpb25GYWlsZWQsIEpXVEV4cGlyZWQsIEpXVEludmFsaWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgeyBkZWNvZGVyIH0gZnJvbSAnLi9idWZmZXJfdXRpbHMuanMnO1xuaW1wb3J0IGVwb2NoIGZyb20gJy4vZXBvY2guanMnO1xuaW1wb3J0IHNlY3MgZnJvbSAnLi9zZWNzLmpzJztcbmltcG9ydCBpc09iamVjdCBmcm9tICcuL2lzX29iamVjdC5qcyc7XG5jb25zdCBub3JtYWxpemVUeXAgPSAodmFsdWUpID0+IHZhbHVlLnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvXmFwcGxpY2F0aW9uXFwvLywgJycpO1xuY29uc3QgY2hlY2tBdWRpZW5jZVByZXNlbmNlID0gKGF1ZFBheWxvYWQsIGF1ZE9wdGlvbikgPT4ge1xuICAgIGlmICh0eXBlb2YgYXVkUGF5bG9hZCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIGF1ZE9wdGlvbi5pbmNsdWRlcyhhdWRQYXlsb2FkKTtcbiAgICB9XG4gICAgaWYgKEFycmF5LmlzQXJyYXkoYXVkUGF5bG9hZCkpIHtcbiAgICAgICAgcmV0dXJuIGF1ZE9wdGlvbi5zb21lKFNldC5wcm90b3R5cGUuaGFzLmJpbmQobmV3IFNldChhdWRQYXlsb2FkKSkpO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59O1xuZXhwb3J0IGRlZmF1bHQgKHByb3RlY3RlZEhlYWRlciwgZW5jb2RlZFBheWxvYWQsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgIGxldCBwYXlsb2FkO1xuICAgIHRyeSB7XG4gICAgICAgIHBheWxvYWQgPSBKU09OLnBhcnNlKGRlY29kZXIuZGVjb2RlKGVuY29kZWRQYXlsb2FkKSk7XG4gICAgfVxuICAgIGNhdGNoIHtcbiAgICB9XG4gICAgaWYgKCFpc09iamVjdChwYXlsb2FkKSkge1xuICAgICAgICB0aHJvdyBuZXcgSldUSW52YWxpZCgnSldUIENsYWltcyBTZXQgbXVzdCBiZSBhIHRvcC1sZXZlbCBKU09OIG9iamVjdCcpO1xuICAgIH1cbiAgICBjb25zdCB7IHR5cCB9ID0gb3B0aW9ucztcbiAgICBpZiAodHlwICYmXG4gICAgICAgICh0eXBlb2YgcHJvdGVjdGVkSGVhZGVyLnR5cCAhPT0gJ3N0cmluZycgfHxcbiAgICAgICAgICAgIG5vcm1hbGl6ZVR5cChwcm90ZWN0ZWRIZWFkZXIudHlwKSAhPT0gbm9ybWFsaXplVHlwKHR5cCkpKSB7XG4gICAgICAgIHRocm93IG5ldyBKV1RDbGFpbVZhbGlkYXRpb25GYWlsZWQoJ3VuZXhwZWN0ZWQgXCJ0eXBcIiBKV1QgaGVhZGVyIHZhbHVlJywgcGF5bG9hZCwgJ3R5cCcsICdjaGVja19mYWlsZWQnKTtcbiAgICB9XG4gICAgY29uc3QgeyByZXF1aXJlZENsYWltcyA9IFtdLCBpc3N1ZXIsIHN1YmplY3QsIGF1ZGllbmNlLCBtYXhUb2tlbkFnZSB9ID0gb3B0aW9ucztcbiAgICBjb25zdCBwcmVzZW5jZUNoZWNrID0gWy4uLnJlcXVpcmVkQ2xhaW1zXTtcbiAgICBpZiAobWF4VG9rZW5BZ2UgIT09IHVuZGVmaW5lZClcbiAgICAgICAgcHJlc2VuY2VDaGVjay5wdXNoKCdpYXQnKTtcbiAgICBpZiAoYXVkaWVuY2UgIT09IHVuZGVmaW5lZClcbiAgICAgICAgcHJlc2VuY2VDaGVjay5wdXNoKCdhdWQnKTtcbiAgICBpZiAoc3ViamVjdCAhPT0gdW5kZWZpbmVkKVxuICAgICAgICBwcmVzZW5jZUNoZWNrLnB1c2goJ3N1YicpO1xuICAgIGlmIChpc3N1ZXIgIT09IHVuZGVmaW5lZClcbiAgICAgICAgcHJlc2VuY2VDaGVjay5wdXNoKCdpc3MnKTtcbiAgICBmb3IgKGNvbnN0IGNsYWltIG9mIG5ldyBTZXQocHJlc2VuY2VDaGVjay5yZXZlcnNlKCkpKSB7XG4gICAgICAgIGlmICghKGNsYWltIGluIHBheWxvYWQpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgSldUQ2xhaW1WYWxpZGF0aW9uRmFpbGVkKGBtaXNzaW5nIHJlcXVpcmVkIFwiJHtjbGFpbX1cIiBjbGFpbWAsIHBheWxvYWQsIGNsYWltLCAnbWlzc2luZycpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChpc3N1ZXIgJiZcbiAgICAgICAgIShBcnJheS5pc0FycmF5KGlzc3VlcikgPyBpc3N1ZXIgOiBbaXNzdWVyXSkuaW5jbHVkZXMocGF5bG9hZC5pc3MpKSB7XG4gICAgICAgIHRocm93IG5ldyBKV1RDbGFpbVZhbGlkYXRpb25GYWlsZWQoJ3VuZXhwZWN0ZWQgXCJpc3NcIiBjbGFpbSB2YWx1ZScsIHBheWxvYWQsICdpc3MnLCAnY2hlY2tfZmFpbGVkJyk7XG4gICAgfVxuICAgIGlmIChzdWJqZWN0ICYmIHBheWxvYWQuc3ViICE9PSBzdWJqZWN0KSB7XG4gICAgICAgIHRocm93IG5ldyBKV1RDbGFpbVZhbGlkYXRpb25GYWlsZWQoJ3VuZXhwZWN0ZWQgXCJzdWJcIiBjbGFpbSB2YWx1ZScsIHBheWxvYWQsICdzdWInLCAnY2hlY2tfZmFpbGVkJyk7XG4gICAgfVxuICAgIGlmIChhdWRpZW5jZSAmJlxuICAgICAgICAhY2hlY2tBdWRpZW5jZVByZXNlbmNlKHBheWxvYWQuYXVkLCB0eXBlb2YgYXVkaWVuY2UgPT09ICdzdHJpbmcnID8gW2F1ZGllbmNlXSA6IGF1ZGllbmNlKSkge1xuICAgICAgICB0aHJvdyBuZXcgSldUQ2xhaW1WYWxpZGF0aW9uRmFpbGVkKCd1bmV4cGVjdGVkIFwiYXVkXCIgY2xhaW0gdmFsdWUnLCBwYXlsb2FkLCAnYXVkJywgJ2NoZWNrX2ZhaWxlZCcpO1xuICAgIH1cbiAgICBsZXQgdG9sZXJhbmNlO1xuICAgIHN3aXRjaCAodHlwZW9mIG9wdGlvbnMuY2xvY2tUb2xlcmFuY2UpIHtcbiAgICAgICAgY2FzZSAnc3RyaW5nJzpcbiAgICAgICAgICAgIHRvbGVyYW5jZSA9IHNlY3Mob3B0aW9ucy5jbG9ja1RvbGVyYW5jZSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbnVtYmVyJzpcbiAgICAgICAgICAgIHRvbGVyYW5jZSA9IG9wdGlvbnMuY2xvY2tUb2xlcmFuY2U7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAndW5kZWZpbmVkJzpcbiAgICAgICAgICAgIHRvbGVyYW5jZSA9IDA7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0ludmFsaWQgY2xvY2tUb2xlcmFuY2Ugb3B0aW9uIHR5cGUnKTtcbiAgICB9XG4gICAgY29uc3QgeyBjdXJyZW50RGF0ZSB9ID0gb3B0aW9ucztcbiAgICBjb25zdCBub3cgPSBlcG9jaChjdXJyZW50RGF0ZSB8fCBuZXcgRGF0ZSgpKTtcbiAgICBpZiAoKHBheWxvYWQuaWF0ICE9PSB1bmRlZmluZWQgfHwgbWF4VG9rZW5BZ2UpICYmIHR5cGVvZiBwYXlsb2FkLmlhdCAhPT0gJ251bWJlcicpIHtcbiAgICAgICAgdGhyb3cgbmV3IEpXVENsYWltVmFsaWRhdGlvbkZhaWxlZCgnXCJpYXRcIiBjbGFpbSBtdXN0IGJlIGEgbnVtYmVyJywgcGF5bG9hZCwgJ2lhdCcsICdpbnZhbGlkJyk7XG4gICAgfVxuICAgIGlmIChwYXlsb2FkLm5iZiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgcGF5bG9hZC5uYmYgIT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgSldUQ2xhaW1WYWxpZGF0aW9uRmFpbGVkKCdcIm5iZlwiIGNsYWltIG11c3QgYmUgYSBudW1iZXInLCBwYXlsb2FkLCAnbmJmJywgJ2ludmFsaWQnKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGF5bG9hZC5uYmYgPiBub3cgKyB0b2xlcmFuY2UpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBKV1RDbGFpbVZhbGlkYXRpb25GYWlsZWQoJ1wibmJmXCIgY2xhaW0gdGltZXN0YW1wIGNoZWNrIGZhaWxlZCcsIHBheWxvYWQsICduYmYnLCAnY2hlY2tfZmFpbGVkJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKHBheWxvYWQuZXhwICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBwYXlsb2FkLmV4cCAhPT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBKV1RDbGFpbVZhbGlkYXRpb25GYWlsZWQoJ1wiZXhwXCIgY2xhaW0gbXVzdCBiZSBhIG51bWJlcicsIHBheWxvYWQsICdleHAnLCAnaW52YWxpZCcpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwYXlsb2FkLmV4cCA8PSBub3cgLSB0b2xlcmFuY2UpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBKV1RFeHBpcmVkKCdcImV4cFwiIGNsYWltIHRpbWVzdGFtcCBjaGVjayBmYWlsZWQnLCBwYXlsb2FkLCAnZXhwJywgJ2NoZWNrX2ZhaWxlZCcpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChtYXhUb2tlbkFnZSkge1xuICAgICAgICBjb25zdCBhZ2UgPSBub3cgLSBwYXlsb2FkLmlhdDtcbiAgICAgICAgY29uc3QgbWF4ID0gdHlwZW9mIG1heFRva2VuQWdlID09PSAnbnVtYmVyJyA/IG1heFRva2VuQWdlIDogc2VjcyhtYXhUb2tlbkFnZSk7XG4gICAgICAgIGlmIChhZ2UgLSB0b2xlcmFuY2UgPiBtYXgpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBKV1RFeHBpcmVkKCdcImlhdFwiIGNsYWltIHRpbWVzdGFtcCBjaGVjayBmYWlsZWQgKHRvbyBmYXIgaW4gdGhlIHBhc3QpJywgcGF5bG9hZCwgJ2lhdCcsICdjaGVja19mYWlsZWQnKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYWdlIDwgMCAtIHRvbGVyYW5jZSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEpXVENsYWltVmFsaWRhdGlvbkZhaWxlZCgnXCJpYXRcIiBjbGFpbSB0aW1lc3RhbXAgY2hlY2sgZmFpbGVkIChpdCBzaG91bGQgYmUgaW4gdGhlIHBhc3QpJywgcGF5bG9hZCwgJ2lhdCcsICdjaGVja19mYWlsZWQnKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcGF5bG9hZDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/private_symbols.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/private_symbols.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unprotected: () => (/* binding */ unprotected)\n/* harmony export */ });\nconst unprotected = Symbol();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9wcml2YXRlX3N5bWJvbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXHByaXZhdGVfc3ltYm9scy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdW5wcm90ZWN0ZWQgPSBTeW1ib2woKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/private_symbols.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/secs.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js":
/*!********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/validate_algorithms.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst validateAlgorithms = (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validateAlgorithms);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi92YWxpZGF0ZV9hbGdvcml0aG1zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsT0FBTztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxrQkFBa0IsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFx2YWxpZGF0ZV9hbGdvcml0aG1zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHZhbGlkYXRlQWxnb3JpdGhtcyA9IChvcHRpb24sIGFsZ29yaXRobXMpID0+IHtcbiAgICBpZiAoYWxnb3JpdGhtcyAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgICghQXJyYXkuaXNBcnJheShhbGdvcml0aG1zKSB8fCBhbGdvcml0aG1zLnNvbWUoKHMpID0+IHR5cGVvZiBzICE9PSAnc3RyaW5nJykpKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYFwiJHtvcHRpb259XCIgb3B0aW9uIG11c3QgYmUgYW4gYXJyYXkgb2Ygc3RyaW5nc2ApO1xuICAgIH1cbiAgICBpZiAoIWFsZ29yaXRobXMpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBTZXQoYWxnb3JpdGhtcyk7XG59O1xuZXhwb3J0IGRlZmF1bHQgdmFsaWRhdGVBbGdvcml0aG1zO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/validate_crit.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validateCrit);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/aeskw.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\nfunction checkKeySize(key, alg) {\n    if (key.symmetricKeySize << 3 !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction ensureKeyObject(key, alg, usage) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        return key;\n    }\n    if (key instanceof Uint8Array) {\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createSecretKey)(key);\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkEncCryptoKey)(key, alg, usage);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_1__.KeyObject.from(key);\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types, 'Uint8Array'));\n}\nconst wrap = (alg, key, cek) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_8__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createCipheriv)(algorithm, keyObject, node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.alloc(8, 0xa6));\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__.concat)(cipher.update(cek), cipher.final());\n};\nconst unwrap = (alg, key, encryptedKey) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_8__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createDecipheriv)(algorithm, keyObject, node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.alloc(8, 0xa6));\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__.concat)(cipher.update(encryptedKey), cipher.final());\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/asn1.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromPKCS8: () => (/* binding */ fromPKCS8),\n/* harmony export */   fromSPKI: () => (/* binding */ fromSPKI),\n/* harmony export */   fromX509: () => (/* binding */ fromX509),\n/* harmony export */   toPKCS8: () => (/* binding */ toPKCS8),\n/* harmony export */   toSPKI: () => (/* binding */ toSPKI)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\nconst genericExport = (keyType, keyFormat, key) => {\n    let keyObject;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(key)) {\n        keyObject = key;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_5__.types));\n    }\n    if (keyObject.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return keyObject.export({ format: 'pem', type: keyFormat });\n};\nconst toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nconst toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst fromPKCS8 = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPrivateKey)({\n    key: node_buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, ''), 'base64'),\n    type: 'pkcs8',\n    format: 'der',\n});\nconst fromSPKI = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({\n    key: node_buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, ''), 'base64'),\n    type: 'spki',\n    format: 'der',\n});\nconst fromX509 = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({\n    key: pem,\n    type: 'spki',\n    format: 'pem',\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/base64url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.decoder.decode(encoded);\n    }\n    return encoded;\n}\nconst encode = (input) => node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString('base64url');\nconst decodeBase64 = (input) => new Uint8Array(node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input, 'base64'));\nconst encodeBase64 = (input) => node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString('base64');\n\nconst decode = (input) => new Uint8Array(node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(normalize(input), 'base64url'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFxQztBQUNZO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix5REFBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsK0NBQU07QUFDekIsK0NBQStDLCtDQUFNO0FBQ3JELGdDQUFnQywrQ0FBTTtBQUMzQjtBQUNYLHlDQUF5QywrQ0FBTSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcYmFzZTY0dXJsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJ1ZmZlciB9IGZyb20gJ25vZGU6YnVmZmVyJztcbmltcG9ydCB7IGRlY29kZXIgfSBmcm9tICcuLi9saWIvYnVmZmVyX3V0aWxzLmpzJztcbmZ1bmN0aW9uIG5vcm1hbGl6ZShpbnB1dCkge1xuICAgIGxldCBlbmNvZGVkID0gaW5wdXQ7XG4gICAgaWYgKGVuY29kZWQgaW5zdGFuY2VvZiBVaW50OEFycmF5KSB7XG4gICAgICAgIGVuY29kZWQgPSBkZWNvZGVyLmRlY29kZShlbmNvZGVkKTtcbiAgICB9XG4gICAgcmV0dXJuIGVuY29kZWQ7XG59XG5jb25zdCBlbmNvZGUgPSAoaW5wdXQpID0+IEJ1ZmZlci5mcm9tKGlucHV0KS50b1N0cmluZygnYmFzZTY0dXJsJyk7XG5leHBvcnQgY29uc3QgZGVjb2RlQmFzZTY0ID0gKGlucHV0KSA9PiBuZXcgVWludDhBcnJheShCdWZmZXIuZnJvbShpbnB1dCwgJ2Jhc2U2NCcpKTtcbmV4cG9ydCBjb25zdCBlbmNvZGVCYXNlNjQgPSAoaW5wdXQpID0+IEJ1ZmZlci5mcm9tKGlucHV0KS50b1N0cmluZygnYmFzZTY0Jyk7XG5leHBvcnQgeyBlbmNvZGUgfTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSAoaW5wdXQpID0+IG5ldyBVaW50OEFycmF5KEJ1ZmZlci5mcm9tKG5vcm1hbGl6ZShpbnB1dCksICdiYXNlNjR1cmwnKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/cbc_tag.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cbcTag)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nfunction cbcTag(aad, iv, ciphertext, macSize, macKey, keySize) {\n    const macData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(aad, iv, ciphertext, (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.uint64be)(aad.length << 3));\n    const hmac = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac)(`sha${macSize}`, macKey);\n    hmac.update(macData);\n    return hmac.digest().slice(0, keySize >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvY2JjX3RhZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUM7QUFDaUI7QUFDM0M7QUFDZixvQkFBb0IsNERBQU0sc0JBQXNCLDhEQUFRO0FBQ3hELGlCQUFpQix1REFBVSxPQUFPLFFBQVE7QUFDMUM7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxjYmNfdGFnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUhtYWMgfSBmcm9tICdub2RlOmNyeXB0byc7XG5pbXBvcnQgeyBjb25jYXQsIHVpbnQ2NGJlIH0gZnJvbSAnLi4vbGliL2J1ZmZlcl91dGlscy5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjYmNUYWcoYWFkLCBpdiwgY2lwaGVydGV4dCwgbWFjU2l6ZSwgbWFjS2V5LCBrZXlTaXplKSB7XG4gICAgY29uc3QgbWFjRGF0YSA9IGNvbmNhdChhYWQsIGl2LCBjaXBoZXJ0ZXh0LCB1aW50NjRiZShhYWQubGVuZ3RoIDw8IDMpKTtcbiAgICBjb25zdCBobWFjID0gY3JlYXRlSG1hYyhgc2hhJHttYWNTaXplfWAsIG1hY0tleSk7XG4gICAgaG1hYy51cGRhdGUobWFjRGF0YSk7XG4gICAgcmV0dXJuIGhtYWMuZGlnZXN0KCkuc2xpY2UoMCwga2V5U2l6ZSA+PiAzKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/check_cek_length.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n\n\nconst checkCekLength = (enc, cek) => {\n    let expected;\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            expected = parseInt(enc.slice(-3), 10);\n            break;\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            expected = parseInt(enc.slice(1, 4), 10);\n            break;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Content Encryption Algorithm ${enc} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (cek instanceof Uint8Array) {\n        const actual = cek.byteLength << 3;\n        if (actual !== expected) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek) && cek.type === 'secret') {\n        const actual = cek.symmetricKeySize << 3;\n        if (actual !== expected) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    throw new TypeError('Invalid Content Encryption Key type');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkCekLength);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/check_key_length.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key, alg) => {\n    let modulusLength;\n    try {\n        if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n            modulusLength = key.asymmetricKeyDetails?.modulusLength;\n        }\n        else {\n            modulusLength = Buffer.from(key.n, 'base64url').byteLength << 3;\n        }\n    }\n    catch { }\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvY2hlY2tfa2V5X2xlbmd0aC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUN4QyxpRUFBZTtBQUNmO0FBQ0E7QUFDQSwyQkFBMkIsa0RBQVM7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixLQUFLO0FBQ3BDO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxjaGVja19rZXlfbGVuZ3RoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEtleU9iamVjdCB9IGZyb20gJ25vZGU6Y3J5cHRvJztcbmV4cG9ydCBkZWZhdWx0IChrZXksIGFsZykgPT4ge1xuICAgIGxldCBtb2R1bHVzTGVuZ3RoO1xuICAgIHRyeSB7XG4gICAgICAgIGlmIChrZXkgaW5zdGFuY2VvZiBLZXlPYmplY3QpIHtcbiAgICAgICAgICAgIG1vZHVsdXNMZW5ndGggPSBrZXkuYXN5bW1ldHJpY0tleURldGFpbHM/Lm1vZHVsdXNMZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBtb2R1bHVzTGVuZ3RoID0gQnVmZmVyLmZyb20oa2V5Lm4sICdiYXNlNjR1cmwnKS5ieXRlTGVuZ3RoIDw8IDM7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2F0Y2ggeyB9XG4gICAgaWYgKHR5cGVvZiBtb2R1bHVzTGVuZ3RoICE9PSAnbnVtYmVyJyB8fCBtb2R1bHVzTGVuZ3RoIDwgMjA0OCkge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGAke2FsZ30gcmVxdWlyZXMga2V5IG1vZHVsdXNMZW5ndGggdG8gYmUgMjA0OCBiaXRzIG9yIGxhcmdlcmApO1xuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/ciphers.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nlet ciphers;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((algorithm) => {\n    ciphers ||= new Set((0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.getCiphers)());\n    return ciphers.has(algorithm);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvY2lwaGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QztBQUN6QztBQUNBLGlFQUFlO0FBQ2Ysd0JBQXdCLHVEQUFVO0FBQ2xDO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxjaXBoZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldENpcGhlcnMgfSBmcm9tICdub2RlOmNyeXB0byc7XG5sZXQgY2lwaGVycztcbmV4cG9ydCBkZWZhdWx0IChhbGdvcml0aG0pID0+IHtcbiAgICBjaXBoZXJzIHx8PSBuZXcgU2V0KGdldENpcGhlcnMoKSk7XG4gICAgcmV0dXJuIGNpcGhlcnMuaGFzKGFsZ29yaXRobSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/decrypt.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../lib/check_iv_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _timing_safe_equal_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./timing_safe_equal.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js\");\n/* harmony import */ var _cbc_tag_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cbc_tag.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const macSize = parseInt(enc.slice(-3), 10);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const expectedTag = (0,_cbc_tag_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(aad, iv, ciphertext, macSize, macKey, keySize);\n    let macCheckPassed;\n    try {\n        macCheckPassed = (0,_timing_safe_equal_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        const decipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipheriv)(algorithm, encKey, iv);\n        plaintext = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_6__.concat)(decipher.update(ciphertext), decipher.final());\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nfunction gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    try {\n        const decipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n        decipher.setAuthTag(tag);\n        if (aad.byteLength) {\n            decipher.setAAD(aad, { plaintextLength: ciphertext.length });\n        }\n        const plaintext = decipher.update(ciphertext);\n        decipher.final();\n        return plaintext;\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n}\nconst decrypt = (enc, cek, ciphertext, iv, tag, aad) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_7__.isCryptoKey)(cek)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_8__.checkEncCryptoKey)(cek, enc, 'decrypt');\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(cek, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_10__.types, 'Uint8Array'));\n    }\n    if (!iv) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEInvalid('JWE Authentication Tag missing');\n    }\n    (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, key);\n    (0,_lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcDecrypt(enc, key, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmDecrypt(enc, key, ciphertext, iv, tag, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decrypt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/digest.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst digest = (algorithm, data) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(algorithm).update(data).digest();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (digest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlDO0FBQ3pDLG9DQUFvQyx1REFBVTtBQUM5QyxpRUFBZSxNQUFNLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGRpZ2VzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVIYXNoIH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuY29uc3QgZGlnZXN0ID0gKGFsZ29yaXRobSwgZGF0YSkgPT4gY3JlYXRlSGFzaChhbGdvcml0aG0pLnVwZGF0ZShkYXRhKS5kaWdlc3QoKTtcbmV4cG9ydCBkZWZhdWx0IGRpZ2VzdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/dsa_digest.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/dsa_digest.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ dsaDigest)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction dsaDigest(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'RS256':\n        case 'ES256':\n        case 'ES256K':\n            return 'sha256';\n        case 'PS384':\n        case 'RS384':\n        case 'ES384':\n            return 'sha384';\n        case 'PS512':\n        case 'RS512':\n        case 'ES512':\n            return 'sha512';\n        case 'Ed25519':\n        case 'EdDSA':\n            return undefined;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZHNhX2RpZ2VzdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUN0QztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0IsUUFBUSxLQUFLO0FBQ25EO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGRzYV9kaWdlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSk9TRU5vdFN1cHBvcnRlZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRzYURpZ2VzdChhbGcpIHtcbiAgICBzd2l0Y2ggKGFsZykge1xuICAgICAgICBjYXNlICdQUzI1Nic6XG4gICAgICAgIGNhc2UgJ1JTMjU2JzpcbiAgICAgICAgY2FzZSAnRVMyNTYnOlxuICAgICAgICBjYXNlICdFUzI1NksnOlxuICAgICAgICAgICAgcmV0dXJuICdzaGEyNTYnO1xuICAgICAgICBjYXNlICdQUzM4NCc6XG4gICAgICAgIGNhc2UgJ1JTMzg0JzpcbiAgICAgICAgY2FzZSAnRVMzODQnOlxuICAgICAgICAgICAgcmV0dXJuICdzaGEzODQnO1xuICAgICAgICBjYXNlICdQUzUxMic6XG4gICAgICAgIGNhc2UgJ1JTNTEyJzpcbiAgICAgICAgY2FzZSAnRVM1MTInOlxuICAgICAgICAgICAgcmV0dXJuICdzaGE1MTInO1xuICAgICAgICBjYXNlICdFZDI1NTE5JzpcbiAgICAgICAgY2FzZSAnRWREU0EnOlxuICAgICAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKGBhbGcgJHthbGd9IGlzIG5vdCBzdXBwb3J0ZWQgZWl0aGVyIGJ5IEpPU0Ugb3IgeW91ciBqYXZhc2NyaXB0IHJ1bnRpbWVgKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/dsa_digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/ecdhes.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deriveKey: () => (/* binding */ deriveKey),\n/* harmony export */   ecdhAllowed: () => (/* binding */ ecdhAllowed),\n/* harmony export */   generateEpk: () => (/* binding */ generateEpk)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./get_named_curve.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\nconst generateKeyPair = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.generateKeyPair);\nasync function deriveKey(publicKee, privateKee, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    let publicKey;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(publicKee)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(publicKee, 'ECDH');\n        publicKey = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(publicKee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(publicKee)) {\n        publicKey = publicKee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(publicKee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    let privateKey;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(privateKee)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(privateKee, 'ECDH', 'deriveBits');\n        privateKey = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(privateKee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(privateKee)) {\n        privateKey = privateKee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(privateKee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    const value = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concat)((0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode(algorithm)), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(apu), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(apv), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.uint32be)(keyLength));\n    const sharedSecret = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.diffieHellman)({ privateKey, publicKey });\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concatKdf)(sharedSecret, keyLength, value);\n}\nasync function generateEpk(kee) {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(kee)) {\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(kee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(kee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    switch (key.asymmetricKeyType) {\n        case 'x25519':\n            return generateKeyPair('x25519');\n        case 'x448': {\n            return generateKeyPair('x448');\n        }\n        case 'ec': {\n            const namedCurve = (0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key);\n            return generateKeyPair('ec', { namedCurve });\n        }\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_9__.JOSENotSupported('Invalid or unsupported EPK');\n    }\n}\nconst ecdhAllowed = (key) => ['P-256', 'P-384', 'P-521', 'X25519', 'X448'].includes((0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/encrypt.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/check_iv_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _cbc_tag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cbc_tag.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _lib_iv_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../lib/iv.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createCipheriv)(algorithm, encKey, iv);\n    const ciphertext = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.concat)(cipher.update(plaintext), cipher.final());\n    const macSize = parseInt(enc.slice(-3), 10);\n    const tag = (0,_cbc_tag_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(aad, iv, ciphertext, macSize, macKey, keySize);\n    return { ciphertext, tag, iv };\n}\nfunction gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createCipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n    if (aad.byteLength) {\n        cipher.setAAD(aad, { plaintextLength: plaintext.length });\n    }\n    const ciphertext = cipher.update(plaintext);\n    cipher.final();\n    const tag = cipher.getAuthTag();\n    return { ciphertext, tag, iv };\n}\nconst encrypt = (enc, plaintext, cek, iv, aad) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_6__.isCryptoKey)(cek)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_7__.checkEncCryptoKey)(cek, enc, 'encrypt');\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(cek, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_9__.types, 'Uint8Array'));\n    }\n    (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(enc, key);\n    if (iv) {\n        (0,_lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, iv);\n    }\n    else {\n        iv = (0,_lib_iv_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcEncrypt(enc, plaintext, key, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmEncrypt(enc, plaintext, key, iv, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encrypt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZW5jcnlwdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUF3RDtBQUNGO0FBQ0g7QUFDSDtBQUNkO0FBQ1c7QUFDWTtBQUNaO0FBQ2E7QUFDcEI7QUFDZTtBQUNoQjtBQUNJO0FBQ3pDO0FBQ0E7QUFDQSxRQUFRLDZEQUFXO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLFFBQVE7QUFDckMsU0FBUyx1REFBUztBQUNsQixrQkFBa0IsNkRBQWdCLFFBQVEsS0FBSztBQUMvQztBQUNBLG1CQUFtQiwyREFBYztBQUNqQyx1QkFBdUIsNERBQU07QUFDN0I7QUFDQSxnQkFBZ0IsdURBQU07QUFDdEIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixRQUFRO0FBQ3JDLFNBQVMsdURBQVM7QUFDbEIsa0JBQWtCLDZEQUFnQixRQUFRLEtBQUs7QUFDL0M7QUFDQSxtQkFBbUIsMkRBQWMsdUJBQXVCLG1CQUFtQjtBQUMzRTtBQUNBLDZCQUE2QixtQ0FBbUM7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsUUFBUSwwREFBVztBQUNuQixRQUFRLHFFQUFpQjtBQUN6QixjQUFjLGtEQUFTO0FBQ3ZCO0FBQ0EsMENBQTBDLDZEQUFXO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixxRUFBZSxTQUFTLGtEQUFLO0FBQ3pEO0FBQ0EsSUFBSSxpRUFBYztBQUNsQjtBQUNBLFFBQVEsb0VBQWE7QUFDckI7QUFDQTtBQUNBLGFBQWEsdURBQVU7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0I7QUFDdEM7QUFDQTtBQUNBLGlFQUFlLE9BQU8sRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcZW5jcnlwdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDaXBoZXJpdiwgS2V5T2JqZWN0IH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuaW1wb3J0IGNoZWNrSXZMZW5ndGggZnJvbSAnLi4vbGliL2NoZWNrX2l2X2xlbmd0aC5qcyc7XG5pbXBvcnQgY2hlY2tDZWtMZW5ndGggZnJvbSAnLi9jaGVja19jZWtfbGVuZ3RoLmpzJztcbmltcG9ydCB7IGNvbmNhdCB9IGZyb20gJy4uL2xpYi9idWZmZXJfdXRpbHMuanMnO1xuaW1wb3J0IGNiY1RhZyBmcm9tICcuL2NiY190YWcuanMnO1xuaW1wb3J0IHsgaXNDcnlwdG9LZXkgfSBmcm9tICcuL3dlYmNyeXB0by5qcyc7XG5pbXBvcnQgeyBjaGVja0VuY0NyeXB0b0tleSB9IGZyb20gJy4uL2xpYi9jcnlwdG9fa2V5LmpzJztcbmltcG9ydCBpc0tleU9iamVjdCBmcm9tICcuL2lzX2tleV9vYmplY3QuanMnO1xuaW1wb3J0IGludmFsaWRLZXlJbnB1dCBmcm9tICcuLi9saWIvaW52YWxpZF9rZXlfaW5wdXQuanMnO1xuaW1wb3J0IGdlbmVyYXRlSXYgZnJvbSAnLi4vbGliL2l2LmpzJztcbmltcG9ydCB7IEpPU0VOb3RTdXBwb3J0ZWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgc3VwcG9ydGVkIGZyb20gJy4vY2lwaGVycy5qcyc7XG5pbXBvcnQgeyB0eXBlcyB9IGZyb20gJy4vaXNfa2V5X2xpa2UuanMnO1xuZnVuY3Rpb24gY2JjRW5jcnlwdChlbmMsIHBsYWludGV4dCwgY2VrLCBpdiwgYWFkKSB7XG4gICAgY29uc3Qga2V5U2l6ZSA9IHBhcnNlSW50KGVuYy5zbGljZSgxLCA0KSwgMTApO1xuICAgIGlmIChpc0tleU9iamVjdChjZWspKSB7XG4gICAgICAgIGNlayA9IGNlay5leHBvcnQoKTtcbiAgICB9XG4gICAgY29uc3QgZW5jS2V5ID0gY2VrLnN1YmFycmF5KGtleVNpemUgPj4gMyk7XG4gICAgY29uc3QgbWFjS2V5ID0gY2VrLnN1YmFycmF5KDAsIGtleVNpemUgPj4gMyk7XG4gICAgY29uc3QgYWxnb3JpdGhtID0gYGFlcy0ke2tleVNpemV9LWNiY2A7XG4gICAgaWYgKCFzdXBwb3J0ZWQoYWxnb3JpdGhtKSkge1xuICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZChgYWxnICR7ZW5jfSBpcyBub3Qgc3VwcG9ydGVkIGJ5IHlvdXIgamF2YXNjcmlwdCBydW50aW1lYCk7XG4gICAgfVxuICAgIGNvbnN0IGNpcGhlciA9IGNyZWF0ZUNpcGhlcml2KGFsZ29yaXRobSwgZW5jS2V5LCBpdik7XG4gICAgY29uc3QgY2lwaGVydGV4dCA9IGNvbmNhdChjaXBoZXIudXBkYXRlKHBsYWludGV4dCksIGNpcGhlci5maW5hbCgpKTtcbiAgICBjb25zdCBtYWNTaXplID0gcGFyc2VJbnQoZW5jLnNsaWNlKC0zKSwgMTApO1xuICAgIGNvbnN0IHRhZyA9IGNiY1RhZyhhYWQsIGl2LCBjaXBoZXJ0ZXh0LCBtYWNTaXplLCBtYWNLZXksIGtleVNpemUpO1xuICAgIHJldHVybiB7IGNpcGhlcnRleHQsIHRhZywgaXYgfTtcbn1cbmZ1bmN0aW9uIGdjbUVuY3J5cHQoZW5jLCBwbGFpbnRleHQsIGNlaywgaXYsIGFhZCkge1xuICAgIGNvbnN0IGtleVNpemUgPSBwYXJzZUludChlbmMuc2xpY2UoMSwgNCksIDEwKTtcbiAgICBjb25zdCBhbGdvcml0aG0gPSBgYWVzLSR7a2V5U2l6ZX0tZ2NtYDtcbiAgICBpZiAoIXN1cHBvcnRlZChhbGdvcml0aG0pKSB7XG4gICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKGBhbGcgJHtlbmN9IGlzIG5vdCBzdXBwb3J0ZWQgYnkgeW91ciBqYXZhc2NyaXB0IHJ1bnRpbWVgKTtcbiAgICB9XG4gICAgY29uc3QgY2lwaGVyID0gY3JlYXRlQ2lwaGVyaXYoYWxnb3JpdGhtLCBjZWssIGl2LCB7IGF1dGhUYWdMZW5ndGg6IDE2IH0pO1xuICAgIGlmIChhYWQuYnl0ZUxlbmd0aCkge1xuICAgICAgICBjaXBoZXIuc2V0QUFEKGFhZCwgeyBwbGFpbnRleHRMZW5ndGg6IHBsYWludGV4dC5sZW5ndGggfSk7XG4gICAgfVxuICAgIGNvbnN0IGNpcGhlcnRleHQgPSBjaXBoZXIudXBkYXRlKHBsYWludGV4dCk7XG4gICAgY2lwaGVyLmZpbmFsKCk7XG4gICAgY29uc3QgdGFnID0gY2lwaGVyLmdldEF1dGhUYWcoKTtcbiAgICByZXR1cm4geyBjaXBoZXJ0ZXh0LCB0YWcsIGl2IH07XG59XG5jb25zdCBlbmNyeXB0ID0gKGVuYywgcGxhaW50ZXh0LCBjZWssIGl2LCBhYWQpID0+IHtcbiAgICBsZXQga2V5O1xuICAgIGlmIChpc0NyeXB0b0tleShjZWspKSB7XG4gICAgICAgIGNoZWNrRW5jQ3J5cHRvS2V5KGNlaywgZW5jLCAnZW5jcnlwdCcpO1xuICAgICAgICBrZXkgPSBLZXlPYmplY3QuZnJvbShjZWspO1xuICAgIH1cbiAgICBlbHNlIGlmIChjZWsgaW5zdGFuY2VvZiBVaW50OEFycmF5IHx8IGlzS2V5T2JqZWN0KGNlaykpIHtcbiAgICAgICAga2V5ID0gY2VrO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihpbnZhbGlkS2V5SW5wdXQoY2VrLCAuLi50eXBlcywgJ1VpbnQ4QXJyYXknKSk7XG4gICAgfVxuICAgIGNoZWNrQ2VrTGVuZ3RoKGVuYywga2V5KTtcbiAgICBpZiAoaXYpIHtcbiAgICAgICAgY2hlY2tJdkxlbmd0aChlbmMsIGl2KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGl2ID0gZ2VuZXJhdGVJdihlbmMpO1xuICAgIH1cbiAgICBzd2l0Y2ggKGVuYykge1xuICAgICAgICBjYXNlICdBMTI4Q0JDLUhTMjU2JzpcbiAgICAgICAgY2FzZSAnQTE5MkNCQy1IUzM4NCc6XG4gICAgICAgIGNhc2UgJ0EyNTZDQkMtSFM1MTInOlxuICAgICAgICAgICAgcmV0dXJuIGNiY0VuY3J5cHQoZW5jLCBwbGFpbnRleHQsIGtleSwgaXYsIGFhZCk7XG4gICAgICAgIGNhc2UgJ0ExMjhHQ00nOlxuICAgICAgICBjYXNlICdBMTkyR0NNJzpcbiAgICAgICAgY2FzZSAnQTI1NkdDTSc6XG4gICAgICAgICAgICByZXR1cm4gZ2NtRW5jcnlwdChlbmMsIHBsYWludGV4dCwga2V5LCBpdiwgYWFkKTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKCdVbnN1cHBvcnRlZCBKV0UgQ29udGVudCBFbmNyeXB0aW9uIEFsZ29yaXRobScpO1xuICAgIH1cbn07XG5leHBvcnQgZGVmYXVsdCBlbmNyeXB0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/fetch_jwks.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/fetch_jwks.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:http */ \"node:http\");\n/* harmony import */ var node_https__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:https */ \"node:https\");\n/* harmony import */ var node_events__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:events */ \"node:events\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\n\n\n\nconst fetchJwks = async (url, timeout, options) => {\n    let get;\n    switch (url.protocol) {\n        case 'https:':\n            get = node_https__WEBPACK_IMPORTED_MODULE_1__.get;\n            break;\n        case 'http:':\n            get = node_http__WEBPACK_IMPORTED_MODULE_0__.get;\n            break;\n        default:\n            throw new TypeError('Unsupported URL protocol.');\n    }\n    const { agent, headers } = options;\n    const req = get(url.href, {\n        agent,\n        timeout,\n        headers,\n    });\n    const [response] = (await Promise.race([(0,node_events__WEBPACK_IMPORTED_MODULE_2__.once)(req, 'response'), (0,node_events__WEBPACK_IMPORTED_MODULE_2__.once)(req, 'timeout')]));\n    if (!response) {\n        req.destroy();\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWKSTimeout();\n    }\n    if (response.statusCode !== 200) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSEError('Expected 200 OK from the JSON Web Key Set HTTP response');\n    }\n    const parts = [];\n    for await (const part of response) {\n        parts.push(part);\n    }\n    try {\n        return JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.decoder.decode((0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.concat)(...parts)));\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSEError('Failed to parse the JSON Web Key Set HTTP response as JSON');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (fetchJwks);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/fetch_jwks.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js":
/*!********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/get_named_curve.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   weakMap: () => (/* binding */ weakMap)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/is_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\n\n\n\n\nconst weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(kee)) {\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(kee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(kee)) {\n        key = kee;\n    }\n    else if ((0,_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_4__.isJWK)(kee)) {\n        return kee.crv;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(kee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            const namedCurve = key.asymmetricKeyDetails.namedCurve;\n            if (raw) {\n                return namedCurve;\n            }\n            return namedCurveToJOSE(namedCurve);\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getNamedCurve);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js":
/*!************************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getSignVerifyKey)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/is_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\n\n\n\nfunction getSignVerifyKey(alg, key, usage) {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.types));\n        }\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createSecretKey)(key);\n    }\n    if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkSigCryptoKey)(key, alg, usage);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    if (_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_5__.isJWK(key)) {\n        if (alg.startsWith('HS')) {\n            return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createSecretKey)(Buffer.from(key.k, 'base64url'));\n        }\n        return key;\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.types, 'Uint8Array', 'JSON Web Key'));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/hmac_digest.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/hmac_digest.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ hmacDigest)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction hmacDigest(alg) {\n    switch (alg) {\n        case 'HS256':\n            return 'sha256';\n        case 'HS384':\n            return 'sha384';\n        case 'HS512':\n            return 'sha512';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvaG1hY19kaWdlc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFDdEM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQixRQUFRLEtBQUs7QUFDbkQ7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcaG1hY19kaWdlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSk9TRU5vdFN1cHBvcnRlZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGhtYWNEaWdlc3QoYWxnKSB7XG4gICAgc3dpdGNoIChhbGcpIHtcbiAgICAgICAgY2FzZSAnSFMyNTYnOlxuICAgICAgICAgICAgcmV0dXJuICdzaGEyNTYnO1xuICAgICAgICBjYXNlICdIUzM4NCc6XG4gICAgICAgICAgICByZXR1cm4gJ3NoYTM4NCc7XG4gICAgICAgIGNhc2UgJ0hTNTEyJzpcbiAgICAgICAgICAgIHJldHVybiAnc2hhNTEyJztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKGBhbGcgJHthbGd9IGlzIG5vdCBzdXBwb3J0ZWQgZWl0aGVyIGJ5IEpPU0Ugb3IgeW91ciBqYXZhc2NyaXB0IHJ1bnRpbWVgKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/hmac_digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/is_key_like.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key) => (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) || (0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key));\nconst types = ['KeyObject'];\nif (globalThis.CryptoKey || _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]?.CryptoKey) {\n    types.push('CryptoKey');\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvaXNfa2V5X2xpa2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3RDtBQUNYO0FBQzdDLGlFQUFlLFNBQVMsNkRBQVcsU0FBUywwREFBVyxLQUFLLEVBQUM7QUFDN0Q7QUFDQSw0QkFBNEIscURBQVM7QUFDckM7QUFDQTtBQUNpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcaXNfa2V5X2xpa2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHdlYmNyeXB0bywgeyBpc0NyeXB0b0tleSB9IGZyb20gJy4vd2ViY3J5cHRvLmpzJztcbmltcG9ydCBpc0tleU9iamVjdCBmcm9tICcuL2lzX2tleV9vYmplY3QuanMnO1xuZXhwb3J0IGRlZmF1bHQgKGtleSkgPT4gaXNLZXlPYmplY3Qoa2V5KSB8fCBpc0NyeXB0b0tleShrZXkpO1xuY29uc3QgdHlwZXMgPSBbJ0tleU9iamVjdCddO1xuaWYgKGdsb2JhbFRoaXMuQ3J5cHRvS2V5IHx8IHdlYmNyeXB0bz8uQ3J5cHRvS2V5KSB7XG4gICAgdHlwZXMucHVzaCgnQ3J5cHRvS2V5Jyk7XG59XG5leHBvcnQgeyB0eXBlcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/is_key_object.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:util */ \"node:util\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((obj) => node_util__WEBPACK_IMPORTED_MODULE_0__.types.isKeyObject(obj));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvaXNfa2V5X29iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQztBQUNsQyxpRUFBZSxTQUFTLDRDQUFVLGlCQUFpQixFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxpc19rZXlfb2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIHV0aWwgZnJvbSAnbm9kZTp1dGlsJztcbmV4cG9ydCBkZWZhdWx0IChvYmopID0+IHV0aWwudHlwZXMuaXNLZXlPYmplY3Qob2JqKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst parse = (key) => {\n    if (key.d) {\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPrivateKey)({ format: 'jwk', key });\n    }\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({ format: 'jwk', key });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parse);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvandrX3RvX2tleS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRTtBQUNoRTtBQUNBO0FBQ0EsZUFBZSw2REFBZ0IsR0FBRyxvQkFBb0I7QUFDdEQ7QUFDQSxXQUFXLDREQUFlLEdBQUcsb0JBQW9CO0FBQ2pEO0FBQ0EsaUVBQWUsS0FBSyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxqd2tfdG9fa2V5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVByaXZhdGVLZXksIGNyZWF0ZVB1YmxpY0tleSB9IGZyb20gJ25vZGU6Y3J5cHRvJztcbmNvbnN0IHBhcnNlID0gKGtleSkgPT4ge1xuICAgIGlmIChrZXkuZCkge1xuICAgICAgICByZXR1cm4gY3JlYXRlUHJpdmF0ZUtleSh7IGZvcm1hdDogJ2p3aycsIGtleSB9KTtcbiAgICB9XG4gICAgcmV0dXJuIGNyZWF0ZVB1YmxpY0tleSh7IGZvcm1hdDogJ2p3aycsIGtleSB9KTtcbn07XG5leHBvcnQgZGVmYXVsdCBwYXJzZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\nconst keyToJWK = (key) => {\n    let keyObject;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        keyObject = key;\n    }\n    else if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: (0,_base64url_js__WEBPACK_IMPORTED_MODULE_3__.encode)(key),\n        };\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_5__.types, 'Uint8Array'));\n    }\n    if (keyObject.type !== 'secret' &&\n        !['rsa', 'ec', 'ed25519', 'x25519', 'ed448', 'x448'].includes(keyObject.asymmetricKeyType)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_6__.JOSENotSupported('Unsupported key asymmetricKeyType');\n    }\n    return keyObject.export({ format: 'jwk' });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (keyToJWK);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUva2V5X3RvX2p3ay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF3QztBQUNhO0FBQ0E7QUFDUjtBQUNBO0FBQ2E7QUFDakI7QUFDekM7QUFDQTtBQUNBLFFBQVEsMERBQVc7QUFDbkI7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtEQUFTO0FBQzdCO0FBQ0EsYUFBYSw2REFBVztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxxREFBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIscUVBQWUsU0FBUyxrREFBSztBQUN6RDtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNkRBQWdCO0FBQ2xDO0FBQ0EsOEJBQThCLGVBQWU7QUFDN0M7QUFDQSxpRUFBZSxRQUFRLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGtleV90b19qd2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgS2V5T2JqZWN0IH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuaW1wb3J0IHsgZW5jb2RlIGFzIGJhc2U2NHVybCB9IGZyb20gJy4vYmFzZTY0dXJsLmpzJztcbmltcG9ydCB7IEpPU0VOb3RTdXBwb3J0ZWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgeyBpc0NyeXB0b0tleSB9IGZyb20gJy4vd2ViY3J5cHRvLmpzJztcbmltcG9ydCBpc0tleU9iamVjdCBmcm9tICcuL2lzX2tleV9vYmplY3QuanMnO1xuaW1wb3J0IGludmFsaWRLZXlJbnB1dCBmcm9tICcuLi9saWIvaW52YWxpZF9rZXlfaW5wdXQuanMnO1xuaW1wb3J0IHsgdHlwZXMgfSBmcm9tICcuL2lzX2tleV9saWtlLmpzJztcbmNvbnN0IGtleVRvSldLID0gKGtleSkgPT4ge1xuICAgIGxldCBrZXlPYmplY3Q7XG4gICAgaWYgKGlzQ3J5cHRvS2V5KGtleSkpIHtcbiAgICAgICAgaWYgKCFrZXkuZXh0cmFjdGFibGUpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0NyeXB0b0tleSBpcyBub3QgZXh0cmFjdGFibGUnKTtcbiAgICAgICAgfVxuICAgICAgICBrZXlPYmplY3QgPSBLZXlPYmplY3QuZnJvbShrZXkpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc0tleU9iamVjdChrZXkpKSB7XG4gICAgICAgIGtleU9iamVjdCA9IGtleTtcbiAgICB9XG4gICAgZWxzZSBpZiAoa2V5IGluc3RhbmNlb2YgVWludDhBcnJheSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAga3R5OiAnb2N0JyxcbiAgICAgICAgICAgIGs6IGJhc2U2NHVybChrZXkpLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihpbnZhbGlkS2V5SW5wdXQoa2V5LCAuLi50eXBlcywgJ1VpbnQ4QXJyYXknKSk7XG4gICAgfVxuICAgIGlmIChrZXlPYmplY3QudHlwZSAhPT0gJ3NlY3JldCcgJiZcbiAgICAgICAgIVsncnNhJywgJ2VjJywgJ2VkMjU1MTknLCAneDI1NTE5JywgJ2VkNDQ4JywgJ3g0NDgnXS5pbmNsdWRlcyhrZXlPYmplY3QuYXN5bW1ldHJpY0tleVR5cGUpKSB7XG4gICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKCdVbnN1cHBvcnRlZCBrZXkgYXN5bW1ldHJpY0tleVR5cGUnKTtcbiAgICB9XG4gICAgcmV0dXJuIGtleU9iamVjdC5leHBvcnQoeyBmb3JtYXQ6ICdqd2snIH0pO1xufTtcbmV4cG9ydCBkZWZhdWx0IGtleVRvSldLO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/node_key.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/node_key.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ keyForCrypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _get_named_curve_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get_named_curve.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _check_key_length_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./check_key_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js\");\n\n\n\n\nconst ecCurveAlgMap = new Map([\n    ['ES256', 'P-256'],\n    ['ES256K', 'secp256k1'],\n    ['ES384', 'P-384'],\n    ['ES512', 'P-521'],\n]);\nfunction keyForCrypto(alg, key) {\n    let asymmetricKeyType;\n    let asymmetricKeyDetails;\n    let isJWK;\n    if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n        asymmetricKeyType = key.asymmetricKeyType;\n        asymmetricKeyDetails = key.asymmetricKeyDetails;\n    }\n    else {\n        isJWK = true;\n        switch (key.kty) {\n            case 'RSA':\n                asymmetricKeyType = 'rsa';\n                break;\n            case 'EC':\n                asymmetricKeyType = 'ec';\n                break;\n            case 'OKP': {\n                if (key.crv === 'Ed25519') {\n                    asymmetricKeyType = 'ed25519';\n                    break;\n                }\n                if (key.crv === 'Ed448') {\n                    asymmetricKeyType = 'ed448';\n                    break;\n                }\n                throw new TypeError('Invalid key for this operation, its crv must be Ed25519 or Ed448');\n            }\n            default:\n                throw new TypeError('Invalid key for this operation, its kty must be RSA, OKP, or EC');\n        }\n    }\n    let options;\n    switch (alg) {\n        case 'Ed25519':\n            if (asymmetricKeyType !== 'ed25519') {\n                throw new TypeError(`Invalid key for this operation, its asymmetricKeyType must be ed25519`);\n            }\n            break;\n        case 'EdDSA':\n            if (!['ed25519', 'ed448'].includes(asymmetricKeyType)) {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448');\n            }\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, alg);\n            break;\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            if (asymmetricKeyType === 'rsa-pss') {\n                const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = asymmetricKeyDetails;\n                const length = parseInt(alg.slice(-3), 10);\n                if (hashAlgorithm !== undefined &&\n                    (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm)) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${alg}`);\n                }\n                if (saltLength !== undefined && saltLength > length >> 3) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${alg}`);\n                }\n            }\n            else if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss');\n            }\n            (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, alg);\n            options = {\n                padding: node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_PSS_PADDING,\n                saltLength: node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PSS_SALTLEN_DIGEST,\n            };\n            break;\n        case 'ES256':\n        case 'ES256K':\n        case 'ES384':\n        case 'ES512': {\n            if (asymmetricKeyType !== 'ec') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ec');\n            }\n            const actual = (0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key);\n            const expected = ecCurveAlgMap.get(alg);\n            if (actual !== expected) {\n                throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${expected}, got ${actual}`);\n            }\n            options = { dsaEncoding: 'ieee-p1363' };\n            break;\n        }\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (isJWK) {\n        return { format: 'jwk', key, ...options };\n    }\n    return options ? { ...options, key } : key;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/node_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/normalize_key.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvbm9ybWFsaXplX2tleS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsRUFBRSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaC1hZG1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxub3JtYWxpemVfa2V5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/pbes2kw.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./random.js */ \"node:crypto\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _aeskw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./aeskw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/check_p2s.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_p2s.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst pbkdf2 = (0,node_util__WEBPACK_IMPORTED_MODULE_0__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_1__.pbkdf2);\nfunction getPassword(key, alg) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        return key.export();\n    }\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkEncCryptoKey)(key, alg, 'deriveBits', 'deriveKey');\n        return node_crypto__WEBPACK_IMPORTED_MODULE_1__.KeyObject.from(key).export();\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types, 'Uint8Array'));\n}\nconst encrypt = async (alg, key, cek, p2c = 2048, p2s = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(16))) => {\n    (0,_lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(p2s);\n    const salt = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    const encryptedKey = await (0,_aeskw_js__WEBPACK_IMPORTED_MODULE_9__.wrap)(alg.slice(-6), derivedKey, cek);\n    return { encryptedKey, p2c, p2s: (0,_base64url_js__WEBPACK_IMPORTED_MODULE_10__.encode)(p2s) };\n};\nconst decrypt = async (alg, key, encryptedKey, p2c, p2s) => {\n    (0,_lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(p2s);\n    const salt = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    return (0,_aeskw_js__WEBPACK_IMPORTED_MODULE_9__.unwrap)(alg.slice(-6), derivedKey, encryptedKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/rsaes.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _check_key_length_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./check_key_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\nconst checkKey = (key, alg) => {\n    if (key.asymmetricKeyType !== 'rsa') {\n        throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n    }\n    (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key, alg);\n};\nconst RSA1_5 = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.deprecate)(() => node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_PADDING, 'The RSA1_5 \"alg\" (JWE Algorithm) is deprecated and will be removed in the next major revision.');\nconst resolvePadding = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_OAEP_PADDING;\n        case 'RSA1_5':\n            return RSA1_5();\n        default:\n            return undefined;\n    }\n};\nconst resolveOaepHash = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n            return 'sha1';\n        case 'RSA-OAEP-256':\n            return 'sha256';\n        case 'RSA-OAEP-384':\n            return 'sha384';\n        case 'RSA-OAEP-512':\n            return 'sha512';\n        default:\n            return undefined;\n    }\n};\nfunction ensureKeyObject(key, alg, ...usages) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(key)) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_4__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_5__.checkEncCryptoKey)(key, alg, ...usages);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_7__.types));\n}\nconst encrypt = (alg, key, cek) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey', 'encrypt');\n    checkKey(keyObject, alg);\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.publicEncrypt)({ key: keyObject, oaepHash, padding }, cek);\n};\nconst decrypt = (alg, key, encryptedKey) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey', 'decrypt');\n    checkKey(keyObject, alg);\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.privateDecrypt)({ key: keyObject, oaepHash, padding }, encryptedKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/sign.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/sign.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dsa_digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/dsa_digest.js\");\n/* harmony import */ var _hmac_digest_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hmac_digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/hmac_digest.js\");\n/* harmony import */ var _node_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/node_key.js\");\n/* harmony import */ var _get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get_sign_verify_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js\");\n\n\n\n\n\n\nconst oneShotSign = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.sign);\nconst sign = async (alg, key, data) => {\n    const k = (0,_get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alg, key, 'sign');\n    if (alg.startsWith('HS')) {\n        const hmac = node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac((0,_hmac_digest_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg), k);\n        hmac.update(data);\n        return hmac.digest();\n    }\n    return oneShotSign((0,_dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(alg), data, (0,_node_key_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(alg, k));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sign);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvc2lnbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXNDO0FBQ0E7QUFDRztBQUNDO0FBQ047QUFDYztBQUNsRCxvQkFBb0Isb0RBQVMsQ0FBQyw2Q0FBVztBQUN6QztBQUNBLGNBQWMsbUVBQVU7QUFDeEI7QUFDQSxxQkFBcUIsbURBQWlCLENBQUMsMkRBQVU7QUFDakQ7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDBEQUFVLGFBQWEsd0RBQU87QUFDckQ7QUFDQSxpRUFBZSxJQUFJLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXHNpZ24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgY3J5cHRvIGZyb20gJ25vZGU6Y3J5cHRvJztcbmltcG9ydCB7IHByb21pc2lmeSB9IGZyb20gJ25vZGU6dXRpbCc7XG5pbXBvcnQgbm9kZURpZ2VzdCBmcm9tICcuL2RzYV9kaWdlc3QuanMnO1xuaW1wb3J0IGhtYWNEaWdlc3QgZnJvbSAnLi9obWFjX2RpZ2VzdC5qcyc7XG5pbXBvcnQgbm9kZUtleSBmcm9tICcuL25vZGVfa2V5LmpzJztcbmltcG9ydCBnZXRTaWduS2V5IGZyb20gJy4vZ2V0X3NpZ25fdmVyaWZ5X2tleS5qcyc7XG5jb25zdCBvbmVTaG90U2lnbiA9IHByb21pc2lmeShjcnlwdG8uc2lnbik7XG5jb25zdCBzaWduID0gYXN5bmMgKGFsZywga2V5LCBkYXRhKSA9PiB7XG4gICAgY29uc3QgayA9IGdldFNpZ25LZXkoYWxnLCBrZXksICdzaWduJyk7XG4gICAgaWYgKGFsZy5zdGFydHNXaXRoKCdIUycpKSB7XG4gICAgICAgIGNvbnN0IGhtYWMgPSBjcnlwdG8uY3JlYXRlSG1hYyhobWFjRGlnZXN0KGFsZyksIGspO1xuICAgICAgICBobWFjLnVwZGF0ZShkYXRhKTtcbiAgICAgICAgcmV0dXJuIGhtYWMuZGlnZXN0KCk7XG4gICAgfVxuICAgIHJldHVybiBvbmVTaG90U2lnbihub2RlRGlnZXN0KGFsZyksIGRhdGEsIG5vZGVLZXkoYWxnLCBrKSk7XG59O1xuZXhwb3J0IGRlZmF1bHQgc2lnbjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js":
/*!**********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst timingSafeEqual = node_crypto__WEBPACK_IMPORTED_MODULE_0__.timingSafeEqual;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (timingSafeEqual);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvdGltaW5nX3NhZmVfZXF1YWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFDdEQsd0JBQXdCLHdEQUFJO0FBQzVCLGlFQUFlLGVBQWUsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcdGltaW5nX3NhZmVfZXF1YWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdGltaW5nU2FmZUVxdWFsIGFzIGltcGwgfSBmcm9tICdub2RlOmNyeXB0byc7XG5jb25zdCB0aW1pbmdTYWZlRXF1YWwgPSBpbXBsO1xuZXhwb3J0IGRlZmF1bHQgdGltaW5nU2FmZUVxdWFsO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/verify.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/verify.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dsa_digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/dsa_digest.js\");\n/* harmony import */ var _node_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/node_key.js\");\n/* harmony import */ var _sign_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sign.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/sign.js\");\n/* harmony import */ var _get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get_sign_verify_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js\");\n\n\n\n\n\n\nconst oneShotVerify = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.verify);\nconst verify = async (alg, key, signature, data) => {\n    const k = (0,_get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alg, key, 'verify');\n    if (alg.startsWith('HS')) {\n        const expected = await (0,_sign_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg, k, data);\n        const actual = signature;\n        try {\n            return node_crypto__WEBPACK_IMPORTED_MODULE_0__.timingSafeEqual(actual, expected);\n        }\n        catch {\n            return false;\n        }\n    }\n    const algorithm = (0,_dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(alg);\n    const keyInput = (0,_node_key_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(alg, k);\n    try {\n        return await oneShotVerify(algorithm, data, keyInput, signature);\n    }\n    catch {\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (verify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvdmVyaWZ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBc0M7QUFDQTtBQUNHO0FBQ0w7QUFDUDtBQUN1QjtBQUNwRCxzQkFBc0Isb0RBQVMsQ0FBQywrQ0FBYTtBQUM3QztBQUNBLGNBQWMsbUVBQVk7QUFDMUI7QUFDQSwrQkFBK0Isb0RBQUk7QUFDbkM7QUFDQTtBQUNBLG1CQUFtQix3REFBc0I7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBVTtBQUNoQyxxQkFBcUIsd0RBQU87QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxNQUFNLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXHZlcmlmeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBjcnlwdG8gZnJvbSAnbm9kZTpjcnlwdG8nO1xuaW1wb3J0IHsgcHJvbWlzaWZ5IH0gZnJvbSAnbm9kZTp1dGlsJztcbmltcG9ydCBub2RlRGlnZXN0IGZyb20gJy4vZHNhX2RpZ2VzdC5qcyc7XG5pbXBvcnQgbm9kZUtleSBmcm9tICcuL25vZGVfa2V5LmpzJztcbmltcG9ydCBzaWduIGZyb20gJy4vc2lnbi5qcyc7XG5pbXBvcnQgZ2V0VmVyaWZ5S2V5IGZyb20gJy4vZ2V0X3NpZ25fdmVyaWZ5X2tleS5qcyc7XG5jb25zdCBvbmVTaG90VmVyaWZ5ID0gcHJvbWlzaWZ5KGNyeXB0by52ZXJpZnkpO1xuY29uc3QgdmVyaWZ5ID0gYXN5bmMgKGFsZywga2V5LCBzaWduYXR1cmUsIGRhdGEpID0+IHtcbiAgICBjb25zdCBrID0gZ2V0VmVyaWZ5S2V5KGFsZywga2V5LCAndmVyaWZ5Jyk7XG4gICAgaWYgKGFsZy5zdGFydHNXaXRoKCdIUycpKSB7XG4gICAgICAgIGNvbnN0IGV4cGVjdGVkID0gYXdhaXQgc2lnbihhbGcsIGssIGRhdGEpO1xuICAgICAgICBjb25zdCBhY3R1YWwgPSBzaWduYXR1cmU7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gY3J5cHRvLnRpbWluZ1NhZmVFcXVhbChhY3R1YWwsIGV4cGVjdGVkKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgYWxnb3JpdGhtID0gbm9kZURpZ2VzdChhbGcpO1xuICAgIGNvbnN0IGtleUlucHV0ID0gbm9kZUtleShhbGcsIGspO1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBhd2FpdCBvbmVTaG90VmVyaWZ5KGFsZ29yaXRobSwgZGF0YSwga2V5SW5wdXQsIHNpZ25hdHVyZSk7XG4gICAgfVxuICAgIGNhdGNoIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbn07XG5leHBvcnQgZGVmYXVsdCB2ZXJpZnk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/verify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/webcrypto.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isCryptoKey: () => (/* binding */ isCryptoKey)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n\n\nconst webcrypto = node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (webcrypto);\nconst isCryptoKey = (key) => node_util__WEBPACK_IMPORTED_MODULE_1__.types.isCryptoKey(key);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvd2ViY3J5cHRvLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0M7QUFDSjtBQUNsQyxrQkFBa0Isa0RBQWdCO0FBQ2xDLGlFQUFlLFNBQVMsRUFBQztBQUNsQiw2QkFBNkIsNENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXHdlYmNyeXB0by5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBjcnlwdG8gZnJvbSAnbm9kZTpjcnlwdG8nO1xuaW1wb3J0ICogYXMgdXRpbCBmcm9tICdub2RlOnV0aWwnO1xuY29uc3Qgd2ViY3J5cHRvID0gY3J5cHRvLndlYmNyeXB0bztcbmV4cG9ydCBkZWZhdWx0IHdlYmNyeXB0bztcbmV4cG9ydCBjb25zdCBpc0NyeXB0b0tleSA9IChrZXkpID0+IHV0aWwudHlwZXMuaXNDcnlwdG9LZXkoa2V5KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/base64url.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\nconst encode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.encode;\nconst decode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.decode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3V0aWwvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM5QyxlQUFlLHlEQUFnQjtBQUMvQixlQUFlLHlEQUFnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcdXRpbFxcYmFzZTY0dXJsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGJhc2U2NHVybCBmcm9tICcuLi9ydW50aW1lL2Jhc2U2NHVybC5qcyc7XG5leHBvcnQgY29uc3QgZW5jb2RlID0gYmFzZTY0dXJsLmVuY29kZTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBiYXNlNjR1cmwuZGVjb2RlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/util/errors.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/errors.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JOSEAlgNotAllowed: () => (/* binding */ JOSEAlgNotAllowed),\n/* harmony export */   JOSEError: () => (/* binding */ JOSEError),\n/* harmony export */   JOSENotSupported: () => (/* binding */ JOSENotSupported),\n/* harmony export */   JWEDecryptionFailed: () => (/* binding */ JWEDecryptionFailed),\n/* harmony export */   JWEInvalid: () => (/* binding */ JWEInvalid),\n/* harmony export */   JWKInvalid: () => (/* binding */ JWKInvalid),\n/* harmony export */   JWKSInvalid: () => (/* binding */ JWKSInvalid),\n/* harmony export */   JWKSMultipleMatchingKeys: () => (/* binding */ JWKSMultipleMatchingKeys),\n/* harmony export */   JWKSNoMatchingKey: () => (/* binding */ JWKSNoMatchingKey),\n/* harmony export */   JWKSTimeout: () => (/* binding */ JWKSTimeout),\n/* harmony export */   JWSInvalid: () => (/* binding */ JWSInvalid),\n/* harmony export */   JWSSignatureVerificationFailed: () => (/* binding */ JWSSignatureVerificationFailed),\n/* harmony export */   JWTClaimValidationFailed: () => (/* binding */ JWTClaimValidationFailed),\n/* harmony export */   JWTExpired: () => (/* binding */ JWTExpired),\n/* harmony export */   JWTInvalid: () => (/* binding */ JWTInvalid)\n/* harmony export */ });\nclass JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JOSEAlgNotAllowed extends JOSEError {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nclass JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nclass JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nclass JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nclass JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nclass JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nclass JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nclass JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nclass JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nclass JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\n");

/***/ })

};
;