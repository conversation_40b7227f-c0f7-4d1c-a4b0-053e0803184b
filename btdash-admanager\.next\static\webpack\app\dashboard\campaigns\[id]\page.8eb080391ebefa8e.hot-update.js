"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/campaigns/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/campaigns/[id]/client-wrapper.tsx":
/*!*********************************************************!*\
  !*** ./app/dashboard/campaigns/[id]/client-wrapper.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CampaignDetailsClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_page_targeting_display__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/page-targeting-display */ \"(app-pages-browser)/./components/page-targeting-display.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Edit,FileText,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Edit,FileText,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Edit,FileText,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Edit,FileText,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Edit,FileText,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n// app/dashboard/campaigns/[id]/client-wrapper.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CampaignDetailsClientWrapper(param) {\n    let { campaign, ads } = param;\n    var _campaign_placement, _targeting_pageTypes, _targeting_pageTypes1, _campaign_clicks;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (__webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\").useToast)();\n    const [deletingAdId, setDeletingAdId] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    if (!campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 text-center\",\n            children: \"Campaign not found\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n            lineNumber: 39,\n            columnNumber: 10\n        }, this);\n    }\n    // Get campaign summary info from ads\n    const hasAds = ads.length > 0;\n    const primaryAd = ads[0]; // Keep for backward compatibility with some fields\n    const handleEditAd = (adId)=>{\n        router.push(\"/dashboard/ads/\".concat(adId, \"/edit\"));\n    };\n    const handleCampaignTargeting = ()=>{\n        router.push(\"/dashboard/campaigns/\".concat(campaign.id, \"/targeting\"));\n    };\n    const handlePayNow = ()=>{\n        router.push(\"/dashboard/campaigns/\".concat(campaign.id, \"/payment\"));\n    };\n    const handleEditCampaign = ()=>{\n        router.push(\"/dashboard/campaigns/\".concat(campaign.id, \"/edit\"));\n    };\n    const handlePaymentSuccess = ()=>{\n        // Refresh the page to show updated status\n        window.location.reload();\n    };\n    const handleAdAnalytics = (adId)=>{\n        router.push(\"/dashboard/ads/\".concat(adId, \"/analytics\"));\n    };\n    const handleDeleteAd = async (adId)=>{\n        if (!confirm(\"Are you sure you want to delete this ad? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            setDeletingAdId(adId);\n            const response = await fetch(\"/api/user/ads/\".concat(adId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Failed to delete ad: \".concat(errorText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                toast({\n                    title: \"Ad Deleted\",\n                    description: \"The ad has been successfully deleted.\"\n                });\n                // Refresh the page to update the ads list\n                window.location.reload();\n            } else {\n                throw new Error(result.message || \"Failed to delete ad\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting ad:\", error);\n            toast({\n                title: \"Delete Failed\",\n                description: error instanceof Error ? error.message : \"Failed to delete ad\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeletingAdId(null);\n        }\n    };\n    const handleAddAd = ()=>{\n        router.push(\"/dashboard/campaigns/\".concat(campaign.id, \"/ads/create\"));\n    };\n    // Helper function to get country names from codes\n    const getCountryNames = function() {\n        let countryCodes = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n        return countryCodes.map((code)=>{\n            const country = countries.find((c)=>c.value === code);\n            return country ? country.label : code;\n        }).join(\", \");\n    };\n    // Get targeting information with safe defaults\n    const targeting = campaign.targeting || {};\n    const countries = targeting.countries || {\n        mode: \"all\",\n        include: [],\n        exclude: []\n    };\n    const { mode, include, exclude } = countries;\n    let targetingDescription = \"Showing to all countries\";\n    if (mode === \"include\" && (include === null || include === void 0 ? void 0 : include.length)) {\n        targetingDescription = \"Showing only in: \".concat(getCountryNames(include));\n    } else if (mode === \"exclude\" && (exclude === null || exclude === void 0 ? void 0 : exclude.length)) {\n        targetingDescription = \"Showing everywhere except: \".concat(getCountryNames(exclude));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>router.back(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 7\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: campaign.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleCampaignTargeting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 7\n                                    }, this),\n                                    \"Campaign Targeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 6\n                            }, this),\n                            campaign.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"/dashboard/campaigns/\".concat(campaign.id, \"/analytics\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 9\n                                        }, this),\n                                        \"Analytics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 8\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 7\n                            }, this),\n                            [\n                                \"pending\",\n                                \"paused\",\n                                \"rejected\"\n                            ].includes(campaign.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"/dashboard/campaigns/\".concat(campaign.id, \"/edit\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 9\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 8\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                lineNumber: 139,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    children: \"Campaign Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: [\n                                                        ((_campaign_placement = campaign.placement) === null || _campaign_placement === void 0 ? void 0 : _campaign_placement.name) || \"No placement\",\n                                                        \" •\",\n                                                        \" \",\n                                                        new Date(campaign.start_date).toLocaleDateString(),\n                                                        \" -\",\n                                                        \" \",\n                                                        new Date(campaign.end_date).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                            variant: campaign.status === \"active\" ? \"default\" : campaign.status === \"approved\" ? \"secondary\" : campaign.status === \"pending\" ? \"outline\" : campaign.status === \"rejected\" ? \"destructive\" : \"secondary\",\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(campaign.status === \"active\" && \"bg-green-100 text-green-800 dark:bg-green-900\", campaign.status === \"paused\" && \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900\", campaign.status === \"rejected\" && \"bg-red-100 text-red-800 dark:bg-red-900\"),\n                                            children: campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 7\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    hasAds ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium mb-2\",\n                                                        children: \"Campaign Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Total Ads\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: ads.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Active Ads\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: ads.filter((ad)=>ad.status === \"active\").length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Placements\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: new Set(ads.map((ad)=>ad.slot_id)).size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Created\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: new Date(campaign.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 9\n                                            }, this),\n                                            primaryAd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Sample Destination URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-600 underline\",\n                                                                children: primaryAd.target_url\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"First Ad Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: new Date(primaryAd.created_at).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 10\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"No ads created for this campaign yet.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: \"Country Targeting\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 9\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: targetingDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: \"Page Targeting\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 9\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_targeting_display__WEBPACK_IMPORTED_MODULE_1__.PageTargetingDisplay, {\n                                                pageTypes: ((_targeting_pageTypes = targeting.pageTypes) === null || _targeting_pageTypes === void 0 ? void 0 : _targeting_pageTypes.types) || [],\n                                                categories: ((_targeting_pageTypes1 = targeting.pageTypes) === null || _targeting_pageTypes1 === void 0 ? void 0 : _targeting_pageTypes1.categories) || {},\n                                                detailed: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 7\n                                    }, this),\n                                    campaign.status === \"active\" && campaign.impressions !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium mb-4\",\n                                                        children: \"Performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Impressions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: campaign.impressions.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Clicks\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: ((_campaign_clicks = campaign.clicks) === null || _campaign_clicks === void 0 ? void 0 : _campaign_clicks.toLocaleString()) || \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"CTR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: campaign.ctr ? \"\".concat(campaign.ctr, \"%\") : \"0.00%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            (primaryAd === null || primaryAd === void 0 ? void 0 : primaryAd.max_impressions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Max Impressions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold\",\n                                                                        children: primaryAd.max_impressions.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    (primaryAd === null || primaryAd === void 0 ? void 0 : primaryAd.rejection_reason) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium mb-2\",\n                                                        children: \"Rejection Reason\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: primaryAd.rejection_reason\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CampaignStatusCard, {\n                                campaign: campaign,\n                                onPayNow: handlePayNow,\n                                onEdit: handleEditCampaign,\n                                showPaymentButton: campaign.status === \"approved\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 6\n                            }, this),\n                            campaign.status === \"approved\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CampaignPayment, {\n                                campaign: campaign,\n                                onPaymentSuccess: handlePaymentSuccess\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 7\n                            }, this),\n                            campaign.placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Placement Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Placement:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: campaign.placement.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: campaign.placement.page\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Dimensions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                campaign.placement.width,\n                                                                \"x\",\n                                                                campaign.placement.height,\n                                                                \"px\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Budget\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                campaign.total_budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Total Budget:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                \"$\",\n                                                                campaign.total_budget\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 10\n                                                }, this),\n                                                campaign.budget_cpc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"CPC:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                \"$\",\n                                                                campaign.budget_cpc\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 10\n                                                }, this),\n                                                campaign.budget_cpm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"CPM:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                \"$\",\n                                                                campaign.budget_cpm\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        children: [\n                                                            \"Campaign Ads (\",\n                                                            ads.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: handleAddAd,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 10\n                                                            }, this),\n                                                            \"Add Ad\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 9\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Manage ads within this campaign\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: ads.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground mb-4\",\n                                                    children: \"No ads in this campaign yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleAddAd,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        \"Create First Ad\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: ads.map((ad, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 items-start p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-20 h-16 rounded-md overflow-hidden border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                src: ad.image_url || \"/placeholder.svg\",\n                                                                alt: \"Ad creative\",\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 13\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: ad.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                                    lineNumber: 434,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: index === 0 ? \"Primary Ad\" : \"Ad \".concat(index + 1)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                                            variant: ad.status === \"active\" ? \"default\" : \"secondary\",\n                                                                            children: ad.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 14\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-4 text-sm text-muted-foreground mb-3\",\n                                                                    children: [\n                                                                        ad.max_impressions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Max Impressions: \",\n                                                                                ad.max_impressions.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        ad.max_clicks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Max Clicks: \",\n                                                                                ad.max_clicks.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Weight: \",\n                                                                                ad.weight || 1\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 14\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleEditAd(ad.id),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                \"Edit\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleAdAnalytics(ad.id),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                                    lineNumber: 468,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                \"Analytics\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        ads.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleDeleteAd(ad.id),\n                                                                            disabled: deletingAdId === ad.id,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Edit_FileText_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                                    lineNumber: 478,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                deletingAdId === ad.id ? \"Deleting...\" : \"Delete\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, ad.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 11\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n                lineNumber: 170,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\[id]\\\\client-wrapper.tsx\",\n        lineNumber: 138,\n        columnNumber: 3\n    }, this);\n}\n_s(CampaignDetailsClientWrapper, \"Kza5RqOmXL2p0SVxhAsACozex0E=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter\n    ];\n});\n_c = CampaignDetailsClientWrapper;\nvar _c;\n$RefreshReg$(_c, \"CampaignDetailsClientWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/campaigns/[id]/client-wrapper.tsx\n"));

/***/ })

});