const Joi = require("joi");
const { sendValidationError } = require("../utils/responseHelpers");

// Valid targeting options (should match CampaignTargetingService)
const VALID_COUNTRIES = ["US", "CA", "UK", "DE", "FR", "AU", "JP", "BR", "IN", "CN"];
const VALID_DEVICES = ["desktop", "mobile", "tablet"];
const VALID_LANGUAGES = ["en", "es", "fr", "de", "pt", "ja", "zh", "hi"];
const VALID_PAGE_TYPES = ["subnet", "companies", "products", "news"];
const VALID_INTERESTS = [
	"technology", "business", "finance", "healthcare", "education", 
	"entertainment", "sports", "travel"
];
const VALID_AGE_RANGES = ["18-24", "25-34", "35-44", "45-54", "55-64", "65+"];

// Country targeting schema
const countryTargetingSchema = Joi.object({
	mode: Joi.string().valid("all", "include", "exclude").required().messages({
		"any.only": "Country mode must be 'all', 'include', or 'exclude'",
		"any.required": "Country mode is required"
	}),
	include: Joi.array().items(
		Joi.string().valid(...VALID_COUNTRIES).messages({
			"any.only": `Country code must be one of: ${VALID_COUNTRIES.join(", ")}`
		})
	).when("mode", {
		is: "include",
		then: Joi.required().min(1).messages({
			"array.min": "At least one country must be included when mode is 'include'"
		}),
		otherwise: Joi.optional().default([])
	}),
	exclude: Joi.array().items(
		Joi.string().valid(...VALID_COUNTRIES).messages({
			"any.only": `Country code must be one of: ${VALID_COUNTRIES.join(", ")}`
		})
	).when("mode", {
		is: "exclude",
		then: Joi.required().min(1).messages({
			"array.min": "At least one country must be excluded when mode is 'exclude'"
		}),
		otherwise: Joi.optional().default([])
	})
}).custom((value, helpers) => {
	// Ensure include and exclude don't overlap
	if (value.include && value.exclude) {
		const overlap = value.include.filter(country => value.exclude.includes(country));
		if (overlap.length > 0) {
			return helpers.error("countries.overlap", { overlap });
		}
	}
	return value;
}).messages({
	"countries.overlap": "Countries cannot be both included and excluded: {{#overlap}}"
});

// Page types targeting schema
const pageTypesTargetingSchema = Joi.object({
	types: Joi.array().items(
		Joi.string().valid(...VALID_PAGE_TYPES).messages({
			"any.only": `Page type must be one of: ${VALID_PAGE_TYPES.join(", ")}`
		})
	).default([]).messages({
		"array.base": "Page types must be an array"
	}),
	categories: Joi.object().pattern(
		Joi.string().valid(...VALID_PAGE_TYPES),
		Joi.alternatives().try(
			Joi.string().valid("all"),
			Joi.array().items(Joi.string())
		)
	).default({}).messages({
		"object.base": "Categories must be an object"
	})
});

// Main targeting schema
const targetingSchema = Joi.object({
	countries: countryTargetingSchema.default({
		mode: "all",
		include: [],
		exclude: []
	}),
	
	pageTypes: pageTypesTargetingSchema.default({
		types: [],
		categories: {}
	}),
	
	devices: Joi.array().items(
		Joi.string().valid(...VALID_DEVICES).messages({
			"any.only": `Device must be one of: ${VALID_DEVICES.join(", ")}`
		})
	).unique().default([]).messages({
		"array.base": "Devices must be an array",
		"array.unique": "Devices must be unique"
	}),
	
	languages: Joi.array().items(
		Joi.string().valid(...VALID_LANGUAGES).messages({
			"any.only": `Language must be one of: ${VALID_LANGUAGES.join(", ")}`
		})
	).unique().default([]).messages({
		"array.base": "Languages must be an array",
		"array.unique": "Languages must be unique"
	}),
	
	interests: Joi.array().items(
		Joi.string().valid(...VALID_INTERESTS).messages({
			"any.only": `Interest must be one of: ${VALID_INTERESTS.join(", ")}`
		})
	).unique().default([]).messages({
		"array.base": "Interests must be an array",
		"array.unique": "Interests must be unique"
	}),
	
	age: Joi.array().items(
		Joi.string().valid(...VALID_AGE_RANGES).messages({
			"any.only": `Age range must be one of: ${VALID_AGE_RANGES.join(", ")}`
		})
	).unique().default([]).messages({
		"array.base": "Age ranges must be an array",
		"array.unique": "Age ranges must be unique"
	})
}).custom((value, helpers) => {
	// Validate targeting effectiveness
	const warnings = [];
	
	// Check if targeting is too restrictive
	if (value.countries.mode === "include" && value.countries.include.length === 1 &&
		value.devices.length === 1 && value.languages.length === 1) {
		warnings.push("Targeting may be too restrictive - consider broadening criteria");
	}
	
	// Check if targeting is too broad (no restrictions)
	if (value.countries.mode === "all" && 
		value.devices.length === 0 && 
		value.languages.length === 0 && 
		value.interests.length === 0 && 
		value.age.length === 0) {
		warnings.push("No targeting restrictions applied - ads will show to all users");
	}
	
	// Add warnings to context for informational purposes
	if (warnings.length > 0) {
		helpers.state.warnings = warnings;
	}
	
	return value;
});

/**
 * Validate targeting rules
 * @param {Object} targetingRules - Targeting rules to validate
 * @returns {Object} Validation result
 */
function validateTargetingRules(targetingRules) {
	const { error, value, warning } = targetingSchema.validate(targetingRules, { 
		abortEarly: false,
		allowUnknown: false,
		stripUnknown: true
	});
	
	const result = {
		valid: !error,
		value: value,
		errors: [],
		warnings: []
	};
	
	if (error) {
		result.errors = error.details.map(detail => ({
			field: detail.path.join("."),
			message: detail.message,
			value: detail.context?.value
		}));
	}
	
	// Add any warnings from custom validation
	if (value && value.warnings) {
		result.warnings = value.warnings;
		delete value.warnings; // Remove from final value
	}
	
	return result;
}

/**
 * Validate targeting effectiveness
 * @param {Object} targetingRules - Targeting rules to analyze
 * @returns {Object} Effectiveness analysis
 */
function analyzeTargetingEffectiveness(targetingRules) {
	const analysis = {
		score: 100, // Start with 100% effectiveness
		issues: [],
		recommendations: [],
		estimatedReach: "Unknown"
	};
	
	// Analyze country targeting
	if (targetingRules.countries) {
		if (targetingRules.countries.mode === "include") {
			const countryCount = targetingRules.countries.include.length;
			if (countryCount === 1) {
				analysis.score -= 20;
				analysis.issues.push("Single country targeting may limit reach");
				analysis.recommendations.push("Consider adding similar markets");
			} else if (countryCount > 8) {
				analysis.issues.push("Many countries selected - ensure budget allocation");
			}
		}
	}
	
	// Analyze device targeting
	if (targetingRules.devices && targetingRules.devices.length > 0) {
		if (targetingRules.devices.length === 1) {
			analysis.score -= 15;
			analysis.issues.push("Single device targeting may limit reach");
			if (targetingRules.devices[0] === "desktop") {
				analysis.recommendations.push("Consider adding mobile for broader reach");
			}
		}
	}
	
	// Analyze language targeting
	if (targetingRules.languages && targetingRules.languages.length > 0) {
		if (targetingRules.languages.length === 1 && targetingRules.languages[0] !== "en") {
			analysis.score -= 10;
			analysis.issues.push("Non-English only targeting may limit reach");
		}
	}
	
	// Analyze age targeting
	if (targetingRules.age && targetingRules.age.length > 0) {
		if (targetingRules.age.length === 1) {
			analysis.score -= 10;
			analysis.issues.push("Single age group targeting may limit reach");
			analysis.recommendations.push("Consider adjacent age groups");
		}
	}
	
	// Estimate reach based on restrictions
	const restrictionCount = [
		targetingRules.countries?.mode !== "all" ? 1 : 0,
		targetingRules.devices?.length > 0 ? 1 : 0,
		targetingRules.languages?.length > 0 ? 1 : 0,
		targetingRules.interests?.length > 0 ? 1 : 0,
		targetingRules.age?.length > 0 ? 1 : 0
	].reduce((sum, val) => sum + val, 0);
	
	if (restrictionCount === 0) {
		analysis.estimatedReach = "Very High";
	} else if (restrictionCount <= 2) {
		analysis.estimatedReach = "High";
	} else if (restrictionCount <= 3) {
		analysis.estimatedReach = "Medium";
	} else {
		analysis.estimatedReach = "Low";
		analysis.recommendations.push("Consider reducing targeting restrictions");
	}
	
	return analysis;
}

/**
 * Middleware to validate targeting rules
 */
const validateTargeting = (req, res, next) => {
	const validation = validateTargetingRules(req.body);
	
	if (!validation.valid) {
		return sendValidationError(res, validation.errors);
	}
	
	// Add validated targeting to request
	req.validatedTargeting = validation.value;
	req.targetingWarnings = validation.warnings;
	
	next();
};

module.exports = {
	validateTargeting,
	validateTargetingRules,
	analyzeTargetingEffectiveness,
	targetingSchema,
	VALID_COUNTRIES,
	VALID_DEVICES,
	VALID_LANGUAGES,
	VALID_PAGE_TYPES,
	VALID_INTERESTS,
	VALID_AGE_RANGES
};
