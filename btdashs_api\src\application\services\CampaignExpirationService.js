const logger = require("../../infrastructure/logging/logger");

/**
 * Service for handling campaign expiration and date validation
 */
class CampaignExpirationService {
	constructor() {
		this.db = require("../../infrastructure/database/knex");
	}

	/**
	 * Check and update expired campaigns
	 * @returns {Promise<Object>} Summary of processed campaigns
	 */
	async processExpiredCampaigns() {
		const trx = await this.db.transaction();
		
		try {
			const now = new Date();
			
			// Find campaigns that have expired but are still active
			const expiredCampaigns = await trx("dtm_ads.ad_campaigns")
				.where("end_date", "<", now)
				.whereIn("status", ["active", "approved"])
				.select("*");

			if (expiredCampaigns.length === 0) {
				await trx.commit();
				return {
					processed: 0,
					expired: [],
					message: "No expired campaigns found"
				};
			}

			const results = {
				processed: expiredCampaigns.length,
				expired: [],
				errors: []
			};

			// Process each expired campaign
			for (const campaign of expiredCampaigns) {
				try {
					await this.expireCampaign(trx, campaign);
					results.expired.push({
						id: campaign.id,
						name: campaign.name,
						end_date: campaign.end_date,
						previous_status: campaign.status
					});
				} catch (error) {
					logger.error("Error expiring campaign", { 
						campaignId: campaign.id, 
						error: error.message 
					});
					results.errors.push({
						id: campaign.id,
						error: error.message
					});
				}
			}

			await trx.commit();

			logger.info("Campaign expiration process completed", {
				processed: results.processed,
				expired: results.expired.length,
				errors: results.errors.length
			});

			return results;

		} catch (error) {
			await trx.rollback();
			logger.error("Error in campaign expiration process", { error: error.message });
			throw error;
		}
	}

	/**
	 * Expire a single campaign
	 * @param {Object} trx - Database transaction
	 * @param {Object} campaign - Campaign to expire
	 */
	async expireCampaign(trx, campaign) {
		// Update campaign status to completed
		await trx("dtm_ads.ad_campaigns")
			.where({ id: campaign.id })
			.update({
				status: "completed",
				updated_at: new Date()
			});

		// Update all ads in the campaign to completed status
		await trx("dtm_ads.ads")
			.where({ campaign_id: campaign.id })
			.update({
				status: "completed",
				updated_at: new Date()
			});

		// Log the expiration
		await this.logCampaignExpiration(trx, campaign);

		logger.info("Campaign expired", {
			campaignId: campaign.id,
			campaignName: campaign.name,
			endDate: campaign.end_date,
			previousStatus: campaign.status
		});
	}

	/**
	 * Log campaign expiration for audit trail
	 * @param {Object} trx - Database transaction
	 * @param {Object} campaign - Expired campaign
	 */
	async logCampaignExpiration(trx, campaign) {
		try {
			// Check if admin_actions table exists
			const tableExists = await trx.schema.hasTable("dtm_ads.admin_actions");
			
			if (tableExists) {
				await trx("dtm_ads.admin_actions").insert({
					admin_id: null, // System action
					action: "campaign_expired",
					entity_id: campaign.id,
					metadata: {
						campaign_name: campaign.name,
						end_date: campaign.end_date,
						previous_status: campaign.status,
						expired_at: new Date().toISOString()
					},
					created_at: new Date()
				});
			}
		} catch (error) {
			// Don't fail the main operation if logging fails
			logger.warn("Failed to log campaign expiration", {
				campaignId: campaign.id,
				error: error.message
			});
		}
	}

	/**
	 * Check for campaigns starting today and activate them
	 * @returns {Promise<Object>} Summary of activated campaigns
	 */
	async processStartingCampaigns() {
		const trx = await this.db.transaction();
		
		try {
			const today = new Date();
			today.setHours(0, 0, 0, 0); // Start of day
			
			const tomorrow = new Date(today);
			tomorrow.setDate(tomorrow.getDate() + 1); // Start of next day

			// Find approved campaigns that should start today
			const startingCampaigns = await trx("dtm_ads.ad_campaigns")
				.where("start_date", ">=", today)
				.where("start_date", "<", tomorrow)
				.where("status", "approved")
				.select("*");

			if (startingCampaigns.length === 0) {
				await trx.commit();
				return {
					processed: 0,
					activated: [],
					message: "No campaigns to activate today"
				};
			}

			const results = {
				processed: startingCampaigns.length,
				activated: [],
				errors: []
			};

			// Process each starting campaign
			for (const campaign of startingCampaigns) {
				try {
					await this.activateCampaign(trx, campaign);
					results.activated.push({
						id: campaign.id,
						name: campaign.name,
						start_date: campaign.start_date
					});
				} catch (error) {
					logger.error("Error activating campaign", { 
						campaignId: campaign.id, 
						error: error.message 
					});
					results.errors.push({
						id: campaign.id,
						error: error.message
					});
				}
			}

			await trx.commit();

			logger.info("Campaign activation process completed", {
				processed: results.processed,
				activated: results.activated.length,
				errors: results.errors.length
			});

			return results;

		} catch (error) {
			await trx.rollback();
			logger.error("Error in campaign activation process", { error: error.message });
			throw error;
		}
	}

	/**
	 * Activate a campaign that's ready to start
	 * @param {Object} trx - Database transaction
	 * @param {Object} campaign - Campaign to activate
	 */
	async activateCampaign(trx, campaign) {
		// Update campaign status to active
		await trx("dtm_ads.ad_campaigns")
			.where({ id: campaign.id })
			.update({
				status: "active",
				updated_at: new Date()
			});

		// Update all ads in the campaign to active status
		await trx("dtm_ads.ads")
			.where({ campaign_id: campaign.id })
			.update({
				status: "active",
				updated_at: new Date()
			});

		logger.info("Campaign activated", {
			campaignId: campaign.id,
			campaignName: campaign.name,
			startDate: campaign.start_date
		});
	}

	/**
	 * Validate campaign dates
	 * @param {Date} startDate - Campaign start date
	 * @param {Date} endDate - Campaign end date
	 * @returns {Object} Validation result
	 */
	validateCampaignDates(startDate, endDate) {
		const now = new Date();
		const start = new Date(startDate);
		const end = new Date(endDate);
		
		const errors = [];

		// Check if dates are valid
		if (isNaN(start.getTime())) {
			errors.push("Start date is invalid");
		}
		
		if (isNaN(end.getTime())) {
			errors.push("End date is invalid");
		}

		if (errors.length > 0) {
			return { valid: false, errors };
		}

		// Check if start date is not in the past (allow today)
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		
		if (start < today) {
			errors.push("Start date cannot be in the past");
		}

		// Check if end date is after start date
		if (end <= start) {
			errors.push("End date must be after start date");
		}

		// Check minimum campaign duration (at least 1 day)
		const minDuration = 24 * 60 * 60 * 1000; // 1 day in milliseconds
		if (end.getTime() - start.getTime() < minDuration) {
			errors.push("Campaign must run for at least 1 day");
		}

		// Check maximum campaign duration (e.g., 1 year)
		const maxDuration = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
		if (end.getTime() - start.getTime() > maxDuration) {
			errors.push("Campaign cannot run for more than 1 year");
		}

		return {
			valid: errors.length === 0,
			errors,
			duration: end.getTime() - start.getTime(),
			durationDays: Math.ceil((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000))
		};
	}

	/**
	 * Get campaigns expiring soon (within specified days)
	 * @param {number} days - Number of days to look ahead
	 * @returns {Promise<Array>} Campaigns expiring soon
	 */
	async getCampaignsExpiringSoon(days = 7) {
		const now = new Date();
		const futureDate = new Date();
		futureDate.setDate(futureDate.getDate() + days);

		return await this.db("dtm_ads.ad_campaigns")
			.where("end_date", ">", now)
			.where("end_date", "<=", futureDate)
			.whereIn("status", ["active", "approved"])
			.select("*");
	}
}

module.exports = CampaignExpirationService;
