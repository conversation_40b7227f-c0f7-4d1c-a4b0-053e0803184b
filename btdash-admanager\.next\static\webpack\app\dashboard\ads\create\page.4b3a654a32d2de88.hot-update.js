"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/ads/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/ads/create/client-wrapper.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/ads/create/client-wrapper.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateAdClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n// app/dashboard/ads/create/client-wrapper.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateAdClientWrapper(param) {\n    let { placementId, campaignId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [placement, setPlacement] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)({\n        title: \"\",\n        image_url: \"\",\n        target_url: \"\",\n        max_impressions: \"\",\n        max_clicks: \"\",\n        weight: \"1\",\n        campaign_id: campaignId || \"\",\n        // New campaign fields\n        campaign_name: \"\",\n        total_budget: \"\",\n        budget_cpc: \"\",\n        budget_cpm: \"\",\n        start_date: \"\",\n        end_date: \"\"\n    });\n    useEffect({\n        \"CreateAdClientWrapper.useEffect\": ()=>{\n            const fetchData = {\n                \"CreateAdClientWrapper.useEffect.fetchData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch placement details if provided\n                        if (placementId) {\n                            const placementRes = await fetch(\"/api/placements/\".concat(placementId));\n                            if (placementRes.ok) {\n                                const placementData = await placementRes.json();\n                                if (placementData.success) {\n                                    setPlacement(placementData.data);\n                                }\n                            }\n                        }\n                        // Fetch user campaigns\n                        const campaignsRes = await fetch(\"/api/user/campaigns\");\n                        if (campaignsRes.ok) {\n                            const campaignsData = await campaignsRes.json();\n                            if (campaignsData.success) {\n                                const activeCampaigns = campaignsData.data.filter({\n                                    \"CreateAdClientWrapper.useEffect.fetchData.activeCampaigns\": (c)=>c.status === \"pending\"\n                                }[\"CreateAdClientWrapper.useEffect.fetchData.activeCampaigns\"]);\n                                setCampaigns(activeCampaigns);\n                                // Auto-select option based on available campaigns\n                                if (campaignId) {\n                                    setSelectedOption(\"existing\");\n                                    setFormData({\n                                        \"CreateAdClientWrapper.useEffect.fetchData\": (prev)=>({\n                                                ...prev,\n                                                campaign_id: campaignId\n                                            })\n                                    }[\"CreateAdClientWrapper.useEffect.fetchData\"]);\n                                } else if (activeCampaigns.length === 0) {\n                                    setSelectedOption(\"new\");\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load data. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CreateAdClientWrapper.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"CreateAdClientWrapper.useEffect\"], [\n        placementId,\n        campaignId,\n        toast\n    ]);\n    // Filter campaigns to only show pending ones\n    const activeCampaigns = campaigns.filter((c)=>c.status === \"pending\");\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedOption) {\n            toast({\n                title: \"Selection Required\",\n                description: \"Please choose to add to existing campaign or create new campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            if (selectedOption === \"new\") {\n                // Create new campaign with ad\n                const campaignData = {\n                    name: formData.campaign_name,\n                    total_budget: formData.total_budget ? parseFloat(formData.total_budget) : null,\n                    budget_cpc: formData.budget_cpc ? parseFloat(formData.budget_cpc) : null,\n                    budget_cpm: formData.budget_cpm ? parseFloat(formData.budget_cpm) : null,\n                    start_date: formData.start_date,\n                    end_date: formData.end_date,\n                    // Ad data\n                    ad_title: formData.title,\n                    image_url: formData.image_url,\n                    target_url: formData.target_url,\n                    max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,\n                    max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,\n                    weight: parseInt(formData.weight),\n                    slot_id: placementId ? parseInt(placementId) : null\n                };\n                const response = await fetch(\"/api/user/campaigns\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(campaignData)\n                });\n                if (!response.ok) {\n                    let errorMessage = \"Failed to create campaign\";\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (jsonError) {\n                        // If response is not JSON, use status text\n                        errorMessage = \"\".concat(response.status, \": \").concat(response.statusText);\n                    }\n                    throw new Error(errorMessage);\n                }\n                const result = await response.json();\n                if (result.success) {\n                    toast({\n                        title: \"Campaign Created\",\n                        description: \"Your campaign and ad have been created successfully.\"\n                    });\n                    router.push(\"/dashboard/campaigns/\".concat(result.data.campaign.id));\n                }\n            } else {\n                // Add ad to existing campaign\n                const adData = {\n                    campaign_id: parseInt(formData.campaign_id),\n                    slot_id: placementId ? parseInt(placementId) : null,\n                    title: formData.title,\n                    image_url: formData.image_url,\n                    target_url: formData.target_url,\n                    max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,\n                    max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,\n                    weight: parseInt(formData.weight)\n                };\n                const response = await fetch(\"/api/user/ads\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(adData)\n                });\n                if (!response.ok) {\n                    let errorMessage = \"Failed to create ad\";\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (jsonError) {\n                        // If response is not JSON, use status text\n                        errorMessage = \"\".concat(response.status, \": \").concat(response.statusText);\n                    }\n                    throw new Error(errorMessage);\n                }\n                const result = await response.json();\n                if (result.success) {\n                    toast({\n                        title: \"Ad Created\",\n                        description: \"Your ad has been created successfully.\"\n                    });\n                    router.push(\"/dashboard/campaigns/\".concat(formData.campaign_id));\n                }\n            }\n        } catch (error) {\n            console.error(\"Error creating ad:\", error);\n            toast({\n                title: \"Creation Failed\",\n                description: error instanceof Error ? error.message : \"Failed to create ad\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                            href: \"/dashboard/placements\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 8\n                                    }, this),\n                                    \"Back to Placements\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Create New Ad\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Add an ad to an existing campaign or create a new campaign\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                !campaignId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Campaign Selection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Choose whether to add this ad to an existing campaign or create a new one\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        activeCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    id: \"existing\",\n                                                                    name: \"campaign-option\",\n                                                                    checked: selectedOption === \"existing\",\n                                                                    onChange: ()=>setSelectedOption(\"existing\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"existing\",\n                                                                    children: \"Add to existing campaign\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    id: \"new\",\n                                                                    name: \"campaign-option\",\n                                                                    checked: selectedOption === \"new\",\n                                                                    onChange: ()=>setSelectedOption(\"new\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"new\",\n                                                                    children: \"Create new campaign\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 10\n                                                }, this),\n                                                selectedOption === \"existing\" && activeCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"campaign\",\n                                                            children: \"Select Campaign\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: formData.campaign_id,\n                                                            onValueChange: (value)=>handleInputChange(\"campaign_id\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Choose a campaign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: activeCampaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: campaign.id.toString(),\n                                                                            children: [\n                                                                                campaign.name,\n                                                                                \" (\",\n                                                                                campaign.status,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, campaign.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 11\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Ad Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: placement ? \"Creating ad for \".concat(placement.name) : \"Enter your ad information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    selectedOption === \"new\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-medium\",\n                                                                        children: \"Campaign Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"campaign_name\",\n                                                                                        children: \"Campaign Name *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 323,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"campaign_name\",\n                                                                                        value: formData.campaign_name,\n                                                                                        onChange: (e)=>handleInputChange(\"campaign_name\", e.target.value),\n                                                                                        placeholder: \"Enter campaign name\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 322,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"total_budget\",\n                                                                                        children: \"Total Budget ($)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 335,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"total_budget\",\n                                                                                        type: \"number\",\n                                                                                        step: \"0.01\",\n                                                                                        value: formData.total_budget,\n                                                                                        onChange: (e)=>handleInputChange(\"total_budget\", e.target.value),\n                                                                                        placeholder: \"0.00\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 336,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"start_date\",\n                                                                                        children: \"Start Date *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 350,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"start_date\",\n                                                                                        type: \"date\",\n                                                                                        value: formData.start_date,\n                                                                                        onChange: (e)=>handleInputChange(\"start_date\", e.target.value),\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 351,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"end_date\",\n                                                                                        children: \"End Date *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 362,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"end_date\",\n                                                                                        type: \"date\",\n                                                                                        value: formData.end_date,\n                                                                                        onChange: (e)=>handleInputChange(\"end_date\", e.target.value),\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 363,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Ad Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"title\",\n                                                                        children: \"Ad Title *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"title\",\n                                                                        value: formData.title,\n                                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                                        placeholder: \"Enter ad title\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"image_url\",\n                                                                        children: \"Image URL *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"image_url\",\n                                                                        type: \"url\",\n                                                                        value: formData.image_url,\n                                                                        onChange: (e)=>handleInputChange(\"image_url\", e.target.value),\n                                                                        placeholder: \"https://example.com/image.jpg\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground mt-1\",\n                                                                        children: [\n                                                                            \"Required dimensions: \",\n                                                                            placement.width,\n                                                                            \"x\",\n                                                                            placement.height,\n                                                                            \"px\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    formData.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: \"Preview:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 relative w-full max-w-md border rounded-lg overflow-hidden bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: formData.image_url,\n                                                                                        alt: \"Ad preview\",\n                                                                                        className: \"w-full h-auto object-cover\",\n                                                                                        onError: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.style.display = \"none\";\n                                                                                            const errorDiv = target.nextElementSibling;\n                                                                                            if (errorDiv) errorDiv.style.display = \"block\";\n                                                                                        },\n                                                                                        onLoad: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.style.display = \"block\";\n                                                                                            const errorDiv = target.nextElementSibling;\n                                                                                            if (errorDiv) errorDiv.style.display = \"none\";\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 412,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"hidden p-4 text-center text-sm text-red-600\",\n                                                                                        style: {\n                                                                                            display: \"none\"\n                                                                                        },\n                                                                                        children: \"Failed to load image. Please check the URL.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 431,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: [\n                                                                                    \"Target: \",\n                                                                                    placement.width,\n                                                                                    \"x\",\n                                                                                    placement.height,\n                                                                                    \"px\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"target_url\",\n                                                                        children: \"Target URL *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"target_url\",\n                                                                        type: \"url\",\n                                                                        value: formData.target_url,\n                                                                        onChange: (e)=>handleInputChange(\"target_url\", e.target.value),\n                                                                        placeholder: \"https://example.com\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"max_impressions\",\n                                                                                children: \"Max Impressions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"max_impressions\",\n                                                                                type: \"number\",\n                                                                                value: formData.max_impressions,\n                                                                                onChange: (e)=>handleInputChange(\"max_impressions\", e.target.value),\n                                                                                placeholder: \"Unlimited\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"max_clicks\",\n                                                                                children: \"Max Clicks\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 471,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"max_clicks\",\n                                                                                type: \"number\",\n                                                                                value: formData.max_clicks,\n                                                                                onChange: (e)=>handleInputChange(\"max_clicks\", e.target.value),\n                                                                                placeholder: \"Unlimited\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 472,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"weight\",\n                                                                                children: \"Weight\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"weight\",\n                                                                                type: \"number\",\n                                                                                min: \"1\",\n                                                                                max: \"1000\",\n                                                                                value: formData.weight,\n                                                                                onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        disabled: submitting,\n                                                        children: submitting ? \"Creating...\" : selectedOption === \"new\" ? \"Create Campaign & Ad\" : \"Create Ad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Selected Placement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Name:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Page:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.page\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Dimensions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: [\n                                                                    placement.width,\n                                                                    \"x\",\n                                                                    placement.height,\n                                                                    \"px\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    placement.allowed_ad_types && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Formats:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 13\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.allowed_ad_types.join(\", \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 13\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Tips\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Use high-quality images that match the placement dimensions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Write compelling ad titles that grab attention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Set reasonable impression and click limits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Higher weight gives your ad more visibility\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n            lineNumber: 233,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n        lineNumber: 232,\n        columnNumber: 3\n    }, this);\n}\n_s(CreateAdClientWrapper, \"XcY8xky1Cnm/OHsq8iFI+4kqhCk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CreateAdClientWrapper;\nvar _c;\n$RefreshReg$(_c, \"CreateAdClientWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/ads/create/client-wrapper.tsx\n"));

/***/ })

});