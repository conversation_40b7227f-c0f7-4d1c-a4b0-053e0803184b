// app/dashboard/ads/create/page.tsx
import CreateAdClientWrapper from "./client-wrapper";

interface PageProps {
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function CreateAdPage({ searchParams }: PageProps) {
	const params = await searchParams;
	const placementId = params.placement as string;
	const campaignId = params.campaign as string;

	return <CreateAdClientWrapper placementId={placementId} campaignId={campaignId} />;
}
