"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
	Clock, 
	CheckCircle, 
	XCircle, 
	CreditCard, 
	AlertTriangle,
	Calendar,
	User,
	MessageSquare
} from "lucide-react";
import { CampaignStatusBadge, getStatusDescription, getNextSteps, type CampaignStatus } from "./campaign-status-badge";
import { formatDistanceToNow } from "date-fns";

interface CampaignStatusCardProps {
	campaign: {
		id: number;
		name: string;
		status: CampaignStatus;
		created_at: string;
		updated_at: string;
		total_budget?: number;
		rejection_reason?: string;
		admin_notes?: string;
		approved_at?: string;
		approved_by?: string;
		rejected_at?: string;
		rejected_by?: string;
		payment_status?: string;
		payment_intent_id?: string;
	};
	onPayNow?: () => void;
	onEdit?: () => void;
	showPaymentButton?: boolean;
}

export function CampaignStatusCard({ 
	campaign, 
	onPayNow, 
	onEdit,
	showPaymentButton = true 
}: CampaignStatusCardProps) {
	const status = campaign.status as CampaignStatus;
	const description = getStatusDescription(status);
	const nextSteps = getNextSteps(status);

	const formatTimestamp = (timestamp: string) => {
		return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
	};

	const renderStatusTimeline = () => {
		const events = [];

		// Campaign created
		events.push({
			icon: Calendar,
			title: "Campaign Created",
			timestamp: campaign.created_at,
			description: "Campaign submitted for review"
		});

		// Approval/Rejection
		if (campaign.approved_at) {
			events.push({
				icon: CheckCircle,
				title: "Campaign Approved",
				timestamp: campaign.approved_at,
				description: campaign.approved_by ? `Approved by ${campaign.approved_by}` : "Campaign approved by admin",
				positive: true
			});
		} else if (campaign.rejected_at) {
			events.push({
				icon: XCircle,
				title: "Campaign Rejected",
				timestamp: campaign.rejected_at,
				description: campaign.rejected_by ? `Rejected by ${campaign.rejected_by}` : "Campaign rejected by admin",
				negative: true
			});
		}

		return (
			<div className="space-y-3">
				{events.map((event, index) => {
					const Icon = event.icon;
					return (
						<div key={index} className="flex items-start gap-3">
							<div className={`p-1.5 rounded-full ${
								event.positive ? 'bg-green-100 text-green-600' :
								event.negative ? 'bg-red-100 text-red-600' :
								'bg-blue-100 text-blue-600'
							}`}>
								<Icon className="h-3 w-3" />
							</div>
							<div className="flex-1 min-w-0">
								<p className="text-sm font-medium">{event.title}</p>
								<p className="text-xs text-muted-foreground">{event.description}</p>
								<p className="text-xs text-muted-foreground">
									{formatTimestamp(event.timestamp)}
								</p>
							</div>
						</div>
					);
				})}
			</div>
		);
	};

	const renderRejectionDetails = () => {
		if (status !== "rejected" || !campaign.rejection_reason) return null;

		return (
			<Alert className="border-red-200 bg-red-50">
				<AlertTriangle className="h-4 w-4 text-red-600" />
				<AlertDescription className="text-red-800">
					<div className="space-y-2">
						<p className="font-medium">Rejection Reason:</p>
						<p className="text-sm">{campaign.rejection_reason}</p>
						{campaign.admin_notes && (
							<>
								<p className="font-medium">Admin Notes:</p>
								<p className="text-sm">{campaign.admin_notes}</p>
							</>
						)}
					</div>
				</AlertDescription>
			</Alert>
		);
	};

	const renderPaymentSection = () => {
		if (status !== "approved" || !showPaymentButton) return null;

		return (
			<div className="space-y-3">
				<Separator />
				<div className="space-y-3">
					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Ready for Payment</p>
							<p className="text-sm text-muted-foreground">
								Campaign budget: ${campaign.total_budget?.toFixed(2) || '0.00'}
							</p>
						</div>
						<Button onClick={onPayNow} className="bg-green-600 hover:bg-green-700">
							<CreditCard className="mr-2 h-4 w-4" />
							Pay Now
						</Button>
					</div>
					<Alert className="border-green-200 bg-green-50">
						<CheckCircle className="h-4 w-4 text-green-600" />
						<AlertDescription className="text-green-800">
							Your campaign has been approved! Complete payment to make it live.
						</AlertDescription>
					</Alert>
				</div>
			</div>
		);
	};

	const renderNextSteps = () => {
		return (
			<div className="space-y-2">
				<p className="font-medium text-sm">Next Steps:</p>
				<ul className="space-y-1">
					{nextSteps.map((step, index) => (
						<li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
							<span className="text-xs mt-1">•</span>
							<span>{step}</span>
						</li>
					))}
				</ul>
			</div>
		);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-start justify-between">
					<div className="space-y-1">
						<CardTitle className="flex items-center gap-2">
							Campaign Status
							<CampaignStatusBadge status={status} size="sm" />
						</CardTitle>
						<CardDescription>{description}</CardDescription>
					</div>
					{status === "rejected" && onEdit && (
						<Button variant="outline" size="sm" onClick={onEdit}>
							Edit Campaign
						</Button>
					)}
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Status Timeline */}
				<div>
					<p className="font-medium text-sm mb-3">Timeline</p>
					{renderStatusTimeline()}
				</div>

				{/* Rejection Details */}
				{renderRejectionDetails()}

				{/* Payment Section */}
				{renderPaymentSection()}

				{/* Next Steps */}
				{renderNextSteps()}

				{/* Last Updated */}
				<div className="pt-2 border-t">
					<p className="text-xs text-muted-foreground">
						Last updated {formatTimestamp(campaign.updated_at)}
					</p>
				</div>
			</CardContent>
		</Card>
	);
}
