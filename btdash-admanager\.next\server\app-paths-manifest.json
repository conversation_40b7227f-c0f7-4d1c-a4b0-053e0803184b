{"/api/admin/campaigns/[id]/approve/route": "app/api/admin/campaigns/[id]/approve/route.js", "/api/admin/campaigns/pending/route": "app/api/admin/campaigns/pending/route.js", "/api/admin/campaigns/rejected/route": "app/api/admin/campaigns/rejected/route.js", "/api/admin/campaigns/approved/route": "app/api/admin/campaigns/approved/route.js", "/api/user/campaigns/[id]/route": "app/api/user/campaigns/[id]/route.js", "/api/user/company/route": "app/api/user/company/route.js", "/api/user/ads/route": "app/api/user/ads/route.js", "/_not-found/page": "app/_not-found/page.js", "/dashboard/campaigns/page": "app/dashboard/campaigns/page.js", "/admin/page": "app/admin/page.js", "/admin/requests/page": "app/admin/requests/page.js", "/dashboard/campaigns/[id]/page": "app/dashboard/campaigns/[id]/page.js", "/admin/requests/[id]/page": "app/admin/requests/[id]/page.js"}