import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export async function GET(req: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();
		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const url = new URL(req.url);
		const searchParams = url.searchParams;
		
		const queryString = searchParams.toString();
		const apiUrl = `${process.env.API_BASE_URL}/billing/webhook-stats${queryString ? `?${queryString}` : ''}`;

		const response = await fetch(apiUrl, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
		});

		const data = await response.json();
		
		if (!response.ok) {
			throw new Error(data.message || "Failed to fetch webhook statistics");
		}

		return NextResponse.json(data);
	} catch (error: any) {
		console.error("Error fetching webhook statistics:", error);
		return NextResponse.json(
			{ error: error.message || "Failed to fetch webhook statistics" },
			{ status: 500 }
		);
	}
}
