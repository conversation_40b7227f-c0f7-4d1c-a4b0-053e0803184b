# BTDash Ad Manager - Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# AUTH0 CONFIGURATION
# =============================================================================
AUTH0_SECRET='use [openssl rand -hex 32] to generate a 32 bytes value'
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='https://your-domain.auth0.com'
AUTH0_CLIENT_ID='your-auth0-client-id'
AUTH0_CLIENT_SECRET='your-auth0-client-secret'

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================
# Stripe Publishable Key (starts with pk_test_ for test mode, pk_live_ for live)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY='pk_test_51...'

# Stripe Secret Key (starts with sk_test_ for test mode, sk_live_ for live)
STRIPE_SECRET_KEY='sk_test_51...'

# Stripe Webhook Secret (starts with whsec_)
# Get this from your Stripe Dashboard > Webhooks > [Your Webhook] > Signing secret
STRIPE_WEBHOOK_SECRET='whsec_...'

# Stripe Configuration Options
STRIPE_CURRENCY='usd'
STRIPE_COUNTRY='US'

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API Base URL
API_BASE_URL='http://localhost:3001'

# Internal API Key for secure communication between services
INTERNAL_API_KEY='your-internal-api-key'

# =============================================================================
# DATABASE CONFIGURATION (if needed for direct connections)
# =============================================================================
# DATABASE_URL='postgresql://username:password@localhost:5432/btdash_ads'

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Set to 'development' for local development, 'production' for live
NODE_ENV='development'

# Enable debug logging for Stripe operations
DEBUG_STRIPE='false'

# =============================================================================
# OPTIONAL: ADDITIONAL SERVICES
# =============================================================================
# Google AdSense (if using fallback ads)
# NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID='ca-pub-...'

# Monitoring and Analytics
# SENTRY_DSN='https://...'
# GOOGLE_ANALYTICS_ID='GA-...'

# =============================================================================
# STRIPE SETUP INSTRUCTIONS
# =============================================================================
# 1. Create a Stripe account at https://stripe.com
# 2. Get your API keys from https://dashboard.stripe.com/apikeys
# 3. Create a webhook endpoint at https://dashboard.stripe.com/webhooks
#    - Endpoint URL: https://your-domain.com/api/stripe/webhook
#    - Events to send: payment_intent.succeeded, payment_intent.payment_failed
# 4. Copy the webhook signing secret to STRIPE_WEBHOOK_SECRET
# 5. For production, complete Stripe account verification and use live keys

# =============================================================================
# SECURITY NOTES
# =============================================================================
# - Never commit this file with real values to version control
# - Use different keys for development and production
# - Regularly rotate your API keys
# - Monitor webhook endpoints for suspicious activity
