{"c": ["app/layout", "app/dashboard/ads/create/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/dashboard/ads/create/page.tsx", "(app-pages-browser)/./components/ui/separator.tsx", "(app-pages-browser)/./node_modules/@radix-ui/react-separator/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbenji%5C%5CDesktop%5C%5CG_PROG%5C%5CNicolas%5C%5Cbtdash-ecosystem%5C%5Cbtdash-admanager%5C%5Capp%5C%5Cdashboard%5C%5Cads%5C%5Ccreate%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}