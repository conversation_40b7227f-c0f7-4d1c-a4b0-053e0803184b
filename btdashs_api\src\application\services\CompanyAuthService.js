const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class CompanyAuthService {
	/**
	 * Check if user has access to a company
	 * @param {number} userId - User ID
	 * @param {number} companyId - Company ID
	 * @param {Array<string>} allowedRoles - Allowed roles (default: all roles)
	 * @returns {Promise<boolean>} Whether user has access
	 */
	async hasCompanyAccess(userId, companyId, allowedRoles = ["owner", "admin", "member"]) {
		try {
			if (!userId || !companyId) {
				return false;
			}

			const userCompany = await db("dtm_base.user_company")
				.where({ user_id: userId, company_id: companyId })
				.first();

			if (!userCompany) {
				return false;
			}

			return allowedRoles.includes(userCompany.role);
		} catch (error) {
			logger.error("Error checking company access", { error, userId, companyId });
			return false;
		}
	}

	/**
	 * Get all companies a user has access to
	 * @param {number} userId - User ID
	 * @param {Array<string>} allowedRoles - Allowed roles (default: all roles)
	 * @returns {Promise<Array>} Array of company IDs
	 */
	async getUserCompanyIds(userId, allowedRoles = ["owner", "admin", "member"]) {
		try {
			if (!userId) {
				return [];
			}

			const userCompanies = await db("dtm_base.user_company")
				.where({ user_id: userId })
				.whereIn("role", allowedRoles)
				.select("company_id");

			return userCompanies.map(uc => uc.company_id);
		} catch (error) {
			logger.error("Error getting user company IDs", { error, userId });
			return [];
		}
	}

	/**
	 * Check if user can access invoice based on company association
	 * @param {number} userId - User ID
	 * @param {number} invoiceId - Invoice ID
	 * @returns {Promise<boolean>} Whether user can access the invoice
	 */
	async canAccessInvoice(userId, invoiceId) {
		try {
			if (!userId || !invoiceId) {
				return false;
			}

			// Get invoice with company information
			const invoice = await db("dtm_ads.invoices")
				.where({ id: invoiceId })
				.select("advertiser_id", "manager_id")
				.first();

			if (!invoice) {
				return false;
			}

			// Check if user is the manager (direct access)
			if (invoice.manager_id === userId) {
				return true;
			}

			// Check if user has access to the advertiser company
			if (invoice.advertiser_id) {
				return await this.hasCompanyAccess(userId, invoice.advertiser_id);
			}

			return false;
		} catch (error) {
			logger.error("Error checking invoice access", { error, userId, invoiceId });
			return false;
		}
	}

	/**
	 * Check if user can access campaign based on company association
	 * @param {number} userId - User ID
	 * @param {number} campaignId - Campaign ID
	 * @returns {Promise<boolean>} Whether user can access the campaign
	 */
	async canAccessCampaign(userId, campaignId) {
		try {
			if (!userId || !campaignId) {
				return false;
			}

			// Get campaign with company information
			const campaign = await db("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.select("advertiser_id", "manager_id")
				.first();

			if (!campaign) {
				return false;
			}

			// Check if user is the manager (direct access)
			if (campaign.manager_id === userId) {
				return true;
			}

			// Check if user has access to the advertiser company
			if (campaign.advertiser_id) {
				return await this.hasCompanyAccess(userId, campaign.advertiser_id);
			}

			return false;
		} catch (error) {
			logger.error("Error checking campaign access", { error, userId, campaignId });
			return false;
		}
	}

	/**
	 * Get all invoices user can access based on company associations
	 * @param {number} userId - User ID
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of invoices
	 */
	async getUserAccessibleInvoices(userId, options = {}) {
		try {
			const { status, limit = 50, offset = 0 } = options;

			// Get all company IDs user has access to
			const companyIds = await this.getUserCompanyIds(userId);

			let query = db("dtm_ads.invoices")
				.where(function() {
					// User is the manager OR user has access to the advertiser company
					this.where("manager_id", userId);
					if (companyIds.length > 0) {
						this.orWhereIn("advertiser_id", companyIds);
					}
				})
				.orderBy("created_at", "desc")
				.limit(limit)
				.offset(offset);

			if (status) {
				query = query.where({ status });
			}

			const invoices = await query;
			return invoices;
		} catch (error) {
			logger.error("Error getting user accessible invoices", { error, userId, options });
			throw error;
		}
	}

	/**
	 * Get user's role for a specific company
	 * @param {number} userId - User ID
	 * @param {number} companyId - Company ID
	 * @returns {Promise<string|null>} User role or null if no access
	 */
	async getUserCompanyRole(userId, companyId) {
		try {
			if (!userId || !companyId) {
				return null;
			}

			const userCompany = await db("dtm_base.user_company")
				.where({ user_id: userId, company_id: companyId })
				.select("role")
				.first();

			return userCompany ? userCompany.role : null;
		} catch (error) {
			logger.error("Error getting user company role", { error, userId, companyId });
			return null;
		}
	}

	/**
	 * Check if user can manage company data (owner or admin)
	 * @param {number} userId - User ID
	 * @param {number} companyId - Company ID
	 * @returns {Promise<boolean>} Whether user can manage company
	 */
	async canManageCompany(userId, companyId) {
		return await this.hasCompanyAccess(userId, companyId, ["owner", "admin"]);
	}

	/**
	 * Check if user is company owner
	 * @param {number} userId - User ID
	 * @param {number} companyId - Company ID
	 * @returns {Promise<boolean>} Whether user is company owner
	 */
	async isCompanyOwner(userId, companyId) {
		return await this.hasCompanyAccess(userId, companyId, ["owner"]);
	}
}

module.exports = CompanyAuthService;
