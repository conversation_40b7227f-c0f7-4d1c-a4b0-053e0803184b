const { asyncHandler } = require("../../middleware/errorHandler");
const InvoiceService = require("../../application/services/InvoiceService");
const PDFInvoiceService = require("../../application/services/PDFInvoiceService");
const CompanyAuthService = require("../../application/services/CompanyAuthService");
const { sendSuccess, sendError } = require("../../utils/responseWrapper");
const logger = require("../../../logger");
const db = require("../../infrastructure/database/knex");
const path = require("path");
const fs = require("fs");

/**
 * Get invoices for the authenticated user based on company access
 */
const getUserInvoices = asyncHandler(async (req, res) => {
	const userId = req.user.id;
	const { status, limit = 50, offset = 0 } = req.query;

	const companyAuthService = new CompanyAuthService();
	const invoices = await companyAuthService.getUserAccessibleInvoices(userId, {
		status,
		limit: parseInt(limit),
		offset: parseInt(offset),
	});

	return sendSuccess(res, { invoices }, "Invoices retrieved successfully");
});

/**
 * Get specific invoice details
 */
const getInvoiceDetails = asyncHandler(async (req, res) => {
	const userId = req.user.id;
	const { id: invoiceId } = req.params;

	// Check if user can access this invoice based on company association
	const companyAuthService = new CompanyAuthService();
	const canAccess = await companyAuthService.canAccessInvoice(userId, invoiceId);

	if (!canAccess) {
		return sendError(res, "Invoice not found or access denied", null, 404);
	}

	const invoiceService = new InvoiceService();
	const invoiceWithLineItems = await invoiceService.getInvoiceWithLineItems(invoiceId);

	return sendSuccess(res, { invoice: invoiceWithLineItems }, "Invoice details retrieved successfully");
});

/**
 * Generate PDF for invoice
 */
const generateInvoicePDF = asyncHandler(async (req, res) => {
	const userId = req.user.id;
	const { id: invoiceId } = req.params;

	// Check if user can access this invoice based on company association
	const companyAuthService = new CompanyAuthService();
	const canAccess = await companyAuthService.canAccessInvoice(userId, invoiceId);

	if (!canAccess) {
		return sendError(res, "Invoice not found or access denied", null, 404);
	}

	const pdfService = new PDFInvoiceService();

	try {
		// Check if PDF already exists
		let pdfPath = await pdfService.getInvoicePDFPath(invoiceId);

		// Generate PDF if it doesn't exist
		if (!pdfPath) {
			pdfPath = await pdfService.generateInvoicePDF(invoiceId);
		}

		// Convert to URL
		const relativePath = path.relative(process.cwd(), pdfPath);
		const pdfUrl = `/${relativePath.replace(/\\/g, "/")}`;

		return sendSuccess(
			res,
			{
				pdf_url: pdfUrl,
				invoice_number: invoice.invoice_number,
			},
			"PDF generated successfully"
		);
	} catch (error) {
		logger.error("Error generating invoice PDF", { error, invoiceId, userId });
		return sendError(res, "Failed to generate PDF", null, 500);
	}
});

/**
 * Download invoice PDF
 */
const downloadInvoicePDF = asyncHandler(async (req, res) => {
	const userId = req.user.id;
	const { id: invoiceId } = req.params;

	// Check if user can access this invoice based on company association
	const companyAuthService = new CompanyAuthService();
	const canAccess = await companyAuthService.canAccessInvoice(userId, invoiceId);

	if (!canAccess) {
		return sendError(res, "Invoice not found or access denied", null, 404);
	}

	const pdfService = new PDFInvoiceService();

	try {
		let pdfPath = await pdfService.getInvoicePDFPath(invoiceId);

		// Generate PDF if it doesn't exist
		if (!pdfPath) {
			pdfPath = await pdfService.generateInvoicePDF(invoiceId);
		}

		// Check if file exists
		if (!fs.existsSync(pdfPath)) {
			return sendError(res, "PDF file not found", null, 404);
		}

		// Set headers for file download
		const fileName = `invoice-${invoice.invoice_number}.pdf`;
		res.setHeader("Content-Type", "application/pdf");
		res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`);

		// Stream the file
		const fileStream = fs.createReadStream(pdfPath);
		fileStream.pipe(res);

		fileStream.on("error", (error) => {
			logger.error("Error streaming PDF file", { error, invoiceId, pdfPath });
			if (!res.headersSent) {
				return sendError(res, "Error downloading file", null, 500);
			}
		});
	} catch (error) {
		logger.error("Error downloading invoice PDF", { error, invoiceId, userId });
		return sendError(res, "Failed to download PDF", null, 500);
	}
});

/**
 * Create invoice for campaign (internal use)
 */
const createCampaignInvoice = asyncHandler(async (req, res) => {
	const {
		managerId,
		advertiserId,
		customerId,
		campaignId,
		amount,
		currency = "USD",
		description,
		lineItems = [],
	} = req.body;

	if (!managerId || !campaignId || !amount) {
		return sendError(res, "Missing required fields", null, 400);
	}

	const invoiceService = new InvoiceService();

	try {
		const invoice = await invoiceService.createInvoice({
			managerId,
			advertiserId,
			customerId,
			campaignId,
			amount,
			currency,
			description: description || `Campaign payment for Campaign #${campaignId}`,
			lineItems,
		});

		return sendSuccess(res, { invoice }, "Invoice created successfully");
	} catch (error) {
		logger.error("Error creating campaign invoice", { error, req: req.body });
		return sendError(res, "Failed to create invoice", null, 500);
	}
});

/**
 * Finalize invoice (internal use)
 */
const finalizeInvoice = asyncHandler(async (req, res) => {
	const { id: invoiceId } = req.params;

	const invoiceService = new InvoiceService();

	try {
		const invoice = await invoiceService.finalizeInvoice(invoiceId);
		return sendSuccess(res, { invoice }, "Invoice finalized successfully");
	} catch (error) {
		logger.error("Error finalizing invoice", { error, invoiceId });
		return sendError(res, error.message, null, 400);
	}
});

/**
 * Mark invoice as paid (internal use - called by webhook)
 */
const markInvoicePaid = asyncHandler(async (req, res) => {
	const { paymentIntentId, paymentMethod = "stripe" } = req.body;

	if (!paymentIntentId) {
		return sendError(res, "Payment intent ID is required", null, 400);
	}

	const invoiceService = new InvoiceService();

	try {
		const invoice = await invoiceService.markInvoicePaid(paymentIntentId, paymentMethod);
		return sendSuccess(res, { invoice }, "Invoice marked as paid");
	} catch (error) {
		logger.error("Error marking invoice as paid", { error, paymentIntentId });
		return sendError(res, error.message, null, 400);
	}
});

/**
 * Associate payment intent with invoice (internal use)
 */
const associatePaymentIntent = asyncHandler(async (req, res) => {
	const { invoiceId, paymentIntentId } = req.body;

	if (!invoiceId || !paymentIntentId) {
		return sendError(res, "Invoice ID and payment intent ID are required", null, 400);
	}

	const invoiceService = new InvoiceService();

	try {
		const invoice = await invoiceService.associatePaymentIntent(invoiceId, paymentIntentId);
		return sendSuccess(res, { invoice }, "Payment intent associated with invoice");
	} catch (error) {
		logger.error("Error associating payment intent", { error, invoiceId, paymentIntentId });
		return sendError(res, "Failed to associate payment intent", null, 500);
	}
});

/**
 * Get invoice statistics for admin
 */
const getInvoiceStats = asyncHandler(async (req, res) => {
	const { startDate, endDate } = req.query;

	try {
		let query = db("dtm_ads.invoices");

		if (startDate && endDate) {
			query = query.whereBetween("created_at", [startDate, endDate]);
		}

		const stats = await query
			.select(
				db.raw("COUNT(*) as total_invoices"),
				db.raw("COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices"),
				db.raw("COUNT(CASE WHEN status = 'open' THEN 1 END) as open_invoices"),
				db.raw("SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_revenue"),
				db.raw("AVG(total_amount) as average_invoice_amount")
			)
			.first();

		return sendSuccess(res, { stats }, "Invoice statistics retrieved successfully");
	} catch (error) {
		logger.error("Error getting invoice statistics", { error });
		return sendError(res, "Failed to get invoice statistics", null, 500);
	}
});

module.exports = {
	getUserInvoices,
	getInvoiceDetails,
	generateInvoicePDF,
	downloadInvoicePDF,
	createCampaignInvoice,
	finalizeInvoice,
	markInvoicePaid,
	associatePaymentIntent,
	getInvoiceStats,
};
