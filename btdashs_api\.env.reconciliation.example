# Payment Reconciliation Configuration
# Copy this file to .env.local and configure for your environment

# =============================================================================
# RECONCILIATION CONFIGURATION
# =============================================================================

# Enable/disable payment reconciliation
PAYMENT_RECONCILIATION_ENABLED=true

# Reconciliation job schedules (cron format)
RECONCILIATION_DAILY_SCHEDULE=0 2 * * *      # Daily at 2 AM
RECONCILIATION_WEEKLY_SCHEDULE=0 3 * * 1     # Weekly on Monday at 3 AM
RECONCILIATION_MONTHLY_SCHEDULE=0 4 1 * *    # Monthly on 1st at 4 AM

# Timezone for scheduled jobs
TZ=UTC

# =============================================================================
# RECONCILIATION THRESHOLDS
# =============================================================================

# Alert thresholds for discrepancy rates (percentage)
RECONCILIATION_ALERT_THRESHOLD_WARNING=2     # Alert if >2% discrepancies
RECONCILIATION_ALERT_THRESHOLD_CRITICAL=5    # Critical alert if >5% discrepancies

# Maximum allowed amount variance (in cents)
RECONCILIATION_AMOUNT_VARIANCE_THRESHOLD=100  # $1.00 variance allowed

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================

# Stripe API configuration (required for reconciliation)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Stripe API rate limiting
STRIPE_API_RATE_LIMIT=100                    # Requests per second
STRIPE_API_BATCH_SIZE=100                    # Records per API call

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database connection settings (should match main app)
DATABASE_URL=postgresql://user:password@localhost:5432/btdash_db

# Query timeout for large reconciliation operations (milliseconds)
RECONCILIATION_QUERY_TIMEOUT=300000         # 5 minutes

# =============================================================================
# REPORTING CONFIGURATION
# =============================================================================

# Report retention period (days)
RECONCILIATION_REPORT_RETENTION_DAYS=90

# Export formats
RECONCILIATION_EXPORT_FORMATS=csv,json

# Report storage location
RECONCILIATION_REPORTS_PATH=./reports/reconciliation

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level for reconciliation operations
RECONCILIATION_LOG_LEVEL=info

# Enable detailed reconciliation logging
RECONCILIATION_VERBOSE_LOGGING=false

# Log reconciliation performance metrics
RECONCILIATION_LOG_PERFORMANCE=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Disable reconciliation in development
DISABLE_RECONCILIATION_IN_DEV=false

# Test mode (uses smaller date ranges)
RECONCILIATION_TEST_MODE=false

# Mock Stripe API responses for testing
RECONCILIATION_MOCK_STRIPE=false

# =============================================================================
# PRODUCTION RECOMMENDATIONS
# =============================================================================

# For production environments:
# 1. Enable reconciliation with appropriate schedules
# 2. Set conservative alert thresholds
# 3. Use production Stripe keys
# 4. Configure proper database timeouts
# 5. Enable performance logging
# 6. Set up report retention policies
# 7. Monitor reconciliation job health
# 8. Test reconciliation processes regularly

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# High-volume environment:
# RECONCILIATION_DAILY_SCHEDULE=0 1,13 * * *    # Twice daily
# STRIPE_API_BATCH_SIZE=50                       # Smaller batches
# RECONCILIATION_QUERY_TIMEOUT=600000           # 10 minutes

# Low-volume environment:
# RECONCILIATION_DAILY_SCHEDULE=0 3 * * *       # Once daily
# RECONCILIATION_WEEKLY_SCHEDULE=0 4 * * 0      # Weekly on Sunday
# STRIPE_API_BATCH_SIZE=200                      # Larger batches

# Development environment:
# PAYMENT_RECONCILIATION_ENABLED=false
# RECONCILIATION_TEST_MODE=true
# RECONCILIATION_MOCK_STRIPE=true
# RECONCILIATION_VERBOSE_LOGGING=true

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# To test the reconciliation system:
# 1. Set RECONCILIATION_TEST_MODE=true
# 2. Use test Stripe keys
# 3. Run manual reconciliation via API
# 4. Verify reports are generated correctly
# 5. Test export functionality

# Test commands:
# curl -X POST http://localhost:3001/api/reconciliation/run \
#      -H "Authorization: Bearer <jwt_token>" \
#      -H "Content-Type: application/json" \
#      -d '{"startDate": "2024-01-01", "endDate": "2024-01-02"}'

# curl -X POST http://localhost:3001/api/reconciliation/quick-check \
#      -H "Authorization: Bearer <jwt_token>"

# curl -X GET http://localhost:3001/api/reconciliation/stats \
#      -H "Authorization: Bearer <jwt_token>"
