const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class WebhookRetryService {
	constructor() {
		this.maxRetries = 5;
		this.baseDelay = 1000; // 1 second
		this.maxDelay = 30000; // 30 seconds
	}

	/**
	 * Process webhook with retry logic
	 * @param {string} eventType - Stripe event type
	 * @param {Object} eventData - Stripe event data
	 * @param {Function} processor - Function to process the webhook
	 * @returns {Promise<boolean>} Success status
	 */
	async processWithRetry(eventType, eventData, processor) {
		const webhookLog = await this.createWebhookLog(eventType, eventData);
		
		for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
			try {
				await processor(eventData);
				
				await this.updateWebhookLog(webhookLog.id, {
					status: 'success',
					attempts: attempt,
					completed_at: new Date(),
				});

				logger.info("Webhook processed successfully", {
					eventType,
					eventId: eventData.id,
					attempt,
					webhookLogId: webhookLog.id,
				});

				return true;
			} catch (error) {
				const isLastAttempt = attempt === this.maxRetries;
				
				await this.updateWebhookLog(webhookLog.id, {
					status: isLastAttempt ? 'failed' : 'retrying',
					attempts: attempt,
					last_error: error.message,
					last_error_at: new Date(),
				});

				logger.error("Webhook processing failed", {
					eventType,
					eventId: eventData.id,
					attempt,
					maxRetries: this.maxRetries,
					error: error.message,
					webhookLogId: webhookLog.id,
					isLastAttempt,
				});

				if (isLastAttempt) {
					await this.handleFinalFailure(eventType, eventData, error);
					return false;
				}

				const delay = this.calculateDelay(attempt);
				await this.sleep(delay);
			}
		}

		return false;
	}

	/**
	 * Create webhook log entry
	 * @param {string} eventType - Stripe event type
	 * @param {Object} eventData - Stripe event data
	 * @returns {Promise<Object>} Webhook log record
	 */
	async createWebhookLog(eventType, eventData) {
		const [webhookLog] = await db("dtm_ads.webhook_logs")
			.insert({
				event_id: eventData.id,
				event_type: eventType,
				event_data: JSON.stringify(eventData),
				status: 'processing',
				attempts: 0,
				created_at: new Date(),
				updated_at: new Date(),
			})
			.returning("*");

		return webhookLog;
	}

	/**
	 * Update webhook log entry
	 * @param {number} logId - Webhook log ID
	 * @param {Object} updates - Updates to apply
	 */
	async updateWebhookLog(logId, updates) {
		await db("dtm_ads.webhook_logs")
			.where({ id: logId })
			.update({
				...updates,
				updated_at: new Date(),
			});
	}

	/**
	 * Calculate exponential backoff delay
	 * @param {number} attempt - Current attempt number
	 * @returns {number} Delay in milliseconds
	 */
	calculateDelay(attempt) {
		const exponentialDelay = this.baseDelay * Math.pow(2, attempt - 1);
		const jitter = Math.random() * 0.1 * exponentialDelay;
		return Math.min(exponentialDelay + jitter, this.maxDelay);
	}

	/**
	 * Sleep for specified duration
	 * @param {number} ms - Milliseconds to sleep
	 */
	sleep(ms) {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Handle final failure after all retries exhausted
	 * @param {string} eventType - Stripe event type
	 * @param {Object} eventData - Stripe event data
	 * @param {Error} error - Final error
	 */
	async handleFinalFailure(eventType, eventData, error) {
		try {
			await db("dtm_ads.payment_failures")
				.insert({
					event_id: eventData.id,
					event_type: eventType,
					payment_intent_id: eventData.data?.object?.id,
					failure_reason: error.message,
					event_data: JSON.stringify(eventData),
					created_at: new Date(),
				});

			logger.error("Webhook processing failed permanently", {
				eventType,
				eventId: eventData.id,
				error: error.message,
				maxRetries: this.maxRetries,
			});

			// TODO: Send alert to monitoring system
		} catch (logError) {
			logger.error("Failed to log payment failure", {
				originalError: error.message,
				logError: logError.message,
				eventType,
				eventId: eventData.id,
			});
		}
	}

	/**
	 * Get webhook processing statistics
	 * @param {Object} options - Query options
	 * @returns {Promise<Object>} Statistics
	 */
	async getWebhookStats(options = {}) {
		const { startDate, endDate } = options;

		let query = db("dtm_ads.webhook_logs");

		if (startDate && endDate) {
			query = query.whereBetween("created_at", [startDate, endDate]);
		}

		const stats = await query
			.select(
				db.raw("COUNT(*) as total_webhooks"),
				db.raw("COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_webhooks"),
				db.raw("COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_webhooks"),
				db.raw("COUNT(CASE WHEN status = 'retrying' THEN 1 END) as retrying_webhooks"),
				db.raw("AVG(attempts) as average_attempts"),
				db.raw("MAX(attempts) as max_attempts")
			)
			.first();

		return {
			...stats,
			success_rate: stats.total_webhooks > 0 
				? (stats.successful_webhooks / stats.total_webhooks * 100).toFixed(2)
				: 0,
		};
	}

	/**
	 * Get recent webhook failures
	 * @param {number} limit - Number of failures to return
	 * @returns {Promise<Array>} Recent failures
	 */
	async getRecentFailures(limit = 10) {
		return await db("dtm_ads.webhook_logs")
			.where({ status: 'failed' })
			.orderBy("created_at", "desc")
			.limit(limit)
			.select("*");
	}

	/**
	 * Retry failed webhook manually
	 * @param {number} webhookLogId - Webhook log ID
	 * @param {Function} processor - Function to process the webhook
	 * @returns {Promise<boolean>} Success status
	 */
	async retryFailedWebhook(webhookLogId, processor) {
		const webhookLog = await db("dtm_ads.webhook_logs")
			.where({ id: webhookLogId })
			.first();

		if (!webhookLog) {
			throw new Error("Webhook log not found");
		}

		if (webhookLog.status !== 'failed') {
			throw new Error("Only failed webhooks can be retried");
		}

		const eventData = JSON.parse(webhookLog.event_data);
		
		try {
			await processor(eventData);
			
			await this.updateWebhookLog(webhookLogId, {
				status: 'success',
				attempts: webhookLog.attempts + 1,
				completed_at: new Date(),
			});

			logger.info("Failed webhook retried successfully", {
				webhookLogId,
				eventType: webhookLog.event_type,
				eventId: webhookLog.event_id,
			});

			return true;
		} catch (error) {
			await this.updateWebhookLog(webhookLogId, {
				attempts: webhookLog.attempts + 1,
				last_error: error.message,
				last_error_at: new Date(),
			});

			logger.error("Manual webhook retry failed", {
				webhookLogId,
				eventType: webhookLog.event_type,
				eventId: webhookLog.event_id,
				error: error.message,
			});

			throw error;
		}
	}
}

module.exports = WebhookRetryService;
