const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const routes = require("./src/presentation/routes");
const { errorHandler, notFoundHandler } = require("./src/middleware/errorHandler");
const { sendSuccess } = require("./src/utils/responseWrapper");
const { requestMetricsMiddleware, errorMetricsMiddleware } = require("./src/middleware/metricsMiddleware");
const logger = require("./logger");
require("module-alias/register");

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Middleware
app.use(
	cors({
		origin:
			process.env.NODE_ENV === "production"
				? ["https://dynamictaomarketcap.com", "https://admanager.dynamictaomarketcap.com"]
				: ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
		credentials: true,
	})
);

app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Metrics collection middleware
app.use(requestMetricsMiddleware);

// Request logging middleware
app.use((req, res, next) => {
	logger.info(`${req.method} ${req.path}`, {
		ip: req.ip,
		userAgent: req.get("User-Agent"),
		body: req.method !== "GET" ? req.body : undefined,
	});
	next();
});

// Register routes
app.use("/api", routes);

// API Documentation routes
const docsRoutes = require("./src/presentation/routes/docs");
app.use("/api/docs", docsRoutes);

// Monitoring routes
const monitoringRoutes = require("./src/presentation/routes/monitoring");
app.use("/monitoring", monitoringRoutes);

// Health check endpoint with standardized response
app.get("/health", (req, res) => {
	sendSuccess(
		res,
		{
			status: "ok",
			timestamp: new Date().toISOString(),
			uptime: process.uptime(),
			environment: process.env.NODE_ENV || "development",
		},
		"BTDashs API is running"
	);
});

// Handle 404 errors
app.use(notFoundHandler);

// Error metrics middleware
app.use(errorMetricsMiddleware);

// Global error handling middleware
app.use(errorHandler);

// Initialize the scheduler service (includes network update jobs if NETWORK_UPDATES_ENABLED is true)
const { initializeScheduler } = require("./src/infrastructure/scheduler");
initializeScheduler();

// Initialize payment monitoring jobs
if (process.env.PAYMENT_MONITORING_ENABLED === "true") {
	const paymentMonitoringJob = require("./src/infrastructure/jobs/PaymentMonitoringJob");
	paymentMonitoringJob.start();
	logger.info("Payment monitoring jobs initialized");
}

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
	logger.info(`Server is running on http://localhost:${PORT}`);
	logger.info(`Environment: ${process.env.NODE_ENV || "development"}`);
});
