/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/campaigns/[id]/route";
exports.ids = ["app/api/user/campaigns/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/user/campaigns/[id]/route.ts":
/*!**********************************************!*\
  !*** ./app/api/user/campaigns/[id]/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// app/api/placements/[id]/route.ts\n\n\nasync function GET(request, { params }) {\n    try {\n        const paramsData = await params;\n        const id = paramsData.id;\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${id}`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch campaign details\");\n        const result = await response.json();\n        // Handle standardized response format\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: result.data,\n                message: result.message\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: result.message,\n                errors: result.errors\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/user/campaigns/[id] - Update campaign\nasync function PUT(request, { params }) {\n    try {\n        const paramsData = await params;\n        const id = paramsData.id;\n        const body = await request.json();\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        // Get user info and company info\n        const userResponse = await fetch(`${process.env.API_BASE_URL}/user/me`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!userResponse.ok) {\n            throw new Error(\"Failed to get user information\");\n        }\n        const userResult = await userResponse.json();\n        const userId = userResult.success ? userResult.data.id : null;\n        if (!userId) {\n            throw new Error(\"User ID not found\");\n        }\n        // Get user's company info for advertiser_id\n        const companyResponse = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        const companyResult = await companyResponse.json();\n        const companyId = companyResult.success ? companyResult.data.id : null;\n        if (!companyId) {\n            throw new Error(`Failed to get company information`);\n        }\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${id}`, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            },\n            body: JSON.stringify({\n                advertiser_id: body.advertiser_id || companyId,\n                manager_id: userId,\n                name: body.name,\n                description: body.description,\n                total_budget: body.total_budget || null,\n                budget_cpc: body.budget_cpc || null,\n                budget_cpm: body.budget_cpm || null,\n                start_date: body.start_date,\n                end_date: body.end_date\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Failed to update campaign: ${errorText}`);\n        }\n        const result = await response.json();\n        // Handle standardized response format\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: result.data,\n                message: result.message\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: result.message,\n                errors: result.errors\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/user/campaigns/[id] - Delete campaign\nasync function DELETE(request, { params }) {\n    try {\n        const paramsData = await params;\n        const id = paramsData.id;\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Failed to delete campaign: ${errorText}`);\n        }\n        const result = await response.json();\n        // Handle standardized response format\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: result.data,\n                message: result.message\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: result.message,\n                errors: result.errors\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/campaigns/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/campaigns/[id]/route.ts */ \"(rsc)/./app/api/user/campaigns/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/campaigns/[id]/route\",\n        pathname: \"/api/user/campaigns/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/campaigns/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\api\\\\user\\\\campaigns\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();