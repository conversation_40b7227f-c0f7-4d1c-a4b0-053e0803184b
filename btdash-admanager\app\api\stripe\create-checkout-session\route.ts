import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import stripe from '@/lib/stripe-server';

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { amount, campaignId, campaignName } = await request.json();

    // Validate input
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    if (!campaignId) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }

    // Create Stripe Checkout Session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `Campaign Payment: ${campaignName || `Campaign #${campaignId}`}`,
              description: `Payment for advertising campaign`,
            },
            unit_amount: Math.round(amount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXTAUTH_URL || process.env.AUTH0_BASE_URL}/dashboard/campaigns/${campaignId}?payment=success`,
      cancel_url: `${process.env.NEXTAUTH_URL || process.env.AUTH0_BASE_URL}/dashboard/campaigns/${campaignId}?payment=cancelled`,
      metadata: {
        campaignId: campaignId.toString(),
        userId: session.user.sub,
        type: 'campaign_payment',
      },
      customer_email: session.user.email,
    });

    // Store payment session in database for tracking
    try {
      const response = await fetch(`${process.env.API_BASE_URL}/billing/payment-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`,
          'X-Internal-Key': process.env.INTERNAL_API_KEY || '',
        },
        body: JSON.stringify({
          payment_intent_id: checkoutSession.payment_intent as string,
          campaign_id: campaignId,
          amount: amount,
          status: 'pending',
          currency: 'usd',
          session_id: checkoutSession.id,
        }),
      });

      if (!response.ok) {
        console.error('Failed to store payment session in database');
        // Continue anyway, as the checkout session was created successfully
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Continue anyway
    }

    return NextResponse.json({
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to create checkout session';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
