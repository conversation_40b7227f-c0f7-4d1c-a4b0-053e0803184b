#!/usr/bin/env node

/**
 * Test script for company-based invoice authorization
 * 
 * This script tests the new authorization logic where users can access
 * invoices for any campaigns belonging to companies they are associated with.
 * 
 * Usage: node test_company_authorization.js
 */

const db = require("./src/infrastructure/database/knex");
const CompanyAuthService = require("./src/application/services/CompanyAuthService");
const InvoiceService = require("./src/application/services/InvoiceService");

async function setupTestData() {
	console.log("🔧 Setting up test data...");

	// Create test users
	const [user1] = await db("dtm_base.users")
		.insert({
			email: "<EMAIL>",
			first_name: "User",
			last_name: "One",
			created_at: new Date(),
			updated_at: new Date(),
		})
		.returning("*");

	const [user2] = await db("dtm_base.users")
		.insert({
			email: "<EMAIL>",
			first_name: "User",
			last_name: "Two",
			created_at: new Date(),
			updated_at: new Date(),
		})
		.returning("*");

	// Create test company
	const [company] = await db("dtm_base.companies")
		.insert({
			name: "Test Company Inc",
			website_url: "https://testcompany.com",
			created_at: new Date(),
			updated_at: new Date(),
		})
		.returning("*");

	// Associate both users with the company
	await db("dtm_base.user_company").insert([
		{
			user_id: user1.id,
			company_id: company.id,
			role: "owner",
			created_at: new Date(),
			updated_at: new Date(),
		},
		{
			user_id: user2.id,
			company_id: company.id,
			role: "member",
			created_at: new Date(),
			updated_at: new Date(),
		},
	]);

	// Create test campaign (managed by user1, for company)
	const [campaign] = await db("dtm_ads.ad_campaigns")
		.insert({
			name: "Test Campaign",
			manager_id: user1.id,
			advertiser_id: company.id,
			total_budget: 1000.00,
			status: "approved",
			created_at: new Date(),
			updated_at: new Date(),
		})
		.returning("*");

	// Create test invoice
	const invoiceService = new InvoiceService();
	const invoice = await invoiceService.createInvoice({
		managerId: user1.id,
		advertiserId: company.id,
		campaignId: campaign.id,
		amount: 1000.00,
		currency: "USD",
		description: "Test Campaign Payment",
		lineItems: [
			{
				description: "Test Campaign - Total Budget",
				quantity: 1,
				unitPrice: 1000.00,
			},
		],
	});

	console.log("✅ Test data created:");
	console.log(`   User 1 ID: ${user1.id} (owner)`);
	console.log(`   User 2 ID: ${user2.id} (member)`);
	console.log(`   Company ID: ${company.id}`);
	console.log(`   Campaign ID: ${campaign.id}`);
	console.log(`   Invoice ID: ${invoice.id}`);

	return { user1, user2, company, campaign, invoice };
}

async function testCompanyAccess(testData) {
	console.log("\n🔍 Testing company access...");

	const companyAuthService = new CompanyAuthService();
	const { user1, user2, company } = testData;

	// Test user1 access (owner)
	const user1Access = await companyAuthService.hasCompanyAccess(user1.id, company.id);
	console.log(`   User 1 company access: ${user1Access ? "✅ PASS" : "❌ FAIL"}`);

	// Test user2 access (member)
	const user2Access = await companyAuthService.hasCompanyAccess(user2.id, company.id);
	console.log(`   User 2 company access: ${user2Access ? "✅ PASS" : "❌ FAIL"}`);

	// Test user roles
	const user1Role = await companyAuthService.getUserCompanyRole(user1.id, company.id);
	const user2Role = await companyAuthService.getUserCompanyRole(user2.id, company.id);
	console.log(`   User 1 role: ${user1Role} (expected: owner)`);
	console.log(`   User 2 role: ${user2Role} (expected: member)`);

	return user1Access && user2Access && user1Role === "owner" && user2Role === "member";
}

async function testInvoiceAccess(testData) {
	console.log("\n📄 Testing invoice access...");

	const companyAuthService = new CompanyAuthService();
	const { user1, user2, invoice } = testData;

	// Test user1 access (campaign manager)
	const user1InvoiceAccess = await companyAuthService.canAccessInvoice(user1.id, invoice.id);
	console.log(`   User 1 invoice access: ${user1InvoiceAccess ? "✅ PASS" : "❌ FAIL"} (manager)`);

	// Test user2 access (company member)
	const user2InvoiceAccess = await companyAuthService.canAccessInvoice(user2.id, invoice.id);
	console.log(`   User 2 invoice access: ${user2InvoiceAccess ? "✅ PASS" : "❌ FAIL"} (company member)`);

	return user1InvoiceAccess && user2InvoiceAccess;
}

async function testInvoiceRetrieval(testData) {
	console.log("\n📋 Testing invoice retrieval...");

	const companyAuthService = new CompanyAuthService();
	const { user1, user2, invoice } = testData;

	// Test user1 accessible invoices
	const user1Invoices = await companyAuthService.getUserAccessibleInvoices(user1.id);
	console.log(`   User 1 accessible invoices: ${user1Invoices.length} (expected: 1)`);

	// Test user2 accessible invoices
	const user2Invoices = await companyAuthService.getUserAccessibleInvoices(user2.id);
	console.log(`   User 2 accessible invoices: ${user2Invoices.length} (expected: 1)`);

	// Verify both users see the same invoice
	const user1HasInvoice = user1Invoices.some(inv => inv.id === invoice.id);
	const user2HasInvoice = user2Invoices.some(inv => inv.id === invoice.id);

	console.log(`   User 1 sees test invoice: ${user1HasInvoice ? "✅ PASS" : "❌ FAIL"}`);
	console.log(`   User 2 sees test invoice: ${user2HasInvoice ? "✅ PASS" : "❌ FAIL"}`);

	return user1Invoices.length === 1 && user2Invoices.length === 1 && user1HasInvoice && user2HasInvoice;
}

async function testCampaignAccess(testData) {
	console.log("\n🎯 Testing campaign access...");

	const companyAuthService = new CompanyAuthService();
	const { user1, user2, campaign } = testData;

	// Test user1 access (campaign manager)
	const user1CampaignAccess = await companyAuthService.canAccessCampaign(user1.id, campaign.id);
	console.log(`   User 1 campaign access: ${user1CampaignAccess ? "✅ PASS" : "❌ FAIL"} (manager)`);

	// Test user2 access (company member)
	const user2CampaignAccess = await companyAuthService.canAccessCampaign(user2.id, campaign.id);
	console.log(`   User 2 campaign access: ${user2CampaignAccess ? "✅ PASS" : "❌ FAIL"} (company member)`);

	return user1CampaignAccess && user2CampaignAccess;
}

async function cleanupTestData(testData) {
	console.log("\n🧹 Cleaning up test data...");

	const { user1, user2, company, campaign, invoice } = testData;

	// Delete in reverse order of dependencies
	await db("dtm_ads.invoice_line_items").where({ invoice_id: invoice.id }).del();
	await db("dtm_ads.invoices").where({ id: invoice.id }).del();
	await db("dtm_ads.ad_campaigns").where({ id: campaign.id }).del();
	await db("dtm_base.user_company").where({ company_id: company.id }).del();
	await db("dtm_base.companies").where({ id: company.id }).del();
	await db("dtm_base.users").whereIn("id", [user1.id, user2.id]).del();

	console.log("✅ Test data cleaned up");
}

async function runTests() {
	console.log("🚀 Starting Company Authorization Tests\n");

	let testData;
	let allTestsPassed = true;

	try {
		// Setup
		testData = await setupTestData();

		// Run tests
		const companyAccessPassed = await testCompanyAccess(testData);
		const invoiceAccessPassed = await testInvoiceAccess(testData);
		const invoiceRetrievalPassed = await testInvoiceRetrieval(testData);
		const campaignAccessPassed = await testCampaignAccess(testData);

		allTestsPassed = companyAccessPassed && invoiceAccessPassed && invoiceRetrievalPassed && campaignAccessPassed;

		// Results
		console.log("\n📊 Test Results:");
		console.log(`   Company Access: ${companyAccessPassed ? "✅ PASS" : "❌ FAIL"}`);
		console.log(`   Invoice Access: ${invoiceAccessPassed ? "✅ PASS" : "❌ FAIL"}`);
		console.log(`   Invoice Retrieval: ${invoiceRetrievalPassed ? "✅ PASS" : "❌ FAIL"}`);
		console.log(`   Campaign Access: ${campaignAccessPassed ? "✅ PASS" : "❌ FAIL"}`);

		console.log(`\n🎯 Overall Result: ${allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`);

	} catch (error) {
		console.error("❌ Test execution failed:", error);
		allTestsPassed = false;
	} finally {
		// Cleanup
		if (testData) {
			await cleanupTestData(testData);
		}

		// Close database connection
		await db.destroy();
	}

	process.exit(allTestsPassed ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
	runTests().catch(console.error);
}

module.exports = { runTests };
