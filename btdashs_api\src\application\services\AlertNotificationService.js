const nodemailer = require("nodemailer");
const logger = require("../../../logger");

class AlertNotificationService {
	constructor() {
		this.emailTransporter = this.createEmailTransporter();
		this.alertWebhookUrl = process.env.ALERT_WEBHOOK_URL;
		this.emailRecipients = this.parseEmailRecipients();
	}

	/**
	 * Create email transporter
	 * @returns {Object} Nodemailer transporter
	 */
	createEmailTransporter() {
		if (!process.env.SMTP_HOST || !process.env.SMTP_USER) {
			logger.warn("Email configuration missing, email alerts disabled");
			return null;
		}

		return nodemailer.createTransporter({
			host: process.env.SMTP_HOST,
			port: parseInt(process.env.SMTP_PORT || "587"),
			secure: process.env.SMTP_SECURE === "true",
			auth: {
				user: process.env.SMTP_USER,
				pass: process.env.SMTP_PASS,
			},
		});
	}

	/**
	 * Parse email recipients from environment
	 * @returns {Array} List of email recipients
	 */
	parseEmailRecipients() {
		const recipients = process.env.ALERT_EMAIL_RECIPIENTS;
		if (!recipients) {
			return [];
		}
		return recipients.split(",").map((email) => email.trim());
	}

	/**
	 * Send email alert
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 */
	async sendEmailAlert(alert, context) {
		if (!this.emailTransporter || this.emailRecipients.length === 0) {
			logger.warn("Email alert skipped - no configuration or recipients");
			return;
		}

		try {
			const subject = this.generateEmailSubject(alert);
			const html = this.generateEmailHTML(alert, context);

			const mailOptions = {
				from: process.env.SMTP_FROM || process.env.SMTP_USER,
				to: this.emailRecipients.join(", "),
				subject,
				html,
			};

			await this.emailTransporter.sendMail(mailOptions);

			logger.info("Email alert sent successfully", {
				alertType: alert.type,
				severity: alert.severity,
				recipients: this.emailRecipients.length,
			});
		} catch (error) {
			logger.error("Failed to send email alert", {
				error: error.message,
				alert: alert.type,
			});
		}
	}

	/**
	 * Generate email subject
	 * @param {Object} alert - Alert details
	 * @returns {string} Email subject
	 */
	generateEmailSubject(alert) {
		const env = process.env.NODE_ENV || "development";
		const severityEmoji = {
			info: "ℹ️",
			warning: "⚠️",
			critical: "🚨",
		};

		return `${severityEmoji[alert.severity]} BTDash Payment Alert [${alert.severity.toUpperCase()}] - ${env}`;
	}

	/**
	 * Generate email HTML content
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 * @returns {string} HTML content
	 */
	generateEmailHTML(alert, context) {
		const severityColor = {
			info: "#3498db",
			warning: "#f39c12",
			critical: "#e74c3c",
		};

		return `
		<!DOCTYPE html>
		<html>
		<head>
			<meta charset="utf-8">
			<title>BTDash Payment Alert</title>
			<style>
				body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
				.container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
				.header { background-color: ${severityColor[alert.severity]}; color: white; padding: 20px; text-align: center; }
				.content { padding: 20px; }
				.alert-details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
				.metric { display: flex; justify-content: space-between; margin: 10px 0; }
				.metric-label { font-weight: bold; }
				.footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
				.button { display: inline-block; background-color: ${
					severityColor[alert.severity]
				}; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
			</style>
		</head>
		<body>
			<div class="container">
				<div class="header">
					<h1>Payment System Alert</h1>
					<p>Severity: ${alert.severity.toUpperCase()}</p>
				</div>
				
				<div class="content">
					<h2>Alert Details</h2>
					<div class="alert-details">
						<div class="metric">
							<span class="metric-label">Alert Type:</span>
							<span>${alert.type}</span>
						</div>
						<div class="metric">
							<span class="metric-label">Message:</span>
							<span>${alert.message}</span>
						</div>
						<div class="metric">
							<span class="metric-label">Time Window:</span>
							<span>${alert.timeWindow}</span>
						</div>
						${
							alert.value !== undefined
								? `
						<div class="metric">
							<span class="metric-label">Current Value:</span>
							<span>${alert.value}</span>
						</div>
						`
								: ""
						}
						${
							alert.threshold !== undefined
								? `
						<div class="metric">
							<span class="metric-label">Threshold:</span>
							<span>${alert.threshold}</span>
						</div>
						`
								: ""
						}
						<div class="metric">
							<span class="metric-label">Timestamp:</span>
							<span>${context.timestamp}</span>
						</div>
					</div>

					<h3>System Status</h3>
					<div class="alert-details">
						<div class="metric">
							<span class="metric-label">Overall Status:</span>
							<span>${context.status.toUpperCase()}</span>
						</div>
						${
							context.metrics.payment
								? `
						<div class="metric">
							<span class="metric-label">Payment Success Rate:</span>
							<span>${context.metrics.payment.success_rate}%</span>
						</div>
						<div class="metric">
							<span class="metric-label">Total Revenue:</span>
							<span>$${context.metrics.payment.total_revenue}</span>
						</div>
						<div class="metric">
							<span class="metric-label">Failed Amount:</span>
							<span>$${context.metrics.payment.total_failed_amount}</span>
						</div>
						`
								: ""
						}
					</div>

					<div style="text-align: center;">
						<a href="${process.env.ADMIN_DASHBOARD_URL || "http://localhost:3000/admin/payments"}" class="button">
							View Payment Dashboard
						</a>
					</div>
				</div>

				<div class="footer">
					<p>BTDash Payment Monitoring System</p>
					<p>Environment: ${process.env.NODE_ENV || "development"}</p>
				</div>
			</div>
		</body>
		</html>
		`;
	}

	/**
	 * Send webhook alert
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 */
	async sendWebhookAlert(alert, context) {
		if (!this.alertWebhookUrl) {
			logger.warn("Webhook alert skipped - no webhook URL configured");
			return;
		}

		try {
			const payload = {
				alert,
				context,
				timestamp: new Date().toISOString(),
				environment: process.env.NODE_ENV || "development",
			};

			const response = await fetch(this.alertWebhookUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"User-Agent": "BTDash-Payment-Monitor/1.0",
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				throw new Error(`Webhook alert failed: ${response.status}`);
			}

			logger.info("Webhook alert sent successfully", {
				alertType: alert.type,
				severity: alert.severity,
				url: this.alertWebhookUrl,
			});
		} catch (error) {
			logger.error("Failed to send webhook alert", {
				error: error.message,
				alert: alert.type,
				url: this.alertWebhookUrl,
			});
		}
	}

	/**
	 * Send SMS alert (placeholder for future implementation)
	 * @param {Object} alert - Alert details
	 * @param {Object} context - Monitoring context
	 */
	async sendSMSAlert(alert, context) {
		// Placeholder for SMS integration (Twilio, AWS SNS, etc.)
		logger.info("SMS alert would be sent", {
			alert: alert.type,
			severity: alert.severity,
		});
	}

	/**
	 * Test notification channels
	 * @returns {Promise<Object>} Test results
	 */
	async testNotificationChannels() {
		const results = {
			email: false,
			webhook: false,
		};

		const testAlert = {
			type: "test_alert",
			severity: "info",
			message: "This is a test alert from BTDash Payment Monitoring",
			timeWindow: "1h",
		};

		const testContext = {
			timestamp: new Date().toISOString(),
			status: "healthy",
			metrics: {
				payment: {
					success_rate: 100,
					total_revenue: 0,
					total_failed_amount: 0,
				},
			},
		};

		// Test email
		try {
			await this.sendEmailAlert(testAlert, testContext);
			results.email = true;
		} catch (error) {
			logger.error("Email test failed", { error: error.message });
		}

		// Test webhook
		try {
			await this.sendWebhookAlert(testAlert, testContext);
			results.webhook = true;
		} catch (error) {
			logger.error("Webhook test failed", { error: error.message });
		}

		return results;
	}
}

module.exports = AlertNotificationService;
