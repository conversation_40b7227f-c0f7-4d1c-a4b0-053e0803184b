// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    namespace V2 {
      namespace Event {
        interface Reason {
          /**
           * Event reason type.
           */
          type: 'request';

          /**
           * Information on the API request that instigated the event.
           */
          request: Reason.Request | null;
        }

        namespace Reason {
          interface Request {
            /**
             * ID of the API request that caused the event.
             */
            id: string;

            /**
             * The idempotency key transmitted during the request.
             */
            idempotency_key: string;
          }
        }
      }

      /**
       * Events are generated to keep you informed of activity in your business account. APIs in the /v2 namespace generate [thin events](https://docs.stripe.com/event-destinations#benefits-of-thin-events) which have small, unversioned payloads that include a reference to the ID of the object that has changed. The Events v2 API returns these new thin events. [Retrieve the event object](https://docs.stripe.com/event-destinations#fetch-data) for additional data about the event. Use the related object ID in the event payload to [fetch the API resource](https://docs.stripe.com/event-destinations#retrieve-the-object-associated-with-thin-events) of the object associated with the event. Comparatively, events generated by most API v1 include a versioned snapshot of an API object in their payload.
       */
      interface EventBase {
        /**
         * Unique identifier for the event.
         */
        id: string;

        /**
         * String representing the object's type. Objects of the same type share the same value of the object field.
         */
        object: 'v2.core.event';

        /**
         * Authentication context needed to fetch the event or related object.
         */
        context: string | null;

        /**
         * Time at which the object was created.
         */
        created: string;

        /**
         * Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
         */
        livemode: boolean;

        /**
         * Reason for the event.
         */
        reason: Event.Reason | null;

        /**
         * The type of the event.
         */
        type: string;
      }
    }
  }
}
