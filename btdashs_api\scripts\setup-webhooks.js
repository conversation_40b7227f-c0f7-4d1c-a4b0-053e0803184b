#!/usr/bin/env node

/**
 * Webhook Setup Script for BTDash API
 * 
 * This script helps set up webhook endpoints for local development and production.
 * It can automatically configure Stripe webhooks and test endpoint connectivity.
 * 
 * Usage:
 *   node scripts/setup-webhooks.js [options]
 * 
 * Options:
 *   --env <environment>    Environment (development, staging, production)
 *   --ngrok <url>         Ngrok URL for local development
 *   --test               Test webhook endpoints only
 *   --list               List existing webhook endpoints
 *   --delete <id>        Delete webhook endpoint by ID
 */

require('dotenv').config();
const WebhookConfigService = require('../src/application/services/WebhookConfigService');
const logger = require('../logger');

class WebhookSetupScript {
	constructor() {
		this.webhookConfigService = new WebhookConfigService();
		this.args = this.parseArguments();
	}

	parseArguments() {
		const args = process.argv.slice(2);
		const parsed = {
			env: process.env.NODE_ENV || 'development',
			ngrok: null,
			test: false,
			list: false,
			delete: null,
			help: false,
		};

		for (let i = 0; i < args.length; i++) {
			switch (args[i]) {
				case '--env':
					parsed.env = args[++i];
					break;
				case '--ngrok':
					parsed.ngrok = args[++i];
					break;
				case '--test':
					parsed.test = true;
					break;
				case '--list':
					parsed.list = true;
					break;
				case '--delete':
					parsed.delete = args[++i];
					break;
				case '--help':
				case '-h':
					parsed.help = true;
					break;
			}
		}

		return parsed;
	}

	showHelp() {
		console.log(`
BTDash Webhook Setup Script

Usage: node scripts/setup-webhooks.js [options]

Options:
  --env <environment>    Set environment (development, staging, production)
  --ngrok <url>         Set ngrok URL for local development
  --test               Test webhook endpoints connectivity
  --list               List existing Stripe webhook endpoints
  --delete <id>        Delete Stripe webhook endpoint by ID
  --help, -h           Show this help message

Examples:
  # Setup webhooks for development
  node scripts/setup-webhooks.js --env development

  # Setup webhooks with ngrok URL
  node scripts/setup-webhooks.js --ngrok https://abc123.ngrok.io

  # Test webhook endpoints
  node scripts/setup-webhooks.js --test

  # List existing webhooks
  node scripts/setup-webhooks.js --list

  # Delete a webhook endpoint
  node scripts/setup-webhooks.js --delete we_1234567890

Environment Variables Required:
  STRIPE_SECRET_KEY      - Stripe secret key
  STRIPE_WEBHOOK_SECRET  - Stripe webhook secret (optional for setup)
  WEBHOOK_BASE_URL       - Base URL for webhooks (or use --ngrok)
		`);
	}

	async run() {
		try {
			if (this.args.help) {
				this.showHelp();
				return;
			}

			console.log('🚀 BTDash Webhook Setup Script');
			console.log('================================\n');

			// Set ngrok URL if provided
			if (this.args.ngrok) {
				process.env.NGROK_URL = this.args.ngrok;
				console.log(`📡 Using ngrok URL: ${this.args.ngrok}`);
			}

			// Set environment
			process.env.NODE_ENV = this.args.env;
			console.log(`🌍 Environment: ${this.args.env}`);

			// Validate configuration
			await this.validateConfiguration();

			if (this.args.list) {
				await this.listWebhooks();
				return;
			}

			if (this.args.delete) {
				await this.deleteWebhook(this.args.delete);
				return;
			}

			if (this.args.test) {
				await this.testWebhooks();
				return;
			}

			// Default: Setup webhooks
			await this.setupWebhooks();

		} catch (error) {
			console.error('❌ Error:', error.message);
			process.exit(1);
		}
	}

	async validateConfiguration() {
		console.log('🔍 Validating configuration...');

		const config = this.webhookConfigService.getWebhookConfig();
		
		console.log(`   Base URL: ${config.baseUrl}`);
		console.log(`   Environment: ${config.environment}`);
		console.log(`   Stripe Secret Key: ${config.stripeSecretKey}`);
		console.log(`   Webhook Secret: ${config.webhookSecret}`);

		if (config.stripeSecretKey === 'missing') {
			throw new Error('STRIPE_SECRET_KEY environment variable is required');
		}

		if (!config.baseUrl || config.baseUrl.includes('localhost') && config.environment === 'production') {
			console.warn('⚠️  Warning: Using localhost URL in production environment');
		}

		console.log('✅ Configuration validated\n');
	}

	async setupWebhooks() {
		console.log('⚙️  Setting up webhook endpoints...');

		try {
			// Setup Stripe webhook
			console.log('   Setting up Stripe webhook...');
			const stripeEndpoint = await this.webhookConfigService.setupStripeWebhook();
			
			console.log(`   ✅ Stripe webhook configured:`);
			console.log(`      ID: ${stripeEndpoint.id}`);
			console.log(`      URL: ${stripeEndpoint.url}`);
			console.log(`      Status: ${stripeEndpoint.status}`);
			console.log(`      Events: ${stripeEndpoint.enabled_events.length} events`);

			// Test webhook endpoints
			console.log('\n🧪 Testing webhook endpoints...');
			await this.testWebhooks();

			console.log('\n🎉 Webhook setup completed successfully!');
			console.log('\nNext steps:');
			console.log('1. Test your webhook endpoints with Stripe CLI or dashboard');
			console.log('2. Monitor webhook logs in your application');
			console.log('3. Set up webhook signature verification in production');

		} catch (error) {
			console.error('❌ Failed to setup webhooks:', error.message);
			throw error;
		}
	}

	async testWebhooks() {
		console.log('🧪 Testing webhook endpoints...');

		const endpoints = this.webhookConfigService.getWebhookEndpoints();

		for (const [name, url] of Object.entries(endpoints)) {
			console.log(`   Testing ${name}: ${url}`);
			
			try {
				const isHealthy = await this.webhookConfigService.verifyWebhookEndpoint(url);
				if (isHealthy) {
					console.log(`   ✅ ${name} endpoint is healthy`);
				} else {
					console.log(`   ❌ ${name} endpoint is not responding`);
				}
			} catch (error) {
				console.log(`   ❌ ${name} endpoint test failed: ${error.message}`);
			}
		}

		console.log('');
	}

	async listWebhooks() {
		console.log('📋 Listing Stripe webhook endpoints...');

		try {
			const endpoints = await this.webhookConfigService.listStripeWebhooks();

			if (endpoints.length === 0) {
				console.log('   No webhook endpoints found');
				return;
			}

			console.log(`   Found ${endpoints.length} webhook endpoint(s):\n`);

			endpoints.forEach((endpoint, index) => {
				console.log(`   ${index + 1}. ${endpoint.id}`);
				console.log(`      URL: ${endpoint.url}`);
				console.log(`      Status: ${endpoint.status}`);
				console.log(`      Events: ${endpoint.enabled_events.length}`);
				console.log(`      Created: ${new Date(endpoint.created * 1000).toISOString()}`);
				console.log('');
			});

		} catch (error) {
			console.error('❌ Failed to list webhooks:', error.message);
			throw error;
		}
	}

	async deleteWebhook(endpointId) {
		console.log(`🗑️  Deleting webhook endpoint: ${endpointId}`);

		try {
			const success = await this.webhookConfigService.deleteStripeWebhook(endpointId);
			
			if (success) {
				console.log('✅ Webhook endpoint deleted successfully');
			} else {
				console.log('❌ Failed to delete webhook endpoint');
			}

		} catch (error) {
			console.error('❌ Failed to delete webhook:', error.message);
			throw error;
		}
	}

	async getWebhookStatus() {
		console.log('📊 Getting webhook status...');

		try {
			const status = await this.webhookConfigService.getWebhookStatus();
			
			console.log('Webhook Status:');
			console.log(JSON.stringify(status, null, 2));

		} catch (error) {
			console.error('❌ Failed to get webhook status:', error.message);
			throw error;
		}
	}
}

// Run the script if called directly
if (require.main === module) {
	const script = new WebhookSetupScript();
	script.run().catch(console.error);
}

module.exports = WebhookSetupScript;
