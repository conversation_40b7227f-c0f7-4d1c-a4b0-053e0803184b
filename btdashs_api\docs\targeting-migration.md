# Targeting System Migration

## Overview

The BTDash advertising system has migrated from ad-level targeting to campaign-level targeting for better management and consistency.

## Migration Summary

### Before (Legacy System)
- **Table**: `dtm_ads.ad_targets`
- **Scope**: Individual ad targeting
- **Storage**: Relational table with key-value pairs
- **Management**: Each ad had separate targeting rules

### After (Current System)
- **Column**: `dtm_ads.ad_campaigns.targeting` (JSONB)
- **Scope**: Campaign-level targeting
- **Storage**: JSON document in campaign record
- **Management**: All ads in campaign inherit targeting rules

## Benefits of Campaign-Level Targeting

1. **Simplified Management**: Set targeting once per campaign
2. **Consistency**: All ads in campaign follow same targeting rules
3. **Performance**: Fewer database queries for targeting evaluation
4. **Flexibility**: JSON storage allows complex targeting structures
5. **Scalability**: Better performance with JSONB indexes

## Targeting Structure

### JSON Schema
```json
{
  "countries": ["US", "CA", "GB"],
  "devices": ["desktop", "mobile", "tablet"],
  "languages": ["en", "es", "fr"],
  "interests": ["technology", "finance"],
  "age_ranges": ["25-34", "35-44", "45-54"]
}
```

### Supported Targeting Options
- **Geographic**: Countries, regions
- **Device**: Desktop, mobile, tablet
- **Language**: User language preferences
- **Interest**: User interest categories
- **Demographics**: Age ranges

## Implementation Details

### Database Changes
1. **Removed**: `dtm_ads.ad_targets` table
2. **Added**: `dtm_ads.ad_campaigns.targeting` JSONB column
3. **Indexed**: GIN index on targeting column for performance

### Service Layer
- **CampaignTargetingService**: Handles campaign-level targeting
- **Removed**: TargetingService (ad-level)
- **Updated**: AdsService to use campaign targeting

### API Changes
- **Removed**: Ad-level targeting endpoints
- **Updated**: Campaign targeting endpoints
- **Maintained**: Backward compatibility where possible

## Migration Process

### Automatic Migration
Run the migration script:
```sql
-- Execute migration
\i src/infrastructure/database/migrations/002_remove_legacy_ad_targeting.sql
```

### Manual Data Migration (if needed)
If you have existing ad_targets data to preserve:

```sql
-- 1. Backup existing data
CREATE TABLE ad_targets_backup AS SELECT * FROM dtm_ads.ad_targets;

-- 2. Migrate to campaign targeting (example)
UPDATE dtm_ads.ad_campaigns 
SET targeting = (
  SELECT jsonb_build_object(
    'countries', jsonb_agg(DISTINCT value) FILTER (WHERE key = 'country_code'),
    'devices', jsonb_agg(DISTINCT value) FILTER (WHERE key = 'device_type'),
    'languages', jsonb_agg(DISTINCT value) FILTER (WHERE key = 'language')
  )
  FROM dtm_ads.ad_targets t
  JOIN dtm_ads.ads a ON t.ad_id = a.id
  WHERE a.campaign_id = ad_campaigns.id
)
WHERE id IN (
  SELECT DISTINCT a.campaign_id 
  FROM dtm_ads.ads a 
  JOIN dtm_ads.ad_targets t ON a.id = t.ad_id
);
```

## Code Updates Required

### Frontend (btdash-admanager)
1. **Removed**: `/dashboard/ads/[id]/targeting` page
2. **Updated**: Campaign targeting UI to handle all ads
3. **Maintained**: Campaign-level targeting interface

### Backend (btdashs_api)
1. **Removed**: TargetingService.js
2. **Updated**: Comments and documentation
3. **Maintained**: CampaignTargetingService

### Ad Serving (btdashs)
1. **Updated**: Ad serving logic to use campaign targeting
2. **Maintained**: Targeting evaluation performance
3. **Enhanced**: Caching for targeting rules

## Testing

### Verify Migration
```sql
-- Check that ad_targets table is removed
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'dtm_ads' AND table_name = 'ad_targets';
-- Should return no rows

-- Check campaign targeting column exists
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_schema = 'dtm_ads' 
AND table_name = 'ad_campaigns' 
AND column_name = 'targeting';
-- Should return: targeting | jsonb
```

### Test Targeting Functionality
1. Create campaign with targeting rules
2. Verify ads inherit campaign targeting
3. Test ad serving with targeting evaluation
4. Confirm targeting UI works correctly

## Troubleshooting

### Common Issues
1. **Missing targeting column**: Run migration script
2. **Ad serving errors**: Check campaign targeting format
3. **UI errors**: Clear browser cache and restart

### Performance Monitoring
- Monitor JSONB query performance
- Check GIN index usage
- Verify targeting evaluation speed

## Future Enhancements

### Planned Features
1. **Advanced Targeting**: Behavioral targeting
2. **A/B Testing**: Campaign-level experiments
3. **Real-time Updates**: Dynamic targeting adjustments
4. **Analytics**: Targeting performance metrics

### Optimization Opportunities
1. **Caching**: Redis cache for targeting rules
2. **Preprocessing**: Pre-computed targeting matches
3. **Machine Learning**: Automated targeting optimization
