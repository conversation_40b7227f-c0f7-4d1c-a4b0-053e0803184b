# Payment Monitoring and Alerting Configuration
# Copy this file to .env.local and configure for your environment

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable/disable monitoring features
PAYMENT_MONITORING_ENABLED=true
MONITORING_JOB_ENABLED=true

# Monitoring intervals (cron format)
MONITORING_REALTIME_SCHEDULE=*/5 * * * *    # Every 5 minutes
MONITORING_HOURLY_SCHEDULE=0 * * * *        # Every hour
MONITORING_DAILY_SCHEDULE=0 9 * * *         # Daily at 9 AM
MONITORING_WEEKLY_SCHEDULE=0 9 * * 1        # Weekly on Monday at 9 AM
MONITORING_HEALTH_SCHEDULE=* * * * *        # Every minute

# Timezone for scheduled jobs
TZ=UTC

# =============================================================================
# ALERT THRESHOLDS
# =============================================================================

# Payment success rate thresholds (percentage)
ALERT_SUCCESS_RATE_WARNING=95
ALERT_SUCCESS_RATE_CRITICAL=90

# Payment failure rate thresholds (percentage)
ALERT_FAILURE_RATE_WARNING=5
ALERT_FAILURE_RATE_CRITICAL=10

# Webhook success rate thresholds (percentage)
ALERT_WEBHOOK_SUCCESS_RATE_WARNING=98
ALERT_WEBHOOK_SUCCESS_RATE_CRITICAL=95

# Failed payment amount thresholds (USD)
ALERT_FAILED_AMOUNT_WARNING=1000
ALERT_FAILED_AMOUNT_CRITICAL=5000

# Response time thresholds (milliseconds)
ALERT_RESPONSE_TIME_WARNING=5000
ALERT_RESPONSE_TIME_CRITICAL=10000

# Alert cooldown period (milliseconds)
ALERT_COOLDOWN_PERIOD=900000  # 15 minutes

# =============================================================================
# EMAIL NOTIFICATIONS
# =============================================================================

# Enable email alerts
ALERT_EMAIL_ENABLED=false

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email settings
SMTP_FROM=BTDash Monitoring <<EMAIL>>
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# =============================================================================
# SLACK NOTIFICATIONS
# =============================================================================

# Enable Slack alerts
ALERT_SLACK_ENABLED=false

# Slack webhook URL (create in Slack app settings)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# WEBHOOK NOTIFICATIONS
# =============================================================================

# Custom webhook URL for alerts
ALERT_WEBHOOK_URL=https://yourdomain.com/api/alerts/webhook

# =============================================================================
# DASHBOARD CONFIGURATION
# =============================================================================

# Admin dashboard URL (for alert links)
ADMIN_DASHBOARD_URL=http://localhost:3000/admin/payments

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level for monitoring (debug, info, warn, error)
MONITORING_LOG_LEVEL=info

# Enable detailed monitoring logs
MONITORING_VERBOSE_LOGGING=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Disable alerts in development
DISABLE_ALERTS_IN_DEV=true

# Test mode (sends test alerts)
MONITORING_TEST_MODE=false

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Gmail SMTP Example:
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-16-character-app-password

# Outlook/Hotmail SMTP Example:
# SMTP_HOST=smtp-mail.outlook.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-password

# SendGrid SMTP Example:
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=apikey
# SMTP_PASS=your-sendgrid-api-key

# =============================================================================
# PRODUCTION RECOMMENDATIONS
# =============================================================================

# For production environments:
# 1. Enable all notification channels (email, Slack, webhook)
# 2. Set appropriate alert thresholds based on your SLA
# 3. Use a dedicated email service (SendGrid, Mailgun, etc.)
# 4. Set up proper Slack channels for alerts
# 5. Configure webhook endpoints for integration with monitoring tools
# 6. Enable verbose logging for troubleshooting
# 7. Set timezone to your business timezone
# 8. Test all notification channels before going live

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# To test the monitoring system:
# 1. Set MONITORING_TEST_MODE=true
# 2. Enable at least one notification channel
# 3. Use the test endpoints to trigger alerts
# 4. Verify alerts are received through configured channels

# Test commands:
# curl -X POST http://localhost:3001/api/monitoring/test-notifications \
#      -H "Authorization: Bearer <jwt_token>"

# curl -X POST http://localhost:3001/api/monitoring/trigger-check \
#      -H "Authorization: Bearer <jwt_token>" \
#      -H "Content-Type: application/json" \
#      -d '{"timeWindow": "1h", "includeWebhooks": true}'
