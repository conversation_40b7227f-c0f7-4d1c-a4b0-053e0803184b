const { asyncHandler } = require("../../middleware/errorHandler");
const WebhookConfigService = require("../../application/services/WebhookConfigService");
const WebhookRetryService = require("../../application/services/WebhookRetryService");
const { sendSuccess, sendError } = require("../../utils/responseWrapper");
const logger = require("../../../logger");

/**
 * Webhook health check endpoint
 */
const webhookHealth = asyncHandler(async (req, res) => {
	return sendSuccess(res, { 
		status: 'healthy', 
		timestamp: new Date().toISOString(),
		service: 'btdash-webhook-endpoint'
	}, "Webhook endpoint is healthy");
});

/**
 * Get webhook configuration and status
 */
const getWebhookStatus = asyncHandler(async (req, res) => {
	const webhookConfigService = new WebhookConfigService();
	const status = await webhookConfigService.getWebhookStatus();
	
	return sendSuccess(res, status, "Webhook status retrieved successfully");
});

/**
 * Setup or update Stripe webhook endpoint
 */
const setupStripeWebhook = asyncHandler(async (req, res) => {
	const { url, events, description, enabled = true } = req.body;
	
	const webhookConfigService = new WebhookConfigService();
	const endpoint = await webhookConfigService.setupStripeWebhook({
		url,
		events,
		description,
		enabled,
	});
	
	logger.info("Stripe webhook endpoint configured", {
		id: endpoint.id,
		url: endpoint.url,
		events: endpoint.enabled_events,
	});
	
	return sendSuccess(res, endpoint, "Stripe webhook endpoint configured successfully");
});

/**
 * Test webhook endpoint
 */
const testWebhookEndpoint = asyncHandler(async (req, res) => {
	const { url } = req.body;
	
	if (!url) {
		return sendError(res, "URL is required", null, 400);
	}
	
	const webhookConfigService = new WebhookConfigService();
	const testResult = await webhookConfigService.testWebhookEndpoint(url);
	
	if (testResult) {
		return sendSuccess(res, { success: true }, "Webhook endpoint test passed");
	} else {
		return sendError(res, "Webhook endpoint test failed", null, 400);
	}
});

/**
 * List all Stripe webhook endpoints
 */
const listStripeWebhooks = asyncHandler(async (req, res) => {
	const webhookConfigService = new WebhookConfigService();
	const endpoints = await webhookConfigService.listStripeWebhooks();
	
	return sendSuccess(res, { endpoints }, "Stripe webhook endpoints retrieved successfully");
});

/**
 * Delete Stripe webhook endpoint
 */
const deleteStripeWebhook = asyncHandler(async (req, res) => {
	const { endpointId } = req.params;
	
	const webhookConfigService = new WebhookConfigService();
	const success = await webhookConfigService.deleteStripeWebhook(endpointId);
	
	if (success) {
		return sendSuccess(res, null, "Stripe webhook endpoint deleted successfully");
	} else {
		return sendError(res, "Failed to delete webhook endpoint", null, 500);
	}
});

/**
 * Get webhook configuration
 */
const getWebhookConfig = asyncHandler(async (req, res) => {
	const webhookConfigService = new WebhookConfigService();
	const config = webhookConfigService.getWebhookConfig();
	
	return sendSuccess(res, config, "Webhook configuration retrieved successfully");
});

/**
 * Get webhook retry statistics
 */
const getWebhookRetryStats = asyncHandler(async (req, res) => {
	const { startDate, endDate } = req.query;
	
	const webhookRetryService = new WebhookRetryService();
	const stats = await webhookRetryService.getWebhookStats({
		startDate: startDate ? new Date(startDate) : undefined,
		endDate: endDate ? new Date(endDate) : undefined,
	});
	
	return sendSuccess(res, stats, "Webhook retry statistics retrieved successfully");
});

/**
 * Get recent webhook failures
 */
const getRecentWebhookFailures = asyncHandler(async (req, res) => {
	const { limit = 10 } = req.query;
	
	const webhookRetryService = new WebhookRetryService();
	const failures = await webhookRetryService.getRecentFailures(parseInt(limit));
	
	return sendSuccess(res, { failures }, "Recent webhook failures retrieved successfully");
});

/**
 * Manually retry failed webhook
 */
const retryFailedWebhook = asyncHandler(async (req, res) => {
	const { webhookLogId } = req.params;
	const { eventType } = req.body;
	
	if (!eventType) {
		return sendError(res, "Event type is required", null, 400);
	}
	
	const webhookRetryService = new WebhookRetryService();
	
	// Define processor based on event type
	let processor;
	switch (eventType) {
		case 'payment_intent.succeeded':
			const { processPaymentSuccess } = require('./billingController');
			processor = processPaymentSuccess;
			break;
		case 'payment_intent.payment_failed':
			const { processPaymentFailure } = require('./billingController');
			processor = processPaymentFailure;
			break;
		default:
			return sendError(res, "Unsupported event type for retry", null, 400);
	}
	
	try {
		await webhookRetryService.retryFailedWebhook(parseInt(webhookLogId), processor);
		return sendSuccess(res, null, "Webhook retried successfully");
	} catch (error) {
		logger.error("Failed to retry webhook", { 
			error: error.message, 
			webhookLogId,
			eventType 
		});
		return sendError(res, "Failed to retry webhook", error.message, 500);
	}
});

/**
 * Initialize webhook endpoints for current environment
 */
const initializeWebhooks = asyncHandler(async (req, res) => {
	try {
		const webhookConfigService = new WebhookConfigService();
		
		// Setup Stripe webhook with default configuration
		const stripeEndpoint = await webhookConfigService.setupStripeWebhook();
		
		// Get current status
		const status = await webhookConfigService.getWebhookStatus();
		
		logger.info("Webhooks initialized successfully", {
			environment: process.env.NODE_ENV,
			stripeEndpointId: stripeEndpoint.id,
			baseUrl: webhookConfigService.getWebhookBaseUrl(),
		});
		
		return sendSuccess(res, {
			stripe_endpoint: stripeEndpoint,
			status: status,
		}, "Webhooks initialized successfully");
		
	} catch (error) {
		logger.error("Failed to initialize webhooks", {
			error: error.message,
			stack: error.stack,
		});
		return sendError(res, "Failed to initialize webhooks", error.message, 500);
	}
});

module.exports = {
	webhookHealth,
	getWebhookStatus,
	setupStripeWebhook,
	testWebhookEndpoint,
	listStripeWebhooks,
	deleteStripeWebhook,
	getWebhookConfig,
	getWebhookRetryStats,
	getRecentWebhookFailures,
	retryFailedWebhook,
	initializeWebhooks,
};
