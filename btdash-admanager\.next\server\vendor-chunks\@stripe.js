"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@stripe";
exports.ids = ["vendor-chunks/@stripe"];
exports.modules = {

/***/ "(ssr)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddressElement: () => (/* binding */ AddressElement),\n/* harmony export */   AffirmMessageElement: () => (/* binding */ AffirmMessageElement),\n/* harmony export */   AfterpayClearpayMessageElement: () => (/* binding */ AfterpayClearpayMessageElement),\n/* harmony export */   AuBankAccountElement: () => (/* binding */ AuBankAccountElement),\n/* harmony export */   CardCvcElement: () => (/* binding */ CardCvcElement),\n/* harmony export */   CardElement: () => (/* binding */ CardElement),\n/* harmony export */   CardExpiryElement: () => (/* binding */ CardExpiryElement),\n/* harmony export */   CardNumberElement: () => (/* binding */ CardNumberElement),\n/* harmony export */   CheckoutProvider: () => (/* binding */ CheckoutProvider),\n/* harmony export */   CurrencySelectorElement: () => (/* binding */ CurrencySelectorElement),\n/* harmony export */   Elements: () => (/* binding */ Elements),\n/* harmony export */   ElementsConsumer: () => (/* binding */ ElementsConsumer),\n/* harmony export */   EmbeddedCheckout: () => (/* binding */ EmbeddedCheckout),\n/* harmony export */   EmbeddedCheckoutProvider: () => (/* binding */ EmbeddedCheckoutProvider),\n/* harmony export */   EpsBankElement: () => (/* binding */ EpsBankElement),\n/* harmony export */   ExpressCheckoutElement: () => (/* binding */ ExpressCheckoutElement),\n/* harmony export */   FpxBankElement: () => (/* binding */ FpxBankElement),\n/* harmony export */   IbanElement: () => (/* binding */ IbanElement),\n/* harmony export */   IdealBankElement: () => (/* binding */ IdealBankElement),\n/* harmony export */   LinkAuthenticationElement: () => (/* binding */ LinkAuthenticationElement),\n/* harmony export */   P24BankElement: () => (/* binding */ P24BankElement),\n/* harmony export */   PaymentElement: () => (/* binding */ PaymentElement),\n/* harmony export */   PaymentMethodMessagingElement: () => (/* binding */ PaymentMethodMessagingElement),\n/* harmony export */   PaymentRequestButtonElement: () => (/* binding */ PaymentRequestButtonElement),\n/* harmony export */   ShippingAddressElement: () => (/* binding */ ShippingAddressElement),\n/* harmony export */   useCheckout: () => (/* binding */ useCheckout),\n/* harmony export */   useElements: () => (/* binding */ useElements),\n/* harmony export */   useStripe: () => (/* binding */ useStripe)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n\n\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar useAttachEvent = function useAttachEvent(element, event, cb) {\n  var cbDefined = !!cb;\n  var cbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(cb); // In many integrations the callback prop changes on each render.\n  // Using a ref saves us from calling element.on/.off every render.\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    cbRef.current = cb;\n  }, [cb]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (!cbDefined || !element) {\n      return function () {};\n    }\n\n    var decoratedCb = function decoratedCb() {\n      if (cbRef.current) {\n        cbRef.current.apply(cbRef, arguments);\n      }\n    };\n\n    element.on(event, decoratedCb);\n    return function () {\n      element.off(event, decoratedCb);\n    };\n  }, [cbDefined, event, element, cbRef]);\n};\n\nvar usePrevious = function usePrevious(value) {\n  var ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n};\n\nvar isUnknownObject = function isUnknownObject(raw) {\n  return raw !== null && _typeof(raw) === 'object';\n};\nvar isPromise = function isPromise(raw) {\n  return isUnknownObject(raw) && typeof raw.then === 'function';\n}; // We are using types to enforce the `stripe` prop in this lib,\n// but in an untyped integration `stripe` could be anything, so we need\n// to do some sanity validation to prevent type errors.\n\nvar isStripe = function isStripe(raw) {\n  return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n};\n\nvar PLAIN_OBJECT_STR = '[object Object]';\nvar isEqual = function isEqual(left, right) {\n  if (!isUnknownObject(left) || !isUnknownObject(right)) {\n    return left === right;\n  }\n\n  var leftArray = Array.isArray(left);\n  var rightArray = Array.isArray(right);\n  if (leftArray !== rightArray) return false;\n  var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n  var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n  if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n  // fallback to reference check.\n\n  if (!leftPlainObject && !leftArray) return left === right;\n  var leftKeys = Object.keys(left);\n  var rightKeys = Object.keys(right);\n  if (leftKeys.length !== rightKeys.length) return false;\n  var keySet = {};\n\n  for (var i = 0; i < leftKeys.length; i += 1) {\n    keySet[leftKeys[i]] = true;\n  }\n\n  for (var _i = 0; _i < rightKeys.length; _i += 1) {\n    keySet[rightKeys[_i]] = true;\n  }\n\n  var allKeys = Object.keys(keySet);\n\n  if (allKeys.length !== leftKeys.length) {\n    return false;\n  }\n\n  var l = left;\n  var r = right;\n\n  var pred = function pred(key) {\n    return isEqual(l[key], r[key]);\n  };\n\n  return allKeys.every(pred);\n};\n\nvar extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n  if (!isUnknownObject(options)) {\n    return null;\n  }\n\n  return Object.keys(options).reduce(function (newOptions, key) {\n    var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n    if (immutableKeys.includes(key)) {\n      if (isUpdated) {\n        console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n      }\n\n      return newOptions;\n    }\n\n    if (!isUpdated) {\n      return newOptions;\n    }\n\n    return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n  }, null);\n};\n\nvar INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n// integration `stripe` could be anything, so we need to do some sanity\n// validation to prevent type errors.\n\nvar validateStripe = function validateStripe(maybeStripe) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (maybeStripe === null || isStripe(maybeStripe)) {\n    return maybeStripe;\n  }\n\n  throw new Error(errorMsg);\n};\n\nvar parseStripeProp = function parseStripeProp(raw) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (isPromise(raw)) {\n    return {\n      tag: 'async',\n      stripePromise: Promise.resolve(raw).then(function (result) {\n        return validateStripe(result, errorMsg);\n      })\n    };\n  }\n\n  var stripe = validateStripe(raw, errorMsg);\n\n  if (stripe === null) {\n    return {\n      tag: 'empty'\n    };\n  }\n\n  return {\n    tag: 'sync',\n    stripe: stripe\n  };\n};\n\nvar registerWithStripeJs = function registerWithStripeJs(stripe) {\n  if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'react-stripe-js',\n    version: \"3.7.0\"\n  });\n\n  stripe.registerAppInfo({\n    name: 'react-stripe-js',\n    version: \"3.7.0\",\n    url: 'https://stripe.com/docs/stripe-js/react'\n  });\n};\n\nvar ElementsContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nElementsContext.displayName = 'ElementsContext';\nvar parseElementsContext = function parseElementsContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n  }\n\n  return ctx;\n};\n/**\n * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n *\n * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n * Pass the returned `Promise` to `Elements`.\n *\n * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n */\n\nvar Elements = function Elements(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return parseStripeProp(rawStripeProp);\n  }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n    };\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    var isMounted = true;\n\n    var safeSetContext = function safeSetContext(stripe) {\n      setContext(function (ctx) {\n        // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n        if (ctx.stripe) return ctx;\n        return {\n          stripe: stripe,\n          elements: stripe.elements(options)\n        };\n      });\n    }; // For an async stripePromise, store it in context once resolved\n\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted) {\n          // Only update Elements context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          safeSetContext(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !ctx.stripe) {\n      // Or, handle a sync stripe instance going from null -> populated\n      safeSetContext(parsed.stripe);\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (!ctx.elements) {\n      return;\n    }\n\n    var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n    if (updates) {\n      ctx.elements.update(updates);\n    }\n  }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ElementsContext.Provider, {\n    value: ctx\n  }, children);\n};\nElements.propTypes = {\n  stripe: prop_types__WEBPACK_IMPORTED_MODULE_1__.any,\n  options: prop_types__WEBPACK_IMPORTED_MODULE_1__.object\n};\nvar useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(ElementsContext);\n  return parseElementsContext(ctx, useCaseMessage);\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n */\n\nvar useElements = function useElements() {\n  var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n      elements = _useElementsContextWi.elements;\n\n  return elements;\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n */\n\nvar ElementsConsumer = function ElementsConsumer(_ref2) {\n  var children = _ref2.children;\n  var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n  return children(ctx);\n};\nElementsConsumer.propTypes = {\n  children: prop_types__WEBPACK_IMPORTED_MODULE_1__.func.isRequired\n};\n\nvar _excluded$1 = [\"on\", \"session\"];\nvar CheckoutSdkContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nCheckoutSdkContext.displayName = 'CheckoutSdkContext';\nvar parseCheckoutSdkContext = function parseCheckoutSdkContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find CheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CheckoutProvider> provider.\"));\n  }\n\n  return ctx;\n};\nvar CheckoutContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nCheckoutContext.displayName = 'CheckoutContext';\nvar extractCheckoutContextValue = function extractCheckoutContextValue(checkoutSdk, sessionState) {\n  if (!checkoutSdk) {\n    return null;\n  }\n\n  checkoutSdk.on;\n      checkoutSdk.session;\n      var actions = _objectWithoutProperties(checkoutSdk, _excluded$1);\n\n  if (!sessionState) {\n    return Object.assign(checkoutSdk.session(), actions);\n  }\n\n  return Object.assign(sessionState, actions);\n};\nvar INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar CheckoutProvider = function CheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n  }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      session = _React$useState2[0],\n      setSession = _React$useState2[1];\n\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_0__.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      checkoutSdk: null\n    };\n  }),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      ctx = _React$useState4[0],\n      setContext = _React$useState4[1];\n\n  var safeSetContext = function safeSetContext(stripe, checkoutSdk) {\n    setContext(function (ctx) {\n      if (ctx.stripe && ctx.checkoutSdk) {\n        return ctx;\n      }\n\n      return {\n        stripe: stripe,\n        checkoutSdk: checkoutSdk\n      };\n    });\n  }; // Ref used to avoid calling initCheckout multiple times when options changes\n\n\n  var initCheckoutCalledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    var isMounted = true;\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted && !initCheckoutCalledRef.current) {\n          // Only update context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          initCheckoutCalledRef.current = true;\n          stripe.initCheckout(options).then(function (checkoutSdk) {\n            if (checkoutSdk) {\n              safeSetContext(stripe, checkoutSdk);\n              checkoutSdk.on('change', setSession);\n            }\n          });\n        }\n      });\n    } else if (parsed.tag === 'sync' && parsed.stripe && !initCheckoutCalledRef.current) {\n      initCheckoutCalledRef.current = true;\n      parsed.stripe.initCheckout(options).then(function (checkoutSdk) {\n        if (checkoutSdk) {\n          safeSetContext(parsed.stripe, checkoutSdk);\n          checkoutSdk.on('change', setSession);\n        }\n      });\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  var prevCheckoutSdk = usePrevious(ctx.checkoutSdk);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    var _prevOptions$elements, _options$elementsOpti;\n\n    // Ignore changes while checkout sdk is not initialized.\n    if (!ctx.checkoutSdk) {\n      return;\n    }\n\n    var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n    var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n    var hasAppearanceChanged = !isEqual(currentAppearance, previousAppearance);\n    var hasSdkLoaded = !prevCheckoutSdk && ctx.checkoutSdk;\n\n    if (currentAppearance && (hasAppearanceChanged || hasSdkLoaded)) {\n      ctx.checkoutSdk.changeAppearance(currentAppearance);\n    }\n  }, [options, prevOptions, ctx.checkoutSdk, prevCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  var checkoutContextValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return extractCheckoutContextValue(ctx.checkoutSdk, session);\n  }, [ctx.checkoutSdk, session]);\n\n  if (!ctx.checkoutSdk) {\n    return null;\n  }\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(CheckoutSdkContext.Provider, {\n    value: ctx\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(CheckoutContext.Provider, {\n    value: checkoutContextValue\n  }, children));\n};\nCheckoutProvider.propTypes = {\n  stripe: prop_types__WEBPACK_IMPORTED_MODULE_1__.any,\n  options: prop_types__WEBPACK_IMPORTED_MODULE_1__.shape({\n    fetchClientSecret: prop_types__WEBPACK_IMPORTED_MODULE_1__.func.isRequired,\n    elementsOptions: prop_types__WEBPACK_IMPORTED_MODULE_1__.object\n  }).isRequired\n};\nvar useCheckoutSdkContextWithUseCase = function useCheckoutSdkContextWithUseCase(useCaseString) {\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CheckoutSdkContext);\n  return parseCheckoutSdkContext(ctx, useCaseString);\n};\nvar useElementsOrCheckoutSdkContextWithUseCase = function useElementsOrCheckoutSdkContextWithUseCase(useCaseString) {\n  var checkoutSdkContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CheckoutSdkContext);\n  var elementsContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(ElementsContext);\n\n  if (checkoutSdkContext && elementsContext) {\n    throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CheckoutProvider> and <Elements> providers.\"));\n  }\n\n  if (checkoutSdkContext) {\n    return parseCheckoutSdkContext(checkoutSdkContext, useCaseString);\n  }\n\n  return parseElementsContext(elementsContext, useCaseString);\n};\nvar useCheckout = function useCheckout() {\n  // ensure it's in CheckoutProvider\n  useCheckoutSdkContextWithUseCase('calls useCheckout()');\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CheckoutContext);\n\n  if (!ctx) {\n    throw new Error('Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.');\n  }\n\n  return ctx;\n};\n\nvar _excluded = [\"mode\"];\n\nvar capitalized = function capitalized(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n};\n\nvar createElementComponent = function createElementComponent(type, isServer) {\n  var displayName = \"\".concat(capitalized(type), \"Element\");\n\n  var ClientElement = function ClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className,\n        _ref$options = _ref.options,\n        options = _ref$options === void 0 ? {} : _ref$options,\n        onBlur = _ref.onBlur,\n        onFocus = _ref.onFocus,\n        onReady = _ref.onReady,\n        onChange = _ref.onChange,\n        onEscape = _ref.onEscape,\n        onClick = _ref.onClick,\n        onLoadError = _ref.onLoadError,\n        onLoaderStart = _ref.onLoaderStart,\n        onNetworksChange = _ref.onNetworksChange,\n        onConfirm = _ref.onConfirm,\n        onCancel = _ref.onCancel,\n        onShippingAddressChange = _ref.onShippingAddressChange,\n        onShippingRateChange = _ref.onShippingRateChange;\n    var ctx = useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var elements = 'elements' in ctx ? ctx.elements : null;\n    var checkoutSdk = 'checkoutSdk' in ctx ? ctx.checkoutSdk : null;\n\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        element = _React$useState2[0],\n        setElement = _React$useState2[1];\n\n    var elementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    var domNode = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null); // For every event where the merchant provides a callback, call element.on\n    // with that callback. If the merchant ever changes the callback, removes\n    // the old callback with element.off and then call element.on with the new one.\n\n    useAttachEvent(element, 'blur', onBlur);\n    useAttachEvent(element, 'focus', onFocus);\n    useAttachEvent(element, 'escape', onEscape);\n    useAttachEvent(element, 'click', onClick);\n    useAttachEvent(element, 'loaderror', onLoadError);\n    useAttachEvent(element, 'loaderstart', onLoaderStart);\n    useAttachEvent(element, 'networkschange', onNetworksChange);\n    useAttachEvent(element, 'confirm', onConfirm);\n    useAttachEvent(element, 'cancel', onCancel);\n    useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n    useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n    useAttachEvent(element, 'change', onChange);\n    var readyCallback;\n\n    if (onReady) {\n      if (type === 'expressCheckout') {\n        // Passes through the event, which includes visible PM types\n        readyCallback = onReady;\n      } else {\n        // For other Elements, pass through the Element itself.\n        readyCallback = function readyCallback() {\n          onReady(element);\n        };\n      }\n    }\n\n    useAttachEvent(element, 'ready', readyCallback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(function () {\n      if (elementRef.current === null && domNode.current !== null && (elements || checkoutSdk)) {\n        var newElement = null;\n\n        if (checkoutSdk) {\n          switch (type) {\n            case 'payment':\n              newElement = checkoutSdk.createPaymentElement(options);\n              break;\n\n            case 'address':\n              if ('mode' in options) {\n                var mode = options.mode,\n                    restOptions = _objectWithoutProperties(options, _excluded);\n\n                if (mode === 'shipping') {\n                  newElement = checkoutSdk.createShippingAddressElement(restOptions);\n                } else if (mode === 'billing') {\n                  newElement = checkoutSdk.createBillingAddressElement(restOptions);\n                } else {\n                  throw new Error(\"Invalid options.mode. mode must be 'billing' or 'shipping'.\");\n                }\n              } else {\n                throw new Error(\"You must supply options.mode. mode must be 'billing' or 'shipping'.\");\n              }\n\n              break;\n\n            case 'expressCheckout':\n              newElement = checkoutSdk.createExpressCheckoutElement(options);\n              break;\n\n            case 'currencySelector':\n              newElement = checkoutSdk.createCurrencySelectorElement();\n              break;\n\n            default:\n              throw new Error(\"Invalid Element type \".concat(displayName, \". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />.\"));\n          }\n        } else if (elements) {\n          newElement = elements.create(type, options);\n        } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n        elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n        setElement(newElement);\n\n        if (newElement) {\n          newElement.mount(domNode.current);\n        }\n      }\n    }, [elements, checkoutSdk, options]);\n    var prevOptions = usePrevious(options);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n      if (!elementRef.current) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n      if (updates && 'update' in elementRef.current) {\n        elementRef.current.update(updates);\n      }\n    }, [options, prevOptions]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(function () {\n      return function () {\n        if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n          try {\n            elementRef.current.destroy();\n            elementRef.current = null;\n          } catch (error) {// Do nothing\n          }\n        }\n      };\n    }, []);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      id: id,\n      className: className,\n      ref: domNode\n    });\n  }; // Only render the Element wrapper in a server environment.\n\n\n  var ServerElement = function ServerElement(props) {\n    useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var id = props.id,\n        className = props.className;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var Element = isServer ? ServerElement : ClientElement;\n  Element.propTypes = {\n    id: prop_types__WEBPACK_IMPORTED_MODULE_1__.string,\n    className: prop_types__WEBPACK_IMPORTED_MODULE_1__.string,\n    onChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onBlur: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onFocus: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onReady: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onEscape: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onClick: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onLoadError: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onLoaderStart: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onNetworksChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onConfirm: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onCancel: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onShippingAddressChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onShippingRateChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    options: prop_types__WEBPACK_IMPORTED_MODULE_1__.object\n  };\n  Element.displayName = displayName;\n  Element.__elementType = type;\n  return Element;\n};\n\nvar isServer = typeof window === 'undefined';\n\nvar EmbeddedCheckoutContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nEmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\nvar useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(EmbeddedCheckoutContext);\n\n  if (!ctx) {\n    throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n  }\n\n  return ctx;\n};\nvar INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n  }, [rawStripeProp]);\n  var embeddedCheckoutPromise = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  var loadedStripe = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    embeddedCheckout: null\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    // Don't support any ctx updates once embeddedCheckout or stripe is set.\n    if (loadedStripe.current || embeddedCheckoutPromise.current) {\n      return;\n    }\n\n    var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n      if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n      loadedStripe.current = stripe;\n      embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n        setContext({\n          embeddedCheckout: embeddedCheckout\n        });\n      });\n    }; // For an async stripePromise, store it once resolved\n\n\n    if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe) {\n          setStripeAndInitEmbeddedCheckout(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      // Or, handle a sync stripe instance going from null -> populated\n      setStripeAndInitEmbeddedCheckout(parsed.stripe);\n    }\n  }, [parsed, options, ctx, loadedStripe]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    // cleanup on unmount\n    return function () {\n      // If embedded checkout is fully initialized, destroy it.\n      if (ctx.embeddedCheckout) {\n        embeddedCheckoutPromise.current = null;\n        ctx.embeddedCheckout.destroy();\n      } else if (embeddedCheckoutPromise.current) {\n        // If embedded checkout is still initializing, destroy it once\n        // it's done. This could be caused by unmounting very quickly\n        // after mounting.\n        embeddedCheckoutPromise.current.then(function () {\n          embeddedCheckoutPromise.current = null;\n\n          if (ctx.embeddedCheckout) {\n            ctx.embeddedCheckout.destroy();\n          }\n        });\n      }\n    };\n  }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    registerWithStripeJs(loadedStripe);\n  }, [loadedStripe]); // Warn on changes to stripe prop.\n  // The stripe prop value can only go from null to non-null once and\n  // can't be changed after that.\n\n  var prevStripe = usePrevious(rawStripeProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n  var prevOptions = usePrevious(options);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevOptions == null) {\n      return;\n    }\n\n    if (options == null) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n      return;\n    }\n\n    if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n      console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n    }\n\n    if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n    }\n\n    if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n    }\n\n    if (prevOptions.onLineItemsChange != null && options.onLineItemsChange !== prevOptions.onLineItemsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.');\n    }\n  }, [prevOptions, options]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(EmbeddedCheckoutContext.Provider, {\n    value: ctx\n  }, children);\n};\n\nvar EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n  var id = _ref.id,\n      className = _ref.className;\n\n  var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n      embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n  var isMounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  var domNode = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(function () {\n    if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n      embeddedCheckout.mount(domNode.current);\n      isMounted.current = true;\n    } // Clean up on unmount\n\n\n    return function () {\n      if (isMounted.current && embeddedCheckout) {\n        try {\n          embeddedCheckout.unmount();\n          isMounted.current = false;\n        } catch (e) {// Do nothing.\n          // Parent effects are destroyed before child effects, so\n          // in cases where both the EmbeddedCheckoutProvider and\n          // the EmbeddedCheckout component are removed at the same\n          // time, the embeddedCheckout instance will be destroyed,\n          // which causes an error when calling unmount.\n        }\n      }\n    };\n  }, [embeddedCheckout]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    ref: domNode,\n    id: id,\n    className: className\n  });\n}; // Only render the wrapper in a server environment.\n\n\nvar EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n  var id = _ref2.id,\n      className = _ref2.className;\n  // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n  useEmbeddedCheckoutContext();\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    id: id,\n    className: className\n  });\n};\n\nvar EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n/**\n * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n */\n\nvar useStripe = function useStripe() {\n  var _useElementsOrCheckou = useElementsOrCheckoutSdkContextWithUseCase('calls useStripe()'),\n      stripe = _useElementsOrCheckou.stripe;\n\n  return stripe;\n};\n\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardElement = createElementComponent('card', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardNumberElement = createElementComponent('cardNumber', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardExpiryElement = createElementComponent('cardExpiry', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardCvcElement = createElementComponent('cardCvc', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar FpxBankElement = createElementComponent('fpxBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IbanElement = createElementComponent('iban', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IdealBankElement = createElementComponent('idealBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar P24BankElement = createElementComponent('p24Bank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar EpsBankElement = createElementComponent('epsBank', isServer);\nvar PaymentElement = createElementComponent('payment', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n */\n\nvar CurrencySelectorElement = createElementComponent('currencySelector', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AddressElement = createElementComponent('address', isServer);\n/**\n * @deprecated\n * Use `AddressElement` instead.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* binding */ loadStripe)\n/* harmony export */ });\nvar RELEASE_TRAIN = 'basil';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"7.4.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise$1 !== null) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise$1[\"catch\"](function (error) {\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"7.4.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise) {\n    return stripePromise;\n  }\n\n  stripePromise = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n  return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/lib/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* reexport safe */ _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__.loadStripe)\n/* harmony export */ });\n/* harmony import */ var _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dist/index.mjs */ \"(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmlwZS9zdHJpcGUtanMvbGliL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxub2RlX21vZHVsZXNcXEBzdHJpcGVcXHN0cmlwZS1qc1xcbGliXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vZGlzdC9pbmRleC5tanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs\n");

/***/ })

};
;