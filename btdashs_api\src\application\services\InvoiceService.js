const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const Stripe = require("stripe");

// Initialize Stripe (only if API key is provided)
const stripe = process.env.STRIPE_SECRET_KEY
	? new Stripe(process.env.STRIPE_SECRET_KEY, {
			apiVersion: "2024-12-18.acacia",
	  })
	: null;

class InvoiceService {
	/**
	 * Create a new invoice for a campaign payment
	 * @param {Object} invoiceData - Invoice creation data
	 * @param {number} invoiceData.managerId - User ID of the campaign manager
	 * @param {number} invoiceData.advertiserId - Company ID of the advertiser
	 * @param {string} invoiceData.customerId - Stripe customer ID
	 * @param {number} invoiceData.campaignId - Campaign ID
	 * @param {number} invoiceData.amount - Invoice amount
	 * @param {string} invoiceData.currency - Currency code
	 * @param {string} invoiceData.description - Invoice description
	 * @param {Array} invoiceData.lineItems - Array of line items
	 * @returns {Promise<Object>} Created invoice
	 */
	async createInvoice(invoiceData) {
		const trx = await db.transaction();

		try {
			const {
				managerId,
				advertiserId,
				customerId,
				campaignId,
				amount,
				currency = "USD",
				description,
				lineItems = [],
			} = invoiceData;

			const invoiceNumber = await this.generateInvoiceNumber(trx);

			const stripeInvoice = await this.createStripeInvoice({
				customerId,
				description,
				metadata: {
					campaign_id: campaignId?.toString(),
					manager_id: managerId.toString(),
					advertiser_id: advertiserId?.toString(),
					invoice_number: invoiceNumber,
				},
			});

			const [invoice] = await trx("dtm_ads.invoices")
				.insert({
					invoice_number: invoiceNumber,
					stripe_invoice_id: stripeInvoice.id,
					manager_id: managerId,
					advertiser_id: advertiserId,
					customer_id: customerId,
					campaign_id: campaignId,
					subtotal: amount,
					tax_amount: 0,
					total_amount: amount,
					currency: currency.toUpperCase(),
					status: "draft",
					description,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			if (lineItems.length > 0) {
				const lineItemsData = lineItems.map((item) => ({
					invoice_id: invoice.id,
					description: item.description,
					quantity: item.quantity || 1,
					unit_price: item.unitPrice,
					amount: (item.quantity || 1) * item.unitPrice,
					campaign_id: campaignId,
					created_at: new Date(),
					updated_at: new Date(),
				}));

				await trx("dtm_ads.invoice_line_items").insert(lineItemsData);
			} else {
				await trx("dtm_ads.invoice_line_items").insert({
					invoice_id: invoice.id,
					description: description || `Campaign payment for Campaign #${campaignId}`,
					quantity: 1,
					unit_price: amount,
					amount: amount,
					campaign_id: campaignId,
					created_at: new Date(),
					updated_at: new Date(),
				});
			}

			await trx.commit();

			logger.info("Invoice created successfully", {
				invoiceId: invoice.id,
				invoiceNumber,
				stripeInvoiceId: stripeInvoice.id,
				managerId,
				advertiserId,
				campaignId,
				amount,
			});

			return {
				...invoice,
				stripe_invoice: stripeInvoice,
			};
		} catch (error) {
			await trx.rollback();
			logger.error("Error creating invoice", { error, invoiceData });
			throw error;
		}
	}

	/**
	 * Create Stripe invoice
	 * @param {Object} data - Stripe invoice data
	 * @returns {Promise<Object>} Stripe invoice
	 */
	async createStripeInvoice(data) {
		try {
			if (!stripe) {
				throw new Error("Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.");
			}

			const { customerId, description, metadata } = data;

			// Create invoice in Stripe
			const invoice = await stripe.invoices.create({
				customer: customerId,
				description,
				metadata,
				auto_advance: false, // Don't automatically finalize
				collection_method: "charge_automatically",
			});

			return invoice;
		} catch (error) {
			logger.error("Error creating Stripe invoice", { error, data });
			throw new Error(`Failed to create Stripe invoice: ${error.message}`);
		}
	}

	/**
	 * Finalize and send invoice
	 * @param {number} invoiceId - Database invoice ID
	 * @returns {Promise<Object>} Updated invoice
	 */
	async finalizeInvoice(invoiceId) {
		const trx = await db.transaction();

		try {
			if (!stripe) {
				throw new Error("Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.");
			}

			// Get invoice
			const invoice = await trx("dtm_ads.invoices").where({ id: invoiceId }).first();

			if (!invoice) {
				throw new Error("Invoice not found");
			}

			if (invoice.status !== "draft") {
				throw new Error("Only draft invoices can be finalized");
			}

			// Finalize Stripe invoice
			const stripeInvoice = await stripe.invoices.finalizeInvoice(invoice.stripe_invoice_id);

			// Update database invoice
			const [updatedInvoice] = await trx("dtm_ads.invoices")
				.where({ id: invoiceId })
				.update({
					status: "open",
					updated_at: new Date(),
				})
				.returning("*");

			await trx.commit();

			logger.info("Invoice finalized", {
				invoiceId,
				invoiceNumber: invoice.invoice_number,
				stripeInvoiceId: invoice.stripe_invoice_id,
			});

			return {
				...updatedInvoice,
				stripe_invoice: stripeInvoice,
			};
		} catch (error) {
			await trx.rollback();
			logger.error("Error finalizing invoice", { error, invoiceId });
			throw error;
		}
	}

	/**
	 * Mark invoice as paid
	 * @param {string} paymentIntentId - Payment intent ID
	 * @param {string} paymentMethod - Payment method used
	 * @returns {Promise<Object>} Updated invoice
	 */
	async markInvoicePaid(paymentIntentId, paymentMethod = "stripe") {
		const trx = await db.transaction();

		try {
			// Find invoice by payment intent ID
			const invoice = await trx("dtm_ads.invoices").where({ payment_intent_id: paymentIntentId }).first();

			if (!invoice) {
				throw new Error("Invoice not found for payment intent");
			}

			// Update invoice status
			const [updatedInvoice] = await trx("dtm_ads.invoices")
				.where({ id: invoice.id })
				.update({
					status: "paid",
					payment_method: paymentMethod,
					paid_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			await trx.commit();

			logger.info("Invoice marked as paid", {
				invoiceId: invoice.id,
				invoiceNumber: invoice.invoice_number,
				paymentIntentId,
				paymentMethod,
			});

			return updatedInvoice;
		} catch (error) {
			await trx.rollback();
			logger.error("Error marking invoice as paid", { error, paymentIntentId });
			throw error;
		}
	}

	/**
	 * Get invoice with line items
	 * @param {number} invoiceId - Invoice ID
	 * @returns {Promise<Object>} Invoice with line items
	 */
	async getInvoiceWithLineItems(invoiceId) {
		try {
			const invoice = await db("dtm_ads.invoices").where({ id: invoiceId }).first();

			if (!invoice) {
				throw new Error("Invoice not found");
			}

			const lineItems = await db("dtm_ads.invoice_line_items").where({ invoice_id: invoiceId });

			return {
				...invoice,
				line_items: lineItems,
			};
		} catch (error) {
			logger.error("Error getting invoice with line items", { error, invoiceId });
			throw error;
		}
	}

	/**
	 * Generate unique invoice number
	 * @param {Object} trx - Database transaction
	 * @returns {Promise<string>} Invoice number
	 */
	async generateInvoiceNumber(trx) {
		try {
			const result = await trx.raw("SELECT dtm_ads.generate_invoice_number() as invoice_number");
			return result.rows[0].invoice_number;
		} catch (error) {
			logger.error("Error generating invoice number", { error });
			throw error;
		}
	}

	/**
	 * Associate payment intent with invoice
	 * @param {number} invoiceId - Invoice ID
	 * @param {string} paymentIntentId - Payment intent ID
	 * @returns {Promise<Object>} Updated invoice
	 */
	async associatePaymentIntent(invoiceId, paymentIntentId) {
		try {
			const [updatedInvoice] = await db("dtm_ads.invoices")
				.where({ id: invoiceId })
				.update({
					payment_intent_id: paymentIntentId,
					updated_at: new Date(),
				})
				.returning("*");

			logger.info("Payment intent associated with invoice", {
				invoiceId,
				paymentIntentId,
			});

			return updatedInvoice;
		} catch (error) {
			logger.error("Error associating payment intent with invoice", {
				error,
				invoiceId,
				paymentIntentId,
			});
			throw error;
		}
	}
}

module.exports = InvoiceService;
