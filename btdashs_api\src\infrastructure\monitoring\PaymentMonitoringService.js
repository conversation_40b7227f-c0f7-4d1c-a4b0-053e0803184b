const logger = require("../logging/logger");
const db = require("../database/knex");

/**
 * Service for monitoring payment failures and system health
 */
class PaymentMonitoringService {
	constructor() {
		this.alertThresholds = {
			paymentFailureRate: 0.15, // 15% failure rate triggers alert
			consecutiveFailures: 3, // 3 consecutive failures for same campaign
			systemErrorRate: 0.05, // 5% system error rate
			responseTimeThreshold: 5000, // 5 seconds
		};
		
		this.alertCooldown = 30 * 60 * 1000; // 30 minutes between similar alerts
		this.lastAlerts = new Map(); // Track last alert times
	}

	/**
	 * Monitor payment failures and trigger alerts
	 * @returns {Promise<Object>} Monitoring summary
	 */
	async monitorPaymentFailures() {
		try {
			const now = new Date();
			const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
			const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

			// Get payment statistics for the last hour
			const hourlyStats = await this.getPaymentStats(oneHourAgo, now);
			
			// Get payment statistics for the last day
			const dailyStats = await this.getPaymentStats(oneDayAgo, now);

			// Check for critical issues
			const alerts = [];

			// Check hourly failure rate
			if (hourlyStats.failureRate > this.alertThresholds.paymentFailureRate) {
				alerts.push(await this.createAlert({
					type: "high_payment_failure_rate",
					severity: "critical",
					message: `Payment failure rate is ${(hourlyStats.failureRate * 100).toFixed(1)}% in the last hour`,
					data: hourlyStats,
					timeframe: "1_hour"
				}));
			}

			// Check for consecutive failures
			const consecutiveFailures = await this.getConsecutiveFailures();
			for (const failure of consecutiveFailures) {
				if (failure.consecutive_count >= this.alertThresholds.consecutiveFailures) {
					alerts.push(await this.createAlert({
						type: "consecutive_payment_failures",
						severity: "high",
						message: `Campaign ${failure.campaign_id} has ${failure.consecutive_count} consecutive payment failures`,
						data: failure,
						campaignId: failure.campaign_id
					}));
				}
			}

			// Check system health
			const systemHealth = await this.checkSystemHealth();
			if (systemHealth.errorRate > this.alertThresholds.systemErrorRate) {
				alerts.push(await this.createAlert({
					type: "high_system_error_rate",
					severity: "critical",
					message: `System error rate is ${(systemHealth.errorRate * 100).toFixed(1)}%`,
					data: systemHealth
				}));
			}

			// Send alerts
			for (const alert of alerts) {
				if (alert && this.shouldSendAlert(alert)) {
					await this.sendAlert(alert);
					this.lastAlerts.set(alert.type + alert.campaignId, now);
				}
			}

			return {
				timestamp: now.toISOString(),
				hourlyStats,
				dailyStats,
				systemHealth,
				alertsTriggered: alerts.length,
				alerts: alerts.filter(Boolean)
			};

		} catch (error) {
			logger.error("Error in payment monitoring", { error: error.message });
			throw error;
		}
	}

	/**
	 * Get payment statistics for a time period
	 * @param {Date} startTime - Start time
	 * @param {Date} endTime - End time
	 * @returns {Promise<Object>} Payment statistics
	 */
	async getPaymentStats(startTime, endTime) {
		try {
			// Get payment attempts from billing logs or payment history
			const paymentAttempts = await db("dtm_ads.payment_logs")
				.whereBetween("created_at", [startTime, endTime])
				.select("status", "campaign_id", "amount", "error_message");

			const totalAttempts = paymentAttempts.length;
			const failures = paymentAttempts.filter(p => p.status === "failed");
			const successes = paymentAttempts.filter(p => p.status === "completed");

			const failureRate = totalAttempts > 0 ? failures.length / totalAttempts : 0;
			const successRate = totalAttempts > 0 ? successes.length / totalAttempts : 0;

			// Group failures by reason
			const failureReasons = {};
			failures.forEach(failure => {
				const reason = failure.error_message || "unknown";
				failureReasons[reason] = (failureReasons[reason] || 0) + 1;
			});

			// Calculate total failed amount
			const totalFailedAmount = failures.reduce((sum, f) => sum + (parseFloat(f.amount) || 0), 0);

			return {
				timeframe: {
					start: startTime.toISOString(),
					end: endTime.toISOString()
				},
				totalAttempts,
				successes: successes.length,
				failures: failures.length,
				failureRate,
				successRate,
				totalFailedAmount,
				failureReasons,
				uniqueCampaigns: new Set(paymentAttempts.map(p => p.campaign_id)).size
			};

		} catch (error) {
			logger.error("Error getting payment stats", { error: error.message });
			return {
				totalAttempts: 0,
				successes: 0,
				failures: 0,
				failureRate: 0,
				successRate: 0,
				totalFailedAmount: 0,
				failureReasons: {},
				uniqueCampaigns: 0
			};
		}
	}

	/**
	 * Get campaigns with consecutive payment failures
	 * @returns {Promise<Array>} Campaigns with consecutive failures
	 */
	async getConsecutiveFailures() {
		try {
			// This would need to be implemented based on your payment log structure
			// For now, return a simplified version
			const recentFailures = await db("dtm_ads.payment_logs")
				.where("status", "failed")
				.where("created_at", ">", new Date(Date.now() - 24 * 60 * 60 * 1000))
				.orderBy("created_at", "desc")
				.select("campaign_id", "created_at");

			// Group by campaign and count consecutive failures
			const campaignFailures = {};
			recentFailures.forEach(failure => {
				if (!campaignFailures[failure.campaign_id]) {
					campaignFailures[failure.campaign_id] = 0;
				}
				campaignFailures[failure.campaign_id]++;
			});

			return Object.entries(campaignFailures)
				.filter(([_, count]) => count >= this.alertThresholds.consecutiveFailures)
				.map(([campaignId, count]) => ({
					campaign_id: parseInt(campaignId),
					consecutive_count: count
				}));

		} catch (error) {
			logger.error("Error getting consecutive failures", { error: error.message });
			return [];
		}
	}

	/**
	 * Check overall system health
	 * @returns {Promise<Object>} System health metrics
	 */
	async checkSystemHealth() {
		try {
			const now = new Date();
			const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

			// Check database connectivity
			const dbHealth = await this.checkDatabaseHealth();
			
			// Check API response times (if you have API logs)
			const apiHealth = await this.checkApiHealth(oneHourAgo, now);

			// Check ad serving health
			const adServingHealth = await this.checkAdServingHealth(oneHourAgo, now);

			const overallHealth = dbHealth.healthy && apiHealth.healthy && adServingHealth.healthy;
			const errorRate = (apiHealth.errorCount + adServingHealth.errorCount) / 
				Math.max(apiHealth.totalRequests + adServingHealth.totalRequests, 1);

			return {
				healthy: overallHealth,
				errorRate,
				database: dbHealth,
				api: apiHealth,
				adServing: adServingHealth,
				timestamp: now.toISOString()
			};

		} catch (error) {
			logger.error("Error checking system health", { error: error.message });
			return {
				healthy: false,
				errorRate: 1,
				error: error.message
			};
		}
	}

	/**
	 * Check database health
	 * @returns {Promise<Object>} Database health status
	 */
	async checkDatabaseHealth() {
		try {
			const start = Date.now();
			await db.raw("SELECT 1");
			const responseTime = Date.now() - start;

			return {
				healthy: responseTime < this.alertThresholds.responseTimeThreshold,
				responseTime,
				timestamp: new Date().toISOString()
			};
		} catch (error) {
			return {
				healthy: false,
				error: error.message,
				timestamp: new Date().toISOString()
			};
		}
	}

	/**
	 * Check API health
	 * @param {Date} startTime - Start time
	 * @param {Date} endTime - End time
	 * @returns {Promise<Object>} API health metrics
	 */
	async checkApiHealth(startTime, endTime) {
		// This would integrate with your API logging system
		// For now, return a mock implementation
		return {
			healthy: true,
			totalRequests: 100,
			errorCount: 2,
			averageResponseTime: 150,
			timestamp: new Date().toISOString()
		};
	}

	/**
	 * Check ad serving health
	 * @param {Date} startTime - Start time
	 * @param {Date} endTime - End time
	 * @returns {Promise<Object>} Ad serving health metrics
	 */
	async checkAdServingHealth(startTime, endTime) {
		// This would check ad serving logs
		// For now, return a mock implementation
		return {
			healthy: true,
			totalRequests: 500,
			errorCount: 5,
			fallbackRate: 0.02,
			timestamp: new Date().toISOString()
		};
	}

	/**
	 * Create an alert object
	 * @param {Object} alertData - Alert data
	 * @returns {Promise<Object>} Alert object
	 */
	async createAlert(alertData) {
		const alert = {
			id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			timestamp: new Date().toISOString(),
			...alertData
		};

		// Store alert in database
		try {
			const alertExists = await db.schema.hasTable("dtm_ads.system_alerts");
			if (alertExists) {
				await db("dtm_ads.system_alerts").insert({
					alert_id: alert.id,
					type: alert.type,
					severity: alert.severity,
					message: alert.message,
					data: JSON.stringify(alert.data),
					campaign_id: alert.campaignId || null,
					created_at: new Date()
				});
			}
		} catch (error) {
			logger.error("Error storing alert", { error: error.message, alert });
		}

		return alert;
	}

	/**
	 * Check if an alert should be sent (respects cooldown)
	 * @param {Object} alert - Alert object
	 * @returns {boolean} Whether to send the alert
	 */
	shouldSendAlert(alert) {
		const alertKey = alert.type + (alert.campaignId || "");
		const lastAlert = this.lastAlerts.get(alertKey);
		
		if (!lastAlert) return true;
		
		const timeSinceLastAlert = Date.now() - lastAlert.getTime();
		return timeSinceLastAlert > this.alertCooldown;
	}

	/**
	 * Send alert to configured channels
	 * @param {Object} alert - Alert to send
	 */
	async sendAlert(alert) {
		try {
			logger.error("SYSTEM ALERT", alert);

			// Send to webhook if configured
			if (process.env.ALERT_WEBHOOK_URL) {
				await fetch(process.env.ALERT_WEBHOOK_URL, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"Authorization": `Bearer ${process.env.ALERT_WEBHOOK_TOKEN}`
					},
					body: JSON.stringify({
						source: "btdash-payment-monitoring",
						alert
					})
				});
			}

			// Send email alert if configured
			if (process.env.ALERT_EMAIL_ENABLED === "true") {
				await this.sendEmailAlert(alert);
			}

			// Send Slack alert if configured
			if (process.env.SLACK_WEBHOOK_URL) {
				await this.sendSlackAlert(alert);
			}

		} catch (error) {
			logger.error("Error sending alert", { error: error.message, alert });
		}
	}

	/**
	 * Send email alert
	 * @param {Object} alert - Alert to send
	 */
	async sendEmailAlert(alert) {
		// Implementation would depend on your email service
		logger.info("Email alert would be sent", { alert });
	}

	/**
	 * Send Slack alert
	 * @param {Object} alert - Alert to send
	 */
	async sendSlackAlert(alert) {
		try {
			const color = alert.severity === "critical" ? "danger" : 
						 alert.severity === "high" ? "warning" : "good";

			const payload = {
				text: `🚨 BTDash System Alert: ${alert.message}`,
				attachments: [{
					color,
					fields: [
						{ title: "Type", value: alert.type, short: true },
						{ title: "Severity", value: alert.severity, short: true },
						{ title: "Timestamp", value: alert.timestamp, short: false }
					]
				}]
			};

			await fetch(process.env.SLACK_WEBHOOK_URL, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(payload)
			});

		} catch (error) {
			logger.error("Error sending Slack alert", { error: error.message });
		}
	}
}

module.exports = PaymentMonitoringService;
