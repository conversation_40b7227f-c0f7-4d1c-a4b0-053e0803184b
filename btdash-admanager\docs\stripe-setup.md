# Stripe Payment Integration Setup Guide

This guide walks you through setting up Stripe payment integration for the BTDash Ad Manager.

## Prerequisites

- Stripe account (create at [stripe.com](https://stripe.com))
- Node.js 18+ installed
- Access to the BTDash Ad Manager codebase

## 1. Environment Configuration

### Step 1: Copy Environment Template

```bash
cp .env.example .env.local
```

### Step 2: Configure Stripe Keys

Edit `.env.local` and fill in your Stripe configuration:

```env
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY='pk_test_51...'  # From Stripe Dashboard
STRIPE_SECRET_KEY='sk_test_51...'                   # From Stripe Dashboard  
STRIPE_WEBHOOK_SECRET='whsec_...'                   # From Webhook Configuration
STRIPE_CURRENCY='usd'                               # Your preferred currency
STRIPE_COUNTRY='US'                                 # Your country code
```

## 2. Stripe Dashboard Setup

### Step 1: Get API Keys

1. Log into your [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers > API keys**
3. Copy your **Publishable key** (starts with `pk_test_` or `pk_live_`)
4. Copy your **Secret key** (starts with `sk_test_` or `sk_live_`)

### Step 2: Configure Webhooks

1. Go to **Developers > Webhooks** in your Stripe Dashboard
2. Click **Add endpoint**
3. Set the endpoint URL:
   - **Development**: `https://your-ngrok-url.ngrok.io/api/stripe/webhook`
   - **Production**: `https://your-domain.com/api/stripe/webhook`
4. Select events to send:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
5. Click **Add endpoint**
6. Copy the **Signing secret** (starts with `whsec_`)

### Step 3: Test Mode vs Live Mode

**For Development:**
- Use test keys (starting with `pk_test_` and `sk_test_`)
- Use test card numbers for payments
- No real money is processed

**For Production:**
- Complete Stripe account verification
- Use live keys (starting with `pk_live_` and `sk_live_`)
- Real payments will be processed

## 3. Development Setup

### Step 1: Install Dependencies

Dependencies are already included in `package.json`:
- `@stripe/stripe-js` - Client-side Stripe
- `@stripe/react-stripe-js` - React components
- `stripe` - Server-side Stripe

### Step 2: Start Development Server

```bash
npm run dev
```

### Step 3: Expose Webhook Endpoint (Development)

For local development, use ngrok to expose your webhook:

```bash
# Install ngrok
npm install -g ngrok

# Expose port 3000
ngrok http 3000

# Use the HTTPS URL for your webhook endpoint
```

### Step 4: Verify Configuration

The application will automatically validate your Stripe configuration on startup. Check the console for:

- ✅ **Success**: "Stripe configuration is valid"
- ❌ **Errors**: Configuration issues that must be fixed
- ⚠️ **Warnings**: Non-critical issues to be aware of

## 4. Testing Payment Flows

### Test Card Numbers

Use these test cards in development:

```
# Successful payments
****************  # Visa
****************  # Visa (debit)

# Failed payments  
****************  # Card declined
****************  # Insufficient funds

# 3D Secure
****************  # Requires authentication
```

### Test Scenarios

1. **Campaign Payment Flow**:
   - Create a campaign
   - Submit for admin approval
   - Admin approves campaign
   - User pays for campaign
   - Campaign becomes active

2. **Payment Failure Handling**:
   - Use declined test card
   - Verify error handling
   - Test payment retry flow

## 5. Configuration Validation

The system includes automatic validation that checks:

### Required Variables
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
- `STRIPE_SECRET_KEY` 
- `STRIPE_WEBHOOK_SECRET`

### Key Format Validation
- Publishable keys start with `pk_`
- Secret keys start with `sk_`
- Webhook secrets start with `whsec_`

### Environment Consistency
- Test/live key matching
- Environment mode warnings

## 6. Troubleshooting

### Common Issues

**"Missing Stripe publishable key" Error**
- Ensure `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` is set in `.env.local`
- Restart the development server after adding environment variables

**Webhook Not Receiving Events**
- Verify webhook URL is accessible (use ngrok for local development)
- Check webhook events are configured correctly
- Verify webhook secret matches your environment variable

**Payment Intent Creation Fails**
- Check server-side secret key is valid
- Verify API_BASE_URL points to running backend
- Check INTERNAL_API_KEY matches backend configuration

### Debug Mode

Enable debug logging by setting:

```env
DEBUG_STRIPE='true'
```

This will log detailed Stripe operation information to the console.

## 7. Production Deployment

### Pre-deployment Checklist

- [ ] Stripe account fully verified
- [ ] Live API keys configured
- [ ] Webhook endpoint uses HTTPS
- [ ] Webhook secret updated for production
- [ ] Payment flow tested end-to-end
- [ ] Error handling verified
- [ ] Monitoring and alerting configured

### Security Considerations

- Never commit `.env.local` to version control
- Use different API keys for each environment
- Regularly rotate API keys
- Monitor webhook endpoints for suspicious activity
- Implement rate limiting on payment endpoints

## 8. Support

For issues with this integration:
1. Check the troubleshooting section above
2. Review Stripe's [documentation](https://stripe.com/docs)
3. Check the application logs for detailed error messages
4. Contact the development team with specific error details
