#!/usr/bin/env node

/**
 * Stripe Configuration Validation Script
 * 
 * This script validates your Stripe environment configuration
 * and provides detailed feedback on any issues.
 * 
 * Usage:
 *   node scripts/validate-stripe-config.js
 *   npm run validate:stripe
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function loadEnvironmentVariables() {
  // Try to load .env.local first, then .env
  const envFiles = ['.env.local', '.env'];
  let envLoaded = false;

  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      
      // Parse environment variables
      envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').replace(/^['"]|['"]$/g, '');
            process.env[key] = value;
          }
        }
      });
      
      console.log(colorize(`✓ Loaded environment from ${envFile}`, 'green'));
      envLoaded = true;
      break;
    }
  }

  if (!envLoaded) {
    console.log(colorize('⚠ No .env.local or .env file found', 'yellow'));
  }
}

function validateStripeConfiguration() {
  const errors = [];
  const warnings = [];
  const info = [];

  // Get environment variables
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const secretKey = process.env.STRIPE_SECRET_KEY;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  const currency = process.env.STRIPE_CURRENCY || 'usd';
  const country = process.env.STRIPE_COUNTRY || 'US';
  const nodeEnv = process.env.NODE_ENV || 'development';

  // Required variables validation
  if (!publishableKey) {
    errors.push('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is missing');
  } else if (!publishableKey.startsWith('pk_')) {
    errors.push('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY must start with "pk_test_" or "pk_live_"');
  } else {
    info.push(`Publishable key: ${publishableKey.substring(0, 12)}...`);
  }

  if (!secretKey) {
    errors.push('STRIPE_SECRET_KEY is missing');
  } else if (!secretKey.startsWith('sk_')) {
    errors.push('STRIPE_SECRET_KEY must start with "sk_test_" or "sk_live_"');
  } else {
    info.push(`Secret key: ${secretKey.substring(0, 12)}...`);
  }

  if (!webhookSecret) {
    errors.push('STRIPE_WEBHOOK_SECRET is missing');
  } else if (!webhookSecret.startsWith('whsec_')) {
    errors.push('STRIPE_WEBHOOK_SECRET must start with "whsec_"');
  } else {
    info.push(`Webhook secret: ${webhookSecret.substring(0, 12)}...`);
  }

  // Key consistency validation
  if (publishableKey && secretKey) {
    const pubKeyIsTest = publishableKey.startsWith('pk_test_');
    const secretKeyIsTest = secretKey.startsWith('sk_test_');
    
    if (pubKeyIsTest !== secretKeyIsTest) {
      errors.push('Publishable key and secret key must both be test keys or both be live keys');
    } else {
      const keyType = pubKeyIsTest ? 'test' : 'live';
      info.push(`Key type: ${keyType} mode`);
    }

    // Environment consistency warnings
    if (pubKeyIsTest && nodeEnv === 'production') {
      warnings.push('Using Stripe test keys in production environment');
    }
    
    if (!pubKeyIsTest && nodeEnv === 'development') {
      warnings.push('Using Stripe live keys in development environment');
    }
  }

  // Currency validation
  const validCurrencies = ['usd', 'eur', 'gbp', 'cad', 'aud', 'jpy', 'chf', 'sek', 'nok', 'dkk'];
  if (!validCurrencies.includes(currency.toLowerCase())) {
    warnings.push(`Currency "${currency}" may not be supported by Stripe`);
  } else {
    info.push(`Currency: ${currency.toUpperCase()}`);
  }

  // Country validation
  info.push(`Country: ${country}`);

  // Additional environment variables
  const apiBaseUrl = process.env.API_BASE_URL;
  const internalApiKey = process.env.INTERNAL_API_KEY;

  if (!apiBaseUrl) {
    warnings.push('API_BASE_URL is not set - payment processing may fail');
  } else {
    info.push(`API Base URL: ${apiBaseUrl}`);
  }

  if (!internalApiKey) {
    warnings.push('INTERNAL_API_KEY is not set - webhook processing may fail');
  }

  return { errors, warnings, info };
}

function printResults(validation) {
  console.log('\n' + colorize('='.repeat(60), 'blue'));
  console.log(colorize('  STRIPE CONFIGURATION VALIDATION RESULTS', 'bold'));
  console.log(colorize('='.repeat(60), 'blue'));

  // Print info
  if (validation.info.length > 0) {
    console.log('\n' + colorize('📋 Configuration Info:', 'blue'));
    validation.info.forEach(item => {
      console.log(`  ${item}`);
    });
  }

  // Print errors
  if (validation.errors.length > 0) {
    console.log('\n' + colorize('❌ Errors (must be fixed):', 'red'));
    validation.errors.forEach(error => {
      console.log(`  ${colorize('✗', 'red')} ${error}`);
    });
  }

  // Print warnings
  if (validation.warnings.length > 0) {
    console.log('\n' + colorize('⚠️  Warnings:', 'yellow'));
    validation.warnings.forEach(warning => {
      console.log(`  ${colorize('!', 'yellow')} ${warning}`);
    });
  }

  // Overall status
  console.log('\n' + colorize('Overall Status:', 'bold'));
  if (validation.errors.length === 0) {
    if (validation.warnings.length === 0) {
      console.log(colorize('  ✅ Configuration is valid and ready for use', 'green'));
    } else {
      console.log(colorize('  ✅ Configuration is valid but has warnings', 'green'));
    }
  } else {
    console.log(colorize('  ❌ Configuration has errors that must be fixed', 'red'));
  }

  console.log('\n' + colorize('='.repeat(60), 'blue'));
}

function printHelp() {
  console.log('\n' + colorize('Next Steps:', 'bold'));
  
  if (!fs.existsSync('.env.local') && !fs.existsSync('.env')) {
    console.log('1. Copy .env.example to .env.local:');
    console.log('   cp .env.example .env.local');
    console.log('');
  }
  
  console.log('2. Get your Stripe API keys:');
  console.log('   - Visit https://dashboard.stripe.com/apikeys');
  console.log('   - Copy your publishable and secret keys');
  console.log('');
  
  console.log('3. Set up webhooks:');
  console.log('   - Visit https://dashboard.stripe.com/webhooks');
  console.log('   - Add endpoint for your application');
  console.log('   - Copy the webhook signing secret');
  console.log('');
  
  console.log('4. Update your .env.local file with the keys');
  console.log('');
  
  console.log('5. Run this script again to validate:');
  console.log('   node scripts/validate-stripe-config.js');
  console.log('');
  
  console.log('For detailed setup instructions, see:');
  console.log('   docs/stripe-setup.md');
}

// Main execution
function main() {
  console.log(colorize('🔍 Validating Stripe Configuration...', 'bold'));
  
  loadEnvironmentVariables();
  const validation = validateStripeConfiguration();
  printResults(validation);
  
  if (validation.errors.length > 0 || validation.warnings.length > 0) {
    printHelp();
  }
  
  // Exit with error code if there are errors
  process.exit(validation.errors.length > 0 ? 1 : 0);
}

if (require.main === module) {
  main();
}

module.exports = { validateStripeConfiguration, loadEnvironmentVariables };
