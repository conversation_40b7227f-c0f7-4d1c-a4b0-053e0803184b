"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { Download, Filter, RefreshCw, Search } from "lucide-react";
import { useState } from "react";

interface PaymentAnalytics {
	overview: {
		total_payment_attempts: number;
		successful_payments: number;
		failed_payments: number;
		success_rate: string;
		total_revenue: number;
		total_failed_amount: number;
	};
	health: {
		status: string;
		issues: string[];
		recommendations: string[];
	};
}

export default function AdminPaymentsPage() {
	const [loading, setLoading] = useState(false);
	const [analytics, setAnalytics] = useState<PaymentAnalytics | null>(null);
	const [analyticsLoading, setAnalyticsLoading] = useState(true);

	const fetchAnalytics = async () => {
		try {
			const response = await fetch("/api/billing/health-report");
			if (response.ok) {
				const data = await response.json();
				setAnalytics(data.data);
			}
		} catch (error) {
			console.error("Failed to fetch payment analytics:", error);
		} finally {
			setAnalyticsLoading(false);
		}
	};

	useEffect(() => {
		fetchAnalytics();
	}, []);

	const handleSaveSettings = () => {
		setLoading(true);
		setTimeout(() => {
			setLoading(false);
			toast({
				title: "Settings saved",
				description: "Payment settings have been updated successfully.",
			});
		}, 1500);
	};

	const getHealthStatusColor = (status: string) => {
		switch (status) {
			case "healthy":
				return "text-green-600";
			case "warning":
				return "text-yellow-600";
			case "critical":
				return "text-red-600";
			default:
				return "text-gray-600";
		}
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Payments</h1>
				<div className="flex gap-2">
					<Button variant="outline">
						<RefreshCw className="mr-2 h-4 w-4" />
						Sync with Stripe
					</Button>
					<Button variant="outline">
						<Download className="mr-2 h-4 w-4" />
						Export
					</Button>
				</div>
			</div>

			{analytics?.health && (
				<Alert className="mb-6">
					<div className="flex items-center gap-2">
						{analytics.health.status === "healthy" ? (
							<CheckCircle className="h-5 w-5 text-green-600" />
						) : (
							<AlertTriangle className="h-5 w-5 text-yellow-600" />
						)}
						<AlertDescription>
							<span className={`font-semibold ${getHealthStatusColor(analytics.health.status)}`}>
								Payment System Status: {analytics.health.status.toUpperCase()}
							</span>
							{analytics.health.issues.length > 0 && (
								<div className="mt-2">
									<p className="font-medium">Issues:</p>
									<ul className="list-disc list-inside">
										{analytics.health.issues.map((issue, index) => (
											<li key={index}>{issue}</li>
										))}
									</ul>
								</div>
							)}
						</AlertDescription>
					</div>
				</Alert>
			)}

			<Tabs defaultValue="overview" className="space-y-4">
				<TabsList>
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="analytics">Analytics</TabsTrigger>
					<TabsTrigger value="transactions">Transactions</TabsTrigger>
					<TabsTrigger value="customers">Customers</TabsTrigger>
					<TabsTrigger value="settings">Settings</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
								<TrendingUp className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									$
									{analyticsLoading ? "..." : analytics?.overview.total_revenue?.toFixed(2) || "0.00"}
								</div>
								<p className="text-xs text-muted-foreground">
									{analytics?.overview.successful_payments || 0} successful payments
								</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Success Rate</CardTitle>
								<CheckCircle className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{analyticsLoading ? "..." : analytics?.overview.success_rate || "0%"}
								</div>
								<p className="text-xs text-muted-foreground">
									{analytics?.overview.total_payment_attempts || 0} total attempts
								</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Failed Amount</CardTitle>
								<TrendingDown className="h-4 w-4 text-red-500" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									$
									{analyticsLoading
										? "..."
										: analytics?.overview.total_failed_amount?.toFixed(2) || "0.00"}
								</div>
								<p className="text-xs text-muted-foreground">
									{analytics?.overview.failed_payments || 0} failed payments
								</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Payment Health</CardTitle>
								{analytics?.health.status === "healthy" ? (
									<CheckCircle className="h-4 w-4 text-green-500" />
								) : (
									<AlertTriangle className="h-4 w-4 text-yellow-500" />
								)}
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold capitalize">
									{analyticsLoading ? "..." : analytics?.health.status || "Unknown"}
								</div>
								<p className="text-xs text-muted-foreground">
									{analytics?.health.issues.length || 0} issues detected
								</p>
							</CardContent>
						</Card>
					</div>

					<Card>
						<CardHeader>
							<CardTitle>Revenue Breakdown</CardTitle>
							<CardDescription>Revenue by subscription plan</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="space-y-2">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<div className="h-4 w-4 rounded-full bg-blue-500"></div>
											<span className="text-sm font-medium">Pro Plan</span>
										</div>
										<span className="text-sm font-medium">$14,935 (60%)</span>
									</div>
									<div className="h-2 w-full rounded-full bg-muted">
										<div className="h-2 rounded-full bg-blue-500" style={{ width: "60%" }}></div>
									</div>
								</div>
								<div className="space-y-2">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<div className="h-4 w-4 rounded-full bg-green-500"></div>
											<span className="text-sm font-medium">Basic Plan</span>
										</div>
										<span className="text-sm font-medium">$6,223 (25%)</span>
									</div>
									<div className="h-2 w-full rounded-full bg-muted">
										<div className="h-2 rounded-full bg-green-500" style={{ width: "25%" }}></div>
									</div>
								</div>
								<div className="space-y-2">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<div className="h-4 w-4 rounded-full bg-purple-500"></div>
											<span className="text-sm font-medium">Enterprise Plan</span>
										</div>
										<span className="text-sm font-medium">$3,734 (15%)</span>
									</div>
									<div className="h-2 w-full rounded-full bg-muted">
										<div className="h-2 rounded-full bg-purple-500" style={{ width: "15%" }}></div>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="transactions" className="space-y-4">
					<Card>
						<CardHeader>
							<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
								<div>
									<CardTitle>Recent Transactions</CardTitle>
									<CardDescription>View and manage payment transactions</CardDescription>
								</div>
								<div className="flex gap-2">
									<div className="relative">
										<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
										<Input
											type="search"
											placeholder="Search transactions..."
											className="w-full rounded-md pl-8 md:w-[200px] lg:w-[300px]"
										/>
									</div>
									<Button variant="outline" size="icon">
										<Filter className="h-4 w-4" />
										<span className="sr-only">Filter</span>
									</Button>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="rounded-md border">
								<div className="grid grid-cols-6 gap-4 p-4 font-medium">
									<div>Transaction ID</div>
									<div>Customer</div>
									<div>Date</div>
									<div>Amount</div>
									<div>Status</div>
									<div className="text-right">Actions</div>
								</div>
								<Separator />
								<div className="grid grid-cols-6 gap-4 p-4 items-center">
									<div className="font-medium">TRX-001</div>
									<div className="text-sm">John Doe</div>
									<div className="text-sm">June 15, 2025</div>
									<div className="text-sm">$49.00</div>
									<div>
										<Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
											Completed
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
								<Separator />
								<div className="grid grid-cols-6 gap-4 p-4 items-center">
									<div className="font-medium">TRX-002</div>
									<div className="text-sm">Jane Smith</div>
									<div className="text-sm">June 14, 2025</div>
									<div className="text-sm">$99.00</div>
									<div>
										<Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
											Completed
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
								<Separator />
								<div className="grid grid-cols-6 gap-4 p-4 items-center">
									<div className="font-medium">TRX-003</div>
									<div className="text-sm">Robert Johnson</div>
									<div className="text-sm">June 13, 2025</div>
									<div className="text-sm">$149.00</div>
									<div>
										<Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100">
											Pending
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
								<Separator />
								<div className="grid grid-cols-6 gap-4 p-4 items-center">
									<div className="font-medium">TRX-004</div>
									<div className="text-sm">Sarah Williams</div>
									<div className="text-sm">June 12, 2025</div>
									<div className="text-sm">$49.00</div>
									<div>
										<Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
											Failed
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
							</div>
						</CardContent>
						<CardFooter className="flex items-center justify-between">
							<div className="text-sm text-muted-foreground">Showing 4 of 24 transactions</div>
							<div className="flex items-center gap-2">
								<Button variant="outline" size="sm" disabled>
									Previous
								</Button>
								<Button variant="outline" size="sm">
									Next
								</Button>
							</div>
						</CardFooter>
					</Card>
				</TabsContent>

				<TabsContent value="analytics" className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle>Payment Analytics</CardTitle>
								<CardDescription>Detailed payment system analytics</CardDescription>
							</CardHeader>
							<CardContent>
								<Button onClick={fetchAnalytics} disabled={analyticsLoading} className="w-full mb-4">
									<RefreshCw className={`h-4 w-4 mr-2 ${analyticsLoading ? "animate-spin" : ""}`} />
									Refresh Analytics
								</Button>

								{analytics && (
									<div className="space-y-4">
										<div>
											<h4 className="font-medium mb-2">Payment Overview</h4>
											<div className="grid grid-cols-2 gap-4 text-sm">
												<div>
													<span className="text-muted-foreground">Total Attempts:</span>
													<div className="font-medium">
														{analytics.overview.total_payment_attempts}
													</div>
												</div>
												<div>
													<span className="text-muted-foreground">Success Rate:</span>
													<div className="font-medium">{analytics.overview.success_rate}</div>
												</div>
												<div>
													<span className="text-muted-foreground">Total Revenue:</span>
													<div className="font-medium">
														${analytics.overview.total_revenue?.toFixed(2)}
													</div>
												</div>
												<div>
													<span className="text-muted-foreground">Failed Amount:</span>
													<div className="font-medium">
														${analytics.overview.total_failed_amount?.toFixed(2)}
													</div>
												</div>
											</div>
										</div>

										{analytics.health.recommendations.length > 0 && (
											<div>
												<h4 className="font-medium mb-2">Recommendations</h4>
												<ul className="text-sm space-y-1">
													{analytics.health.recommendations.map((rec, index) => (
														<li key={index} className="flex items-start gap-2">
															<span className="text-blue-500 mt-1">•</span>
															<span>{rec}</span>
														</li>
													))}
												</ul>
											</div>
										)}
									</div>
								)}
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>System Health</CardTitle>
								<CardDescription>Payment system health monitoring</CardDescription>
							</CardHeader>
							<CardContent>
								{analytics?.health ? (
									<div className="space-y-4">
										<div className="flex items-center gap-2">
											{analytics.health.status === "healthy" ? (
												<CheckCircle className="h-5 w-5 text-green-600" />
											) : (
												<AlertTriangle className="h-5 w-5 text-yellow-600" />
											)}
											<span
												className={`font-semibold ${getHealthStatusColor(
													analytics.health.status
												)}`}
											>
												{analytics.health.status.toUpperCase()}
											</span>
										</div>

										{analytics.health.issues.length > 0 ? (
											<div>
												<h4 className="font-medium mb-2 text-red-600">Issues Detected</h4>
												<ul className="text-sm space-y-1">
													{analytics.health.issues.map((issue, index) => (
														<li key={index} className="flex items-start gap-2">
															<AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
															<span>{issue}</span>
														</li>
													))}
												</ul>
											</div>
										) : (
											<div className="flex items-center gap-2 text-green-600">
												<CheckCircle className="h-4 w-4" />
												<span className="text-sm">No issues detected</span>
											</div>
										)}
									</div>
								) : (
									<div className="text-center py-8">
										<RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
										<p className="text-sm text-muted-foreground">Loading health data...</p>
									</div>
								)}
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="customers" className="space-y-4">
					<Card>
						<CardHeader>
							<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
								<div>
									<CardTitle>Customers</CardTitle>
									<CardDescription>View and manage customer billing information</CardDescription>
								</div>
								<div className="flex gap-2">
									<div className="relative">
										<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
										<Input
											type="search"
											placeholder="Search customers..."
											className="w-full rounded-md pl-8 md:w-[200px] lg:w-[300px]"
										/>
									</div>
									<Button variant="outline" size="icon">
										<Filter className="h-4 w-4" />
										<span className="sr-only">Filter</span>
									</Button>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="rounded-md border">
								<div className="grid grid-cols-5 gap-4 p-4 font-medium">
									<div>Customer</div>
									<div>Email</div>
									<div>Plan</div>
									<div>Status</div>
									<div className="text-right">Actions</div>
								</div>
								<Separator />
								<div className="grid grid-cols-5 gap-4 p-4 items-center">
									<div className="font-medium">John Doe</div>
									<div className="text-sm"><EMAIL></div>
									<div className="text-sm">Pro Plan</div>
									<div>
										<Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
											Active
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
								<Separator />
								<div className="grid grid-cols-5 gap-4 p-4 items-center">
									<div className="font-medium">Jane Smith</div>
									<div className="text-sm"><EMAIL></div>
									<div className="text-sm">Pro Plan</div>
									<div>
										<Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
											Active
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
								<Separator />
								<div className="grid grid-cols-5 gap-4 p-4 items-center">
									<div className="font-medium">Robert Johnson</div>
									<div className="text-sm"><EMAIL></div>
									<div className="text-sm">Basic Plan</div>
									<div>
										<Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
											Active
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
								<Separator />
								<div className="grid grid-cols-5 gap-4 p-4 items-center">
									<div className="font-medium">Sarah Williams</div>
									<div className="text-sm"><EMAIL></div>
									<div className="text-sm">Enterprise Plan</div>
									<div>
										<Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
											Canceled
										</Badge>
									</div>
									<div className="flex justify-end">
										<Button variant="ghost" size="sm">
											View
										</Button>
									</div>
								</div>
							</div>
						</CardContent>
						<CardFooter className="flex items-center justify-between">
							<div className="text-sm text-muted-foreground">Showing 4 of 38 customers</div>
							<div className="flex items-center gap-2">
								<Button variant="outline" size="sm" disabled>
									Previous
								</Button>
								<Button variant="outline" size="sm">
									Next
								</Button>
							</div>
						</CardFooter>
					</Card>
				</TabsContent>

				<TabsContent value="settings" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Stripe Integration</CardTitle>
							<CardDescription>Configure your Stripe payment gateway settings</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="stripe-public-key">Stripe Public Key</Label>
								<Input id="stripe-public-key" defaultValue="pk_test_51..." type="password" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="stripe-secret-key">Stripe Secret Key</Label>
								<Input id="stripe-secret-key" defaultValue="sk_test_51..." type="password" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="stripe-webhook-secret">Webhook Secret</Label>
								<Input id="stripe-webhook-secret" defaultValue="whsec_..." type="password" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="stripe-webhook-url">Webhook URL</Label>
								<div className="flex gap-2">
									<Input
										id="stripe-webhook-url"
										defaultValue="https://admanager.com/api/webhooks/stripe"
										readOnly
									/>
									<Button variant="outline" size="sm">
										Copy
									</Button>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<Button onClick={handleSaveSettings} disabled={loading}>
								{loading ? "Saving..." : "Save Settings"}
							</Button>
						</CardFooter>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Payment Settings</CardTitle>
							<CardDescription>Configure general payment settings</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="test-mode">Test Mode</Label>
									<p className="text-sm text-muted-foreground">
										Enable test mode to use Stripe test credentials
									</p>
								</div>
								<Switch id="test-mode" defaultChecked />
							</div>
							<Separator />
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="auto-invoice">Automatic Invoicing</Label>
									<p className="text-sm text-muted-foreground">
										Automatically generate invoices for successful payments
									</p>
								</div>
								<Switch id="auto-invoice" defaultChecked />
							</div>
							<Separator />
							<div className="space-y-2">
								<Label htmlFor="currency">Default Currency</Label>
								<Select defaultValue="usd">
									<SelectTrigger id="currency">
										<SelectValue placeholder="Select currency" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="usd">USD ($)</SelectItem>
										<SelectItem value="eur">EUR (€)</SelectItem>
										<SelectItem value="gbp">GBP (£)</SelectItem>
										<SelectItem value="jpy">JPY (¥)</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div className="space-y-2">
								<Label htmlFor="payment-methods">Accepted Payment Methods</Label>
								<div className="flex flex-wrap gap-2">
									<div className="flex items-center space-x-2">
										<input
											type="checkbox"
											id="card"
											className="rounded border-gray-300"
											defaultChecked
										/>
										<Label htmlFor="card" className="text-sm">
											Credit/Debit Cards
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<input
											type="checkbox"
											id="paypal"
											className="rounded border-gray-300"
											defaultChecked
										/>
										<Label htmlFor="paypal" className="text-sm">
											PayPal
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<input
											type="checkbox"
											id="bank"
											className="rounded border-gray-300"
											defaultChecked
										/>
										<Label htmlFor="bank" className="text-sm">
											Bank Transfer
										</Label>
									</div>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<Button onClick={handleSaveSettings} disabled={loading}>
								{loading ? "Saving..." : "Save Settings"}
							</Button>
						</CardFooter>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
