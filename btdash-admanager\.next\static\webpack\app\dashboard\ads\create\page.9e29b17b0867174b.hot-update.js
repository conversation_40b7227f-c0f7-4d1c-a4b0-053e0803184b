"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/ads/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/ads/create/client-wrapper.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/ads/create/client-wrapper.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateAdClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n// app/dashboard/ads/create/client-wrapper.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateAdClientWrapper(param) {\n    let { placementId, campaignId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]);\n    const [placement, setPlacement] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)({\n        title: \"\",\n        image_url: \"\",\n        target_url: \"\",\n        max_impressions: \"\",\n        max_clicks: \"\",\n        weight: \"1\",\n        campaign_id: campaignId || \"\",\n        // New campaign fields\n        campaign_name: \"\",\n        total_budget: \"\",\n        budget_cpc: \"\",\n        budget_cpm: \"\",\n        start_date: \"\",\n        end_date: \"\"\n    });\n    useEffect({\n        \"CreateAdClientWrapper.useEffect\": ()=>{\n            const fetchData = {\n                \"CreateAdClientWrapper.useEffect.fetchData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch placement details if provided\n                        if (placementId) {\n                            const placementRes = await fetch(\"/api/placements/\".concat(placementId));\n                            if (placementRes.ok) {\n                                const placementData = await placementRes.json();\n                                if (placementData.success) {\n                                    setPlacement(placementData.data);\n                                }\n                            }\n                        }\n                        // Fetch user campaigns\n                        const campaignsRes = await fetch(\"/api/user/campaigns\");\n                        if (campaignsRes.ok) {\n                            const campaignsData = await campaignsRes.json();\n                            if (campaignsData.success) {\n                                const activeCampaigns = campaignsData.data.filter({\n                                    \"CreateAdClientWrapper.useEffect.fetchData.activeCampaigns\": (c)=>c.status === \"pending\"\n                                }[\"CreateAdClientWrapper.useEffect.fetchData.activeCampaigns\"]);\n                                setCampaigns(activeCampaigns);\n                                // Auto-select option based on available campaigns\n                                if (campaignId) {\n                                    setSelectedOption(\"existing\");\n                                    setFormData({\n                                        \"CreateAdClientWrapper.useEffect.fetchData\": (prev)=>({\n                                                ...prev,\n                                                campaign_id: campaignId\n                                            })\n                                    }[\"CreateAdClientWrapper.useEffect.fetchData\"]);\n                                } else if (activeCampaigns.length === 0) {\n                                    setSelectedOption(\"new\");\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load data. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CreateAdClientWrapper.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"CreateAdClientWrapper.useEffect\"], [\n        placementId,\n        campaignId,\n        toast\n    ]);\n    // Filter campaigns to only show pending ones\n    const activeCampaigns = campaigns.filter((c)=>c.status === \"pending\");\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedOption) {\n            toast({\n                title: \"Selection Required\",\n                description: \"Please choose to add to existing campaign or create new campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            if (selectedOption === \"new\") {\n                // Create new campaign with ad\n                const campaignData = {\n                    name: formData.campaign_name,\n                    total_budget: formData.total_budget ? parseFloat(formData.total_budget) : null,\n                    budget_cpc: formData.budget_cpc ? parseFloat(formData.budget_cpc) : null,\n                    budget_cpm: formData.budget_cpm ? parseFloat(formData.budget_cpm) : null,\n                    start_date: formData.start_date,\n                    end_date: formData.end_date,\n                    // Ad data\n                    ad_title: formData.title,\n                    image_url: formData.image_url,\n                    target_url: formData.target_url,\n                    max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,\n                    max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,\n                    weight: parseInt(formData.weight),\n                    slot_id: placementId ? parseInt(placementId) : null\n                };\n                const response = await fetch(\"/api/user/campaigns\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(campaignData)\n                });\n                if (!response.ok) {\n                    let errorMessage = \"Failed to create campaign\";\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (jsonError) {\n                        // If response is not JSON, use status text\n                        errorMessage = \"\".concat(response.status, \": \").concat(response.statusText);\n                    }\n                    throw new Error(errorMessage);\n                }\n                const result = await response.json();\n                if (result.success) {\n                    toast({\n                        title: \"Campaign Created\",\n                        description: \"Your campaign and ad have been created successfully.\"\n                    });\n                    router.push(\"/dashboard/campaigns/\".concat(result.data.campaign.id));\n                }\n            } else {\n                // Add ad to existing campaign\n                const adData = {\n                    campaign_id: parseInt(formData.campaign_id),\n                    slot_id: placementId ? parseInt(placementId) : null,\n                    title: formData.title,\n                    image_url: formData.image_url,\n                    target_url: formData.target_url,\n                    max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,\n                    max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,\n                    weight: parseInt(formData.weight)\n                };\n                const response = await fetch(\"/api/user/ads\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(adData)\n                });\n                if (!response.ok) {\n                    let errorMessage = \"Failed to create ad\";\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (jsonError) {\n                        // If response is not JSON, use status text\n                        errorMessage = \"\".concat(response.status, \": \").concat(response.statusText);\n                    }\n                    throw new Error(errorMessage);\n                }\n                const result = await response.json();\n                if (result.success) {\n                    toast({\n                        title: \"Ad Created\",\n                        description: \"Your ad has been created successfully.\"\n                    });\n                    router.push(\"/dashboard/campaigns/\".concat(formData.campaign_id));\n                }\n            }\n        } catch (error) {\n            console.error(\"Error creating ad:\", error);\n            toast({\n                title: \"Creation Failed\",\n                description: error instanceof Error ? error.message : \"Failed to create ad\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                lineNumber: 234,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n            lineNumber: 233,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                            href: \"/dashboard/placements\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 8\n                                    }, this),\n                                    \"Back to Placements\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Create New Ad\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Add an ad to an existing campaign or create a new campaign\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                !campaignId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Campaign Selection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Choose whether to add this ad to an existing campaign or create a new one\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        activeCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    id: \"existing\",\n                                                                    name: \"campaign-option\",\n                                                                    checked: selectedOption === \"existing\",\n                                                                    onChange: ()=>setSelectedOption(\"existing\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"existing\",\n                                                                    children: \"Add to existing campaign\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    id: \"new\",\n                                                                    name: \"campaign-option\",\n                                                                    checked: selectedOption === \"new\",\n                                                                    onChange: ()=>setSelectedOption(\"new\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"new\",\n                                                                    children: \"Create new campaign\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 10\n                                                }, this),\n                                                selectedOption === \"existing\" && activeCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"campaign\",\n                                                            children: \"Select Campaign\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: formData.campaign_id,\n                                                            onValueChange: (value)=>handleInputChange(\"campaign_id\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Choose a campaign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 13\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: activeCampaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: campaign.id.toString(),\n                                                                            children: [\n                                                                                campaign.name,\n                                                                                \" (\",\n                                                                                campaign.status,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, campaign.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 11\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Ad Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: placement ? \"Creating ad for \".concat(placement.name) : \"Enter your ad information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    selectedOption === \"new\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-medium\",\n                                                                        children: \"Campaign Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"campaign_name\",\n                                                                                        children: \"Campaign Name *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 334,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"campaign_name\",\n                                                                                        value: formData.campaign_name,\n                                                                                        onChange: (e)=>handleInputChange(\"campaign_name\", e.target.value),\n                                                                                        placeholder: \"Enter campaign name\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 335,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"total_budget\",\n                                                                                        children: \"Total Budget ($)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 346,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"total_budget\",\n                                                                                        type: \"number\",\n                                                                                        step: \"0.01\",\n                                                                                        value: formData.total_budget,\n                                                                                        onChange: (e)=>handleInputChange(\"total_budget\", e.target.value),\n                                                                                        placeholder: \"0.00\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"start_date\",\n                                                                                        children: \"Start Date *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"start_date\",\n                                                                                        type: \"date\",\n                                                                                        value: formData.start_date,\n                                                                                        onChange: (e)=>handleInputChange(\"start_date\", e.target.value),\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 362,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                        htmlFor: \"end_date\",\n                                                                                        children: \"End Date *\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 373,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"end_date\",\n                                                                                        type: \"date\",\n                                                                                        value: formData.end_date,\n                                                                                        onChange: (e)=>handleInputChange(\"end_date\", e.target.value),\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 374,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Ad Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"title\",\n                                                                        children: \"Ad Title *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"title\",\n                                                                        value: formData.title,\n                                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                                        placeholder: \"Enter ad title\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"image_url\",\n                                                                        children: \"Image URL *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"image_url\",\n                                                                        type: \"url\",\n                                                                        value: formData.image_url,\n                                                                        onChange: (e)=>handleInputChange(\"image_url\", e.target.value),\n                                                                        placeholder: \"https://example.com/image.jpg\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground mt-1\",\n                                                                        children: [\n                                                                            \"Required dimensions: \",\n                                                                            placement.width,\n                                                                            \"x\",\n                                                                            placement.height,\n                                                                            \"px\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    formData.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: \"Preview:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 421,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 relative w-full max-w-md border rounded-lg overflow-hidden bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: formData.image_url,\n                                                                                        alt: \"Ad preview\",\n                                                                                        className: \"w-full h-auto object-cover\",\n                                                                                        onError: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.style.display = \"none\";\n                                                                                            const errorDiv = target.nextElementSibling;\n                                                                                            if (errorDiv) errorDiv.style.display = \"block\";\n                                                                                        },\n                                                                                        onLoad: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.style.display = \"block\";\n                                                                                            const errorDiv = target.nextElementSibling;\n                                                                                            if (errorDiv) errorDiv.style.display = \"none\";\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 423,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"hidden p-4 text-center text-sm text-red-600\",\n                                                                                        style: {\n                                                                                            display: \"none\"\n                                                                                        },\n                                                                                        children: \"Failed to load image. Please check the URL.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                        lineNumber: 442,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 422,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: [\n                                                                                    \"Target: \",\n                                                                                    placement.width,\n                                                                                    \"x\",\n                                                                                    placement.height,\n                                                                                    \"px\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"target_url\",\n                                                                        children: \"Target URL *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"target_url\",\n                                                                        type: \"url\",\n                                                                        value: formData.target_url,\n                                                                        onChange: (e)=>handleInputChange(\"target_url\", e.target.value),\n                                                                        placeholder: \"https://example.com\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"max_impressions\",\n                                                                                children: \"Max Impressions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"max_impressions\",\n                                                                                type: \"number\",\n                                                                                value: formData.max_impressions,\n                                                                                onChange: (e)=>handleInputChange(\"max_impressions\", e.target.value),\n                                                                                placeholder: \"Unlimited\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 471,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"max_clicks\",\n                                                                                children: \"Max Clicks\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"max_clicks\",\n                                                                                type: \"number\",\n                                                                                value: formData.max_clicks,\n                                                                                onChange: (e)=>handleInputChange(\"max_clicks\", e.target.value),\n                                                                                placeholder: \"Unlimited\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 12\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"weight\",\n                                                                                children: \"Weight\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 13\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"weight\",\n                                                                                type: \"number\",\n                                                                                min: \"1\",\n                                                                                max: \"1000\",\n                                                                                value: formData.weight,\n                                                                                onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 13\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        disabled: submitting,\n                                                        children: submitting ? \"Creating...\" : selectedOption === \"new\" ? \"Create Campaign & Ad\" : \"Create Ad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                placement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Selected Placement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Name:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Page:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.page\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Dimensions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 12\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: [\n                                                                    placement.width,\n                                                                    \"x\",\n                                                                    placement.height,\n                                                                    \"px\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 12\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    placement.allowed_ad_types && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Formats:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 13\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: placement.allowed_ad_types.join(\", \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 13\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Tips\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Use high-quality images that match the placement dimensions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Write compelling ad titles that grab attention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Set reasonable impression and click limits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Higher weight gives your ad more visibility\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 9\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n            lineNumber: 244,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\ads\\\\create\\\\client-wrapper.tsx\",\n        lineNumber: 243,\n        columnNumber: 3\n    }, this);\n}\n_s(CreateAdClientWrapper, \"XcY8xky1Cnm/OHsq8iFI+4kqhCk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CreateAdClientWrapper;\nvar _c;\n$RefreshReg$(_c, \"CreateAdClientWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/ads/create/client-wrapper.tsx\n"));

/***/ })

});