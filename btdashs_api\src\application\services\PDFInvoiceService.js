const PDFDocument = require("pdfkit");
const fs = require("fs");
const path = require("path");
const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class PDFInvoiceService {
	constructor() {
		// Ensure uploads directory exists
		this.uploadsDir = path.join(process.cwd(), "uploads", "invoices");
		if (!fs.existsSync(this.uploadsDir)) {
			fs.mkdirSync(this.uploadsDir, { recursive: true });
		}
	}

	/**
	 * Generate PDF invoice
	 * @param {number} invoiceId - Invoice ID
	 * @returns {Promise<string>} PDF file path
	 */
	async generateInvoicePDF(invoiceId) {
		try {
			// Get invoice with line items and related data
			const invoiceData = await this.getInvoiceData(invoiceId);

			if (!invoiceData) {
				throw new Error("Invoice not found");
			}

			// Generate PDF
			const pdfPath = await this.createPDF(invoiceData);

			// Update invoice with PDF URL
			await this.updateInvoicePDFUrl(invoiceId, pdfPath);

			logger.info("PDF invoice generated", {
				invoiceId,
				invoiceNumber: invoiceData.invoice_number,
				pdfPath,
			});

			return pdfPath;
		} catch (error) {
			logger.error("Error generating PDF invoice", { error, invoiceId });
			throw error;
		}
	}

	/**
	 * Get complete invoice data for PDF generation
	 * @param {number} invoiceId - Invoice ID
	 * @returns {Promise<Object>} Complete invoice data
	 */
	async getInvoiceData(invoiceId) {
		try {
			// Get invoice with manager and advertiser info
			const invoice = await db("dtm_ads.invoices")
				.leftJoin("dtm_base.users as manager", "invoices.manager_id", "manager.id")
				.leftJoin("dtm_base.companies as advertiser", "invoices.advertiser_id", "advertiser.id")
				.leftJoin("dtm_ads.ad_campaigns", "invoices.campaign_id", "ad_campaigns.id")
				.where("invoices.id", invoiceId)
				.select(
					"invoices.*",
					"manager.email as manager_email",
					"manager.first_name as manager_first_name",
					"manager.last_name as manager_last_name",
					"advertiser.name as advertiser_name",
					"advertiser.website_url as advertiser_website",
					"ad_campaigns.name as campaign_name"
				)
				.first();

			if (!invoice) {
				return null;
			}

			// Get line items
			const lineItems = await db("dtm_ads.invoice_line_items").where({ invoice_id: invoiceId });

			return {
				...invoice,
				line_items: lineItems,
			};
		} catch (error) {
			logger.error("Error getting invoice data", { error, invoiceId });
			throw error;
		}
	}

	/**
	 * Create PDF document
	 * @param {Object} invoiceData - Complete invoice data
	 * @returns {Promise<string>} PDF file path
	 */
	async createPDF(invoiceData) {
		return new Promise((resolve, reject) => {
			try {
				const fileName = `invoice-${invoiceData.invoice_number}.pdf`;
				const filePath = path.join(this.uploadsDir, fileName);

				// Create PDF document
				const doc = new PDFDocument({ margin: 50 });
				const stream = fs.createWriteStream(filePath);
				doc.pipe(stream);

				// Add content to PDF
				this.addHeader(doc, invoiceData);
				this.addInvoiceInfo(doc, invoiceData);
				this.addBillingInfo(doc, invoiceData);
				this.addLineItems(doc, invoiceData);
				this.addTotals(doc, invoiceData);
				this.addFooter(doc, invoiceData);

				// Finalize PDF
				doc.end();

				stream.on("finish", () => {
					resolve(filePath);
				});

				stream.on("error", (error) => {
					reject(error);
				});
			} catch (error) {
				reject(error);
			}
		});
	}

	/**
	 * Add header to PDF
	 */
	addHeader(doc, invoiceData) {
		// Company logo and info
		doc.fontSize(20).text("BTDash Ad Manager", 50, 50);
		doc.fontSize(10).text("Digital Advertising Platform", 50, 75);
		doc.text("<EMAIL>", 50, 90);

		// Invoice title
		doc.fontSize(24).text("INVOICE", 400, 50);
		doc.fontSize(12).text(`#${invoiceData.invoice_number}`, 400, 80);

		// Add line
		doc.moveTo(50, 120).lineTo(550, 120).stroke();
	}

	/**
	 * Add invoice information
	 */
	addInvoiceInfo(doc, invoiceData) {
		const startY = 140;

		// Invoice details
		doc.fontSize(12).text("Invoice Date:", 50, startY);
		doc.text(new Date(invoiceData.created_at).toLocaleDateString(), 150, startY);

		if (invoiceData.due_date) {
			doc.text("Due Date:", 50, startY + 20);
			doc.text(new Date(invoiceData.due_date).toLocaleDateString(), 150, startY + 20);
		}

		doc.text("Status:", 50, startY + 40);
		doc.text(invoiceData.status.toUpperCase(), 150, startY + 40);

		if (invoiceData.campaign_name) {
			doc.text("Campaign:", 50, startY + 60);
			doc.text(invoiceData.campaign_name, 150, startY + 60);
		}
	}

	/**
	 * Add billing information
	 */
	addBillingInfo(doc, invoiceData) {
		const startY = 140;

		// Bill to section
		doc.fontSize(14).text("Bill To:", 350, startY);
		doc.fontSize(12);

		// Show advertiser company name if available, otherwise manager name
		const customerName =
			invoiceData.advertiser_name ||
			(invoiceData.manager_first_name && invoiceData.manager_last_name
				? `${invoiceData.manager_first_name} ${invoiceData.manager_last_name}`
				: "Customer");

		doc.text(customerName, 350, startY + 25);

		// Show manager email as contact
		if (invoiceData.manager_email) {
			doc.text(invoiceData.manager_email, 350, startY + 45);
		}

		// Show advertiser website if available
		if (invoiceData.advertiser_website) {
			doc.fontSize(10).text(`Website: ${invoiceData.advertiser_website}`, 350, startY + 65);
		}

		if (invoiceData.customer_id) {
			doc.fontSize(10).text(`Customer ID: ${invoiceData.customer_id}`, 350, startY + 85);
		}
	}

	/**
	 * Add line items table
	 */
	addLineItems(doc, invoiceData) {
		const startY = 250;
		const tableTop = startY;

		// Table headers
		doc.fontSize(12);
		doc.text("Description", 50, tableTop);
		doc.text("Qty", 300, tableTop);
		doc.text("Unit Price", 350, tableTop);
		doc.text("Amount", 450, tableTop);

		// Header line
		doc.moveTo(50, tableTop + 20)
			.lineTo(550, tableTop + 20)
			.stroke();

		// Line items
		let currentY = tableTop + 35;
		invoiceData.line_items.forEach((item, index) => {
			doc.fontSize(10);
			doc.text(item.description, 50, currentY, { width: 240 });
			doc.text(item.quantity.toString(), 300, currentY);
			doc.text(`$${parseFloat(item.unit_price).toFixed(2)}`, 350, currentY);
			doc.text(`$${parseFloat(item.amount).toFixed(2)}`, 450, currentY);

			currentY += 25;

			// Add line between items
			if (index < invoiceData.line_items.length - 1) {
				doc.moveTo(50, currentY - 5)
					.lineTo(550, currentY - 5)
					.stroke("#EEEEEE");
			}
		});

		return currentY;
	}

	/**
	 * Add totals section
	 */
	addTotals(doc, invoiceData) {
		const startY = 400;

		// Totals
		doc.fontSize(12);
		doc.text("Subtotal:", 350, startY);
		doc.text(`$${parseFloat(invoiceData.subtotal).toFixed(2)}`, 450, startY);

		if (invoiceData.tax_amount > 0) {
			doc.text("Tax:", 350, startY + 20);
			doc.text(`$${parseFloat(invoiceData.tax_amount).toFixed(2)}`, 450, startY + 20);
		}

		// Total line
		doc.moveTo(350, startY + 35)
			.lineTo(550, startY + 35)
			.stroke();

		doc.fontSize(14).text("Total:", 350, startY + 45);
		doc.text(`$${parseFloat(invoiceData.total_amount).toFixed(2)}`, 450, startY + 45);

		// Payment status
		if (invoiceData.status === "paid") {
			doc.fontSize(16)
				.fillColor("green")
				.text("PAID", 350, startY + 75);
			if (invoiceData.paid_at) {
				doc.fontSize(10)
					.fillColor("black")
					.text(`Paid on: ${new Date(invoiceData.paid_at).toLocaleDateString()}`, 350, startY + 100);
			}
		}
	}

	/**
	 * Add footer
	 */
	addFooter(doc, invoiceData) {
		const footerY = 700;

		doc.fontSize(10).fillColor("gray");
		doc.text("Thank you for your business!", 50, footerY);
		doc.text("For questions about this invoice, contact <EMAIL>", 50, footerY + 15);

		if (invoiceData.notes) {
			doc.text("Notes:", 50, footerY + 40);
			doc.text(invoiceData.notes, 50, footerY + 55, { width: 500 });
		}
	}

	/**
	 * Update invoice with PDF URL
	 * @param {number} invoiceId - Invoice ID
	 * @param {string} pdfPath - PDF file path
	 */
	async updateInvoicePDFUrl(invoiceId, pdfPath) {
		try {
			// Convert absolute path to relative URL
			const relativePath = path.relative(process.cwd(), pdfPath);
			const pdfUrl = `/${relativePath.replace(/\\/g, "/")}`;

			await db("dtm_ads.invoices").where({ id: invoiceId }).update({
				pdf_url: pdfUrl,
				pdf_generated_at: new Date(),
				updated_at: new Date(),
			});

			logger.info("Invoice PDF URL updated", { invoiceId, pdfUrl });
		} catch (error) {
			logger.error("Error updating invoice PDF URL", { error, invoiceId, pdfPath });
			throw error;
		}
	}

	/**
	 * Get PDF file path for invoice
	 * @param {number} invoiceId - Invoice ID
	 * @returns {Promise<string|null>} PDF file path or null if not generated
	 */
	async getInvoicePDFPath(invoiceId) {
		try {
			const invoice = await db("dtm_ads.invoices").where({ id: invoiceId }).select("pdf_url").first();

			if (!invoice || !invoice.pdf_url) {
				return null;
			}

			const fullPath = path.join(process.cwd(), invoice.pdf_url.substring(1));

			// Check if file exists
			if (fs.existsSync(fullPath)) {
				return fullPath;
			}

			return null;
		} catch (error) {
			logger.error("Error getting invoice PDF path", { error, invoiceId });
			throw error;
		}
	}
}

module.exports = PDFInvoiceService;
