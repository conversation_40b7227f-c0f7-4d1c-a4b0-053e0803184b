// src/presentation/routes/index.js
const express = require("express");
const path = require("path");
const router = express.Router();
const checkJwt = require("../../middleware/auth");
const checkInternalKey = require("../../middleware/internal");

// Import controllers
const categoriesController = require("../controllers/categoriesController");
const subnetsController = require("../controllers/subnetsController");
const subnetMetricsController = require("../controllers/subnetMetricsController");
const githubTeamMembersController = require("../controllers/githubTeamMembersController");
const githubContributionsController = require("../controllers/githubContributionsController");
const networkPricesController = require("../controllers/networkPricesController");
const networkStatsController = require("../controllers/networkStatsController");
const companiesController = require("../controllers/companiesController");
const jobsController = require("../controllers/jobsController");
const newsController = require("../controllers/newsController");
const validatorsController = require("../controllers/validatorsController");
const validatorPerformanceController = require("../controllers/validatorPerformanceController");
const updateController = require("../controllers/updateController");
const productsController = require("../controllers/productsController");
const skillsController = require("../controllers/skillsController");
const usersController = require("../controllers/usersController");
const eventsController = require("../controllers/eventsController");

// Import endpoints route
const endpointsRoutes = require("./endpoints");

// Categories routes
router.get("/categories", checkInternalKey, categoriesController.getAllCategories);
router.get("/categories/:id", checkInternalKey, categoriesController.getCategoryById);
router.post("/categories", checkInternalKey, categoriesController.createCategory);
router.put("/categories/:id", checkInternalKey, categoriesController.updateCategory);
router.delete("/categories/:id", checkInternalKey, categoriesController.deleteCategory);

// Subnets routes
router.get("/subnets", checkInternalKey, subnetsController.getAllSubnets);
router.get("/subnets/:id", checkInternalKey, subnetsController.getSubnetById);
router.post("/subnets", checkInternalKey, subnetsController.createSubnet);
router.put("/subnets/:id", checkInternalKey, subnetsController.updateSubnet);
router.delete("/subnets/:id", checkInternalKey, subnetsController.deleteSubnet);

// Subnet Metrics routes
router.get("/subnet-metrics", checkInternalKey, subnetMetricsController.getAllSubnetMetrics);
router.get("/subnet-metrics/:id", checkInternalKey, subnetMetricsController.getSubnetMetricById);
router.post("/subnet-metrics", checkInternalKey, subnetMetricsController.createSubnetMetric);
router.put("/subnet-metrics/:id", checkInternalKey, subnetMetricsController.updateSubnetMetric);
router.delete("/subnet-metrics/:id", checkInternalKey, subnetMetricsController.deleteSubnetMetric);

// GitHub Team Members routes
router.get("/subnets/:id/github-team-members", checkInternalKey, githubTeamMembersController.getSubnetTeamMembers);
router.post("/subnets/:id/github-team-members", checkInternalKey, githubTeamMembersController.updateSubnetTeamMembers);
router.delete(
	"/subnets/:id/github-team-members/:memberLogin",
	checkInternalKey,
	githubTeamMembersController.removeSubnetTeamMember
);

// GitHub Contributions routes
router.get(
	"/subnets/:id/github-contributions",
	checkInternalKey,
	githubContributionsController.getSubnetGithubContributions
);

// Network Prices routes
router.get("/network-prices", checkInternalKey, networkPricesController.getAllNetworkPrices);
router.get("/network-prices/:id", checkInternalKey, networkPricesController.getNetworkPriceById);
router.post("/network-prices", checkInternalKey, networkPricesController.createNetworkPrice);
router.put("/network-prices/:id", checkInternalKey, networkPricesController.updateNetworkPrice);
router.delete("/network-prices/:id", checkInternalKey, networkPricesController.deleteNetworkPrice);

// Network Stats routes
router.get("/network-stats", checkInternalKey, networkStatsController.getAllNetworkStats);
router.get("/network-stats/latest", checkInternalKey, networkStatsController.getLatestNetworkStats);
router.get("/network-stats/:id", checkInternalKey, networkStatsController.getNetworkStatById);
router.post("/network-stats", checkInternalKey, networkStatsController.createNetworkStat);
router.put("/network-stats/:id", checkInternalKey, networkStatsController.updateNetworkStat);
router.delete("/network-stats/:id", checkInternalKey, networkStatsController.deleteNetworkStat);

// Companies routes
router.get("/companies", checkInternalKey, companiesController.getAllCompanies);
router.get("/companies/:id", checkInternalKey, companiesController.getCompanyById);
router.post("/companies", checkInternalKey, companiesController.createCompany);
router.put("/companies/:id", checkInternalKey, companiesController.updateCompany);
router.delete("/companies/:id", checkInternalKey, companiesController.deleteCompany);

// Jobs routes
router.get("/jobs", checkInternalKey, jobsController.getAllJobs);
router.get("/jobs/:id", checkInternalKey, jobsController.getJobById);
router.post("/jobs", checkInternalKey, jobsController.createJob);
router.put("/jobs/:id", checkInternalKey, jobsController.updateJob);
router.delete("/jobs/:id", checkInternalKey, jobsController.deleteJob);

// Products routes
router.get("/products", checkInternalKey, productsController.getAllProducts);
router.get("/products/:id", checkInternalKey, productsController.getProductById);
router.get("/products/subnet/:id", checkInternalKey, productsController.getProductsBySubnetId);
router.get("/products/featured/:flag", checkInternalKey, productsController.getFeaturedProducts);

// News routes
router.get("/news", checkInternalKey, newsController.getAllNews);
router.get("/news/:id", checkInternalKey, newsController.getNewsById);
router.post("/news", checkInternalKey, newsController.createNews);
router.put("/news/:id", checkInternalKey, newsController.updateNews);
router.delete("/news/:id", checkInternalKey, newsController.deleteNews);

// Validators routes
router.get("/validators", checkInternalKey, validatorsController.getAllValidators);
router.get("/validators/:id", checkInternalKey, validatorsController.getValidatorById);
router.post("/validators", checkInternalKey, validatorsController.createValidator);
router.put("/validators/:id", checkInternalKey, validatorsController.updateValidator);
router.delete("/validators/:id", checkInternalKey, validatorsController.deleteValidator);

// Validator Performance routes
router.get("/validator-performance", checkInternalKey, validatorPerformanceController.getAllValidatorPerformance);
router.get("/validator-performance/:id", checkInternalKey, validatorPerformanceController.getValidatorPerformanceById);
router.post("/validator-performance", checkInternalKey, validatorPerformanceController.createValidatorPerformance);
router.put("/validator-performance/:id", checkInternalKey, validatorPerformanceController.updateValidatorPerformance);
router.delete(
	"/validator-performance/:id",
	checkInternalKey,
	validatorPerformanceController.deleteValidatorPerformance
);

// Events routes
router.get("/events", checkInternalKey, eventsController.getAllEvents);
router.get("/events/:id", checkInternalKey, eventsController.getEventById);
router.post("/events", checkJwt, eventsController.createEvent);
router.put("/events/:id", checkJwt, eventsController.updateEvent);
router.delete("/events/:id", checkJwt, eventsController.deleteEvent);

// Skills routes
router.get("/skills", checkInternalKey, skillsController.getAllSkills);
router.get("/skills/:id", checkInternalKey, skillsController.getSkillById);
router.post("/skills", checkInternalKey, skillsController.createSkill);
router.put("/skills/:id", checkInternalKey, skillsController.updateSkill);
router.delete("/skills/:id", checkInternalKey, skillsController.deleteSkill);

// Users routes
router.post("/user/sync", usersController.syncUser);
router.get("/user/me", checkJwt, usersController.getUserData);
router.put("/user/me", checkJwt, usersController.updateUserData);

// User Companies routes
router.post("/user/companies/with-owner", checkJwt, usersController.createCompanyWithOwner);
router.get("/user/companies/me", checkJwt, usersController.getUserCompany);
router.get("/user/company", checkJwt, usersController.getUserCompany); // Alias for backward compatibility
router.put("/user/companies/:id", checkJwt, usersController.updateCompanyWithAuth);
router.delete("/user/companies/:id", checkJwt, usersController.deleteCompanyWithAuth);

// User Skills routes
router.get("/user/skills", checkJwt, usersController.getUserSkills);
router.get("/user/skills/:id", checkJwt, usersController.getUserSkillById);
router.post("/user/skills", checkJwt, usersController.createUserSkill);
router.put("/user/skills/:id", checkJwt, usersController.updateUserSkill);
router.delete("/user/skills/:id", checkJwt, usersController.deleteUserSkill);

// User Preferences routes
router.get("/user/preferences", checkJwt, usersController.getUserPreferences);
router.get("/user/preferences/:id", checkJwt, usersController.getUserPreferencesById);
router.post("/user/preferences", checkJwt, usersController.createUserPreferences);
router.put("/user/preferences", checkJwt, usersController.updateUserPreferences);
router.delete("/user/preferences", checkJwt, usersController.deleteUserPreferences);

// User Educations routes
router.get("/user/educations", checkJwt, usersController.getUserEducations);
router.get("/user/educations/:id", checkJwt, usersController.getUserEducationById);
router.post("/user/educations", checkJwt, usersController.createUserEducation);
router.put("/user/educations/:id", checkJwt, usersController.updateUserEducation);
router.delete("/user/educations/:id", checkJwt, usersController.deleteUserEducation);

// User Experiences routes
router.get("/user/experiences", checkJwt, usersController.getUserExperiences);
router.get("/user/experiences/:id", checkJwt, usersController.getUserExperienceById);
router.post("/user/experiences", checkJwt, usersController.createUserExperience);
router.put("/user/experiences/:id", checkJwt, usersController.updateUserExperience);
router.delete("/user/experiences/:id", checkJwt, usersController.deleteUserExperience);

// User Jobs routes
router.get("/user/jobs", checkJwt, usersController.getUserJobs);
router.get("/user/jobs/:id", checkJwt, usersController.getUserJobById);
router.post("/user/jobs", checkJwt, usersController.createUserJob);
router.put("/user/jobs/:id", checkJwt, usersController.updateUserJob);
router.delete("/user/jobs/:id", checkJwt, usersController.deleteUserJob);

// User Events routes
router.get("/user/events", checkJwt, usersController.getUserEvents);
router.get("/user/events/:id", checkJwt, usersController.getUserEventById);
router.post("/user/events", checkJwt, usersController.createUserEvent);
router.put("/user/events/:id", checkJwt, usersController.updateUserEvent);
router.delete("/user/events/:id", checkJwt, usersController.deleteUserEvent);

// Update routes
router.post("/update/subnets", checkInternalKey, subnetsController.updateSubnetsWithTaoStats);
router.post("/update/subnets-metrics", checkInternalKey, subnetMetricsController.updateSubnetsMetricsWithTaoStats);
router.post("/update/github-team-members", checkInternalKey, githubTeamMembersController.syncAllTeamsFromGitHub);
router.post(
	"/update/github-contributions",
	checkInternalKey,
	githubContributionsController.updateAllSubnetsGithubContributions
);
router.post("/update/prices", checkInternalKey, networkPricesController.updateNetworkPricesWithTaoStats);
router.post("/update/network-stats", checkInternalKey, networkStatsController.updateNetworkStatsWithTaoStats);
router.post("/update/validators", checkInternalKey, validatorsController.updateValidatorsWithTaoStats);
router.post(
	"/update/validator-performance",
	checkInternalKey,
	validatorPerformanceController.updateValidatorsPerformanceWithTaoStats
);
router.post("/update/all-tao", checkInternalKey, updateController.updateAllTaostats);
router.post("/update/update-all-links", checkInternalKey, updateController.updateAllTablesRelationships);
router.get("/update/status", checkInternalKey, updateController.getUpdateStatus);

// Ads routes
const adsRoutes = require("./adsRoutes");
router.use("/", adsRoutes);

// Admin routes
const adminRoutes = require("./adminRoutes");
router.use("/admin", adminRoutes);

// Billing routes
const billingRoutes = require("./billingRoutes");
router.use("/billing", billingRoutes);

// Invoice routes
const invoiceRoutes = require("./invoiceRoutes");
router.use("/invoices", invoiceRoutes);

// Webhook routes
const webhookRoutes = require("./webhookRoutes");
router.use("/billing/webhooks", webhookRoutes);

// API Endpoints documentation
router.use("/endpoints", endpointsRoutes);

// Static file serving for uploaded images
router.use("/uploads", express.static(path.join(process.cwd(), "uploads")));

module.exports = router;
