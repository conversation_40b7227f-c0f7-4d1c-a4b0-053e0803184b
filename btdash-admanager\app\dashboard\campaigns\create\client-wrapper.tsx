"use client";

import { CampaignWizard } from "@/components/campaigns/campaign-wizard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface CreateCampaignClientWrapperProps {
	placementId: string | null;
}

export default function CreateCampaignClientWrapper({ placementId }: CreateCampaignClientWrapperProps) {
	const { toast } = useToast();
	const router = useRouter();

	const handleCampaignComplete = async (campaignData: any) => {
		try {
			// Transform the wizard data to match the API format for multiple placements
			const apiData = {
				name: campaignData.name,
				description: campaignData.description,
				total_budget: campaignData.budget_type === "total" ? campaignData.total_budget : null,
				budget_cpc: campaignData.budget_type === "cpc" ? campaignData.budget_cpc : null,
				budget_cpm: campaignData.budget_type === "cpm" ? campaignData.budget_cpm : null,
				start_date: campaignData.start_date?.toISOString().split("T")[0],
				end_date: campaignData.end_date?.toISOString().split("T")[0],
				// Targeting data
				targeting: campaignData.targeting,
				// Multiple placements and their creatives
				placements: campaignData.selected_placements.map((placementId: number) => {
					const creative = campaignData.placement_creatives[placementId];
					return {
						slot_id: placementId,
						title: creative.ad_title,
						image_url: creative.image_url,
						target_url: creative.destination_url,
						description: creative.ad_description,
						max_impressions: null,
						max_clicks: null,
						weight: 1,
					};
				}),
			};

			console.log("Sending campaign data:", apiData);

			const response = await fetch("/api/user/campaigns", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(apiData),
			});

			console.log("Response status:", response.status);
			console.log("Response headers:", response.headers);

			if (!response.ok) {
				const errorText = await response.text();
				console.error("HTTP Error:", response.status, errorText);
				throw new Error(`HTTP ${response.status}: ${errorText}`);
			}

			const result = await response.json();
			console.log("API Response:", result);

			if (result.success) {
				toast({
					title: "Campaign Created",
					description: "Your campaign has been submitted for review.",
				});
				router.push("/dashboard/campaigns");
			} else {
				console.error("API Error:", result);
				throw new Error(result.message || result.error || "Failed to create campaign");
			}
		} catch (error: any) {
			console.error("Campaign creation error:", error);
			toast({
				title: "Error",
				description: error.message || "Failed to create campaign. Please try again.",
				variant: "destructive",
			});
			throw error; // Re-throw to let the wizard handle the loading state
		}
	};

	const handleCancel = () => {
		router.push("/dashboard/campaigns");
	};

	return (
		<div className="container mx-auto py-6">
			<div className="mb-6">
				<Link href="/dashboard/campaigns">
					<Button variant="ghost" className="mb-4">
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Campaigns
					</Button>
				</Link>
				<h1 className="text-3xl font-bold tracking-tight">Create New Campaign</h1>
				<p className="text-muted-foreground">Create a new advertising campaign with our step-by-step wizard.</p>
			</div>

			<CampaignWizard
				onComplete={handleCampaignComplete}
				onCancel={handleCancel}
				initialPlacementId={placementId}
			/>
		</div>
	);
}
