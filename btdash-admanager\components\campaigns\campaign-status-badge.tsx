"use client";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { 
	Clock, 
	CheckCircle, 
	XCircle, 
	CreditCard, 
	Play,
	Pause,
	AlertCircle
} from "lucide-react";

export type CampaignStatus = 
	| "pending" 
	| "approved" 
	| "rejected" 
	| "active" 
	| "paused" 
	| "completed" 
	| "payment_required";

interface CampaignStatusBadgeProps {
	status: CampaignStatus;
	size?: "sm" | "md" | "lg";
	showIcon?: boolean;
	className?: string;
}

export function CampaignStatusBadge({ 
	status, 
	size = "md", 
	showIcon = true, 
	className 
}: CampaignStatusBadgeProps) {
	const getStatusConfig = () => {
		switch (status) {
			case "pending":
				return {
					label: "Pending Review",
					variant: "outline" as const,
					icon: Clock,
					className: "border-yellow-200 bg-yellow-50 text-yellow-700 hover:bg-yellow-100",
				};
			case "approved":
				return {
					label: "Approved",
					variant: "secondary" as const,
					icon: CheckCircle,
					className: "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100",
				};
			case "rejected":
				return {
					label: "Rejected",
					variant: "destructive" as const,
					icon: XCircle,
					className: "border-red-200 bg-red-50 text-red-700 hover:bg-red-100",
				};
			case "payment_required":
				return {
					label: "Payment Required",
					variant: "outline" as const,
					icon: CreditCard,
					className: "border-orange-200 bg-orange-50 text-orange-700 hover:bg-orange-100",
				};
			case "active":
				return {
					label: "Active",
					variant: "default" as const,
					icon: Play,
					className: "border-green-200 bg-green-50 text-green-700 hover:bg-green-100",
				};
			case "paused":
				return {
					label: "Paused",
					variant: "secondary" as const,
					icon: Pause,
					className: "border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100",
				};
			case "completed":
				return {
					label: "Completed",
					variant: "outline" as const,
					icon: CheckCircle,
					className: "border-gray-200 bg-gray-50 text-gray-600 hover:bg-gray-100",
				};
			default:
				return {
					label: "Unknown",
					variant: "outline" as const,
					icon: AlertCircle,
					className: "border-gray-200 bg-gray-50 text-gray-600",
				};
		}
	};

	const config = getStatusConfig();
	const Icon = config.icon;

	const sizeClasses = {
		sm: "text-xs px-2 py-0.5",
		md: "text-sm px-2.5 py-1",
		lg: "text-base px-3 py-1.5",
	};

	const iconSizes = {
		sm: "h-3 w-3",
		md: "h-4 w-4", 
		lg: "h-5 w-5",
	};

	return (
		<Badge
			variant={config.variant}
			className={cn(
				sizeClasses[size],
				config.className,
				"font-medium inline-flex items-center gap-1.5",
				className
			)}
		>
			{showIcon && <Icon className={iconSizes[size]} />}
			{config.label}
		</Badge>
	);
}

export function getStatusDescription(status: CampaignStatus): string {
	switch (status) {
		case "pending":
			return "Your campaign is being reviewed by our team. This typically takes 1-2 business days.";
		case "approved":
			return "Great! Your campaign has been approved. Complete payment to make it live.";
		case "rejected":
			return "Your campaign needs changes before it can be approved. Check the rejection details below.";
		case "payment_required":
			return "Payment is required to activate your approved campaign.";
		case "active":
			return "Your campaign is live and serving ads to your target audience.";
		case "paused":
			return "Your campaign is temporarily paused and not serving ads.";
		case "completed":
			return "Your campaign has finished running and is no longer active.";
		default:
			return "Campaign status is unknown.";
	}
}

export function getNextSteps(status: CampaignStatus): string[] {
	switch (status) {
		case "pending":
			return [
				"Wait for admin review (1-2 business days)",
				"You'll receive an email notification when reviewed",
				"Make sure your contact information is up to date"
			];
		case "approved":
			return [
				"Click 'Pay Now' to complete payment",
				"Your campaign will go live immediately after payment",
				"Monitor performance in the Analytics section"
			];
		case "rejected":
			return [
				"Review the rejection reason and admin notes",
				"Make necessary changes to your campaign",
				"Resubmit for review once issues are addressed"
			];
		case "payment_required":
			return [
				"Complete payment to activate your campaign",
				"Ensure your payment method is valid",
				"Contact support if you encounter payment issues"
			];
		case "active":
			return [
				"Monitor campaign performance in Analytics",
				"Adjust targeting if needed",
				"Consider creating additional campaigns"
			];
		case "paused":
			return [
				"Resume campaign when ready",
				"Review and adjust settings if needed",
				"Check budget and targeting settings"
			];
		case "completed":
			return [
				"Review campaign performance",
				"Download reports for your records",
				"Create new campaigns based on learnings"
			];
		default:
			return ["Contact support for assistance"];
	}
}
