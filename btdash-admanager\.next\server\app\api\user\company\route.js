/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/company/route";
exports.ids = ["app/api/user/company/route"];
exports.modules = {

/***/ "(rsc)/./app/api/user/company/route.ts":
/*!***************************************!*\
  !*** ./app/api/user/company/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n\n\nconst POST = async function createCompany(req) {\n    try {\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", req.url));\n        }\n        const body = await req.json();\n        const response = await fetch(`${process.env.API_BASE_URL}/user/companies/with-owner`, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(body)\n        });\n        const json = await response.json();\n        if (!response.ok) {\n            console.error(\"Backend API error:\", {\n                status: response.status,\n                statusText: response.statusText,\n                error: json,\n                url: `${process.env.API_BASE_URL}/user/companies/with-owner`\n            });\n            throw new Error(json.message || json.error || `Backend API error: ${response.status}`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(json);\n    } catch (err) {\n        console.error(\"Company creation error:\", err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: err.message\n        }, {\n            status: 500\n        });\n    }\n};\nconst GET = async function getUserCompany(req) {\n    try {\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", req.url));\n        const res = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.message);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error fetching user company:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n};\nconst PUT = async function updateUserCompany(req) {\n    try {\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", req.url));\n        const body = await req.json();\n        const companyId = body.id;\n        const res = await fetch(`${process.env.API_BASE_URL}/user/companies/${companyId}`, {\n            method: \"PUT\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(body)\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.message);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error updating company:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n};\nconst DELETE = async function deleteUserCompany(req) {\n    try {\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", req.url));\n        const { id } = await req.json(); // expect { id: string }\n        const res = await fetch(`${process.env.API_BASE_URL}/user/companies/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!res.ok) {\n            const error = await res.json();\n            throw new Error(error.message || \"Failed to delete company\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error deleting company:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3VzZXIvY29tcGFueS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDRztBQUV2QyxNQUFNRSxPQUFPLGVBQWVDLGNBQWNDLEdBQVk7SUFDNUQsSUFBSTtRQUNILE1BQU0sRUFBRUMsT0FBT0MsV0FBVyxFQUFFLEdBQUcsTUFBTUwsNkNBQUtBLENBQUNNLGNBQWM7UUFFekQsSUFBSSxDQUFDRCxhQUFhO1lBQ2pCLE9BQU9OLHFEQUFZQSxDQUFDUSxRQUFRLENBQUMsSUFBSUMsSUFBSSxtQkFBbUJMLElBQUlNLEdBQUc7UUFDaEU7UUFFQSxNQUFNQyxPQUFPLE1BQU1QLElBQUlRLElBQUk7UUFFM0IsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLEdBQUdDLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWSxDQUFDLDBCQUEwQixDQUFDLEVBQUU7WUFDckZDLFFBQVE7WUFDUkMsU0FBUztnQkFDUkMsZUFBZSxDQUFDLE9BQU8sRUFBRWQsYUFBYTtnQkFDdEMsZ0JBQWdCO1lBQ2pCO1lBQ0FLLE1BQU1VLEtBQUtDLFNBQVMsQ0FBQ1g7UUFDdEI7UUFFQSxNQUFNQyxPQUFPLE1BQU1DLFNBQVNELElBQUk7UUFDaEMsSUFBSSxDQUFDQyxTQUFTVSxFQUFFLEVBQUU7WUFDakJDLFFBQVFDLEtBQUssQ0FBQyxzQkFBc0I7Z0JBQ25DQyxRQUFRYixTQUFTYSxNQUFNO2dCQUN2QkMsWUFBWWQsU0FBU2MsVUFBVTtnQkFDL0JGLE9BQU9iO2dCQUNQRixLQUFLLEdBQUdLLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWSxDQUFDLDBCQUEwQixDQUFDO1lBQzdEO1lBQ0EsTUFBTSxJQUFJVyxNQUFNaEIsS0FBS2lCLE9BQU8sSUFBSWpCLEtBQUthLEtBQUssSUFBSSxDQUFDLG1CQUFtQixFQUFFWixTQUFTYSxNQUFNLEVBQUU7UUFDdEY7UUFFQSxPQUFPMUIscURBQVlBLENBQUNZLElBQUksQ0FBQ0E7SUFDMUIsRUFBRSxPQUFPa0IsS0FBVTtRQUNsQk4sUUFBUUMsS0FBSyxDQUFDLDJCQUEyQks7UUFDekMsT0FBTzlCLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7WUFBRWEsT0FBT0ssSUFBSUQsT0FBTztRQUFDLEdBQUc7WUFBRUgsUUFBUTtRQUFJO0lBQ2hFO0FBQ0QsRUFBRTtBQUVLLE1BQU1LLE1BQU0sZUFBZUMsZUFBZTVCLEdBQVk7SUFDNUQsSUFBSTtRQUNILE1BQU0sRUFBRUMsT0FBT0MsV0FBVyxFQUFFLEdBQUcsTUFBTUwsNkNBQUtBLENBQUNNLGNBQWM7UUFDekQsSUFBSSxDQUFDRCxhQUFhLE9BQU9OLHFEQUFZQSxDQUFDUSxRQUFRLENBQUMsSUFBSUMsSUFBSSxtQkFBbUJMLElBQUlNLEdBQUc7UUFFakYsTUFBTXVCLE1BQU0sTUFBTW5CLE1BQU0sR0FBR0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLENBQUMsa0JBQWtCLENBQUMsRUFBRTtZQUN4RUUsU0FBUztnQkFBRUMsZUFBZSxDQUFDLE9BQU8sRUFBRWQsYUFBYTtZQUFDO1FBQ25EO1FBRUEsTUFBTTRCLE9BQU8sTUFBTUQsSUFBSXJCLElBQUk7UUFDM0IsSUFBSSxDQUFDcUIsSUFBSVYsRUFBRSxFQUFFLE1BQU0sSUFBSUssTUFBTU0sS0FBS0wsT0FBTztRQUV6QyxPQUFPN0IscURBQVlBLENBQUNZLElBQUksQ0FBQ3NCO0lBQzFCLEVBQUUsT0FBT1QsT0FBWTtRQUNwQkQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBT3pCLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7WUFBRWEsT0FBT0EsTUFBTUksT0FBTztRQUFDLEdBQUc7WUFBRUgsUUFBUTtRQUFJO0lBQ2xFO0FBQ0QsRUFBRTtBQUVLLE1BQU1TLE1BQU0sZUFBZUMsa0JBQWtCaEMsR0FBWTtJQUMvRCxJQUFJO1FBQ0gsTUFBTSxFQUFFQyxPQUFPQyxXQUFXLEVBQUUsR0FBRyxNQUFNTCw2Q0FBS0EsQ0FBQ00sY0FBYztRQUN6RCxJQUFJLENBQUNELGFBQWEsT0FBT04scURBQVlBLENBQUNRLFFBQVEsQ0FBQyxJQUFJQyxJQUFJLG1CQUFtQkwsSUFBSU0sR0FBRztRQUVqRixNQUFNQyxPQUFPLE1BQU1QLElBQUlRLElBQUk7UUFDM0IsTUFBTXlCLFlBQVkxQixLQUFLMkIsRUFBRTtRQUV6QixNQUFNTCxNQUFNLE1BQU1uQixNQUFNLEdBQUdDLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWSxDQUFDLGdCQUFnQixFQUFFb0IsV0FBVyxFQUFFO1lBQ2xGbkIsUUFBUTtZQUNSQyxTQUFTO2dCQUNSQyxlQUFlLENBQUMsT0FBTyxFQUFFZCxhQUFhO2dCQUN0QyxnQkFBZ0I7WUFDakI7WUFDQUssTUFBTVUsS0FBS0MsU0FBUyxDQUFDWDtRQUN0QjtRQUVBLE1BQU11QixPQUFPLE1BQU1ELElBQUlyQixJQUFJO1FBQzNCLElBQUksQ0FBQ3FCLElBQUlWLEVBQUUsRUFBRSxNQUFNLElBQUlLLE1BQU1NLEtBQUtMLE9BQU87UUFFekMsT0FBTzdCLHFEQUFZQSxDQUFDWSxJQUFJLENBQUNzQjtJQUMxQixFQUFFLE9BQU9ULE9BQVk7UUFDcEJELFFBQVFDLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU96QixxREFBWUEsQ0FBQ1ksSUFBSSxDQUFDO1lBQUVhLE9BQU9BLE1BQU1JLE9BQU87UUFBQyxHQUFHO1lBQUVILFFBQVE7UUFBSTtJQUNsRTtBQUNELEVBQUU7QUFFSyxNQUFNYSxTQUFTLGVBQWVDLGtCQUFrQnBDLEdBQVk7SUFDbEUsSUFBSTtRQUNILE1BQU0sRUFBRUMsT0FBT0MsV0FBVyxFQUFFLEdBQUcsTUFBTUwsNkNBQUtBLENBQUNNLGNBQWM7UUFDekQsSUFBSSxDQUFDRCxhQUFhLE9BQU9OLHFEQUFZQSxDQUFDUSxRQUFRLENBQUMsSUFBSUMsSUFBSSxtQkFBbUJMLElBQUlNLEdBQUc7UUFFakYsTUFBTSxFQUFFNEIsRUFBRSxFQUFFLEdBQUcsTUFBTWxDLElBQUlRLElBQUksSUFBSSx3QkFBd0I7UUFFekQsTUFBTXFCLE1BQU0sTUFBTW5CLE1BQU0sR0FBR0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLENBQUMsZ0JBQWdCLEVBQUVxQixJQUFJLEVBQUU7WUFDM0VwQixRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1JDLGVBQWUsQ0FBQyxPQUFPLEVBQUVkLGFBQWE7Z0JBQ3RDLGdCQUFnQjtZQUNqQjtRQUNEO1FBRUEsSUFBSSxDQUFDMkIsSUFBSVYsRUFBRSxFQUFFO1lBQ1osTUFBTUUsUUFBUSxNQUFNUSxJQUFJckIsSUFBSTtZQUM1QixNQUFNLElBQUlnQixNQUFNSCxNQUFNSSxPQUFPLElBQUk7UUFDbEM7UUFFQSxPQUFPN0IscURBQVlBLENBQUNZLElBQUksQ0FBQztZQUFFNkIsU0FBUztRQUFLO0lBQzFDLEVBQUUsT0FBT2hCLE9BQVk7UUFDcEJELFFBQVFDLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU96QixxREFBWUEsQ0FBQ1ksSUFBSSxDQUFDO1lBQUVhLE9BQU9BLE1BQU1JLE9BQU87UUFBQyxHQUFHO1lBQUVILFFBQVE7UUFBSTtJQUNsRTtBQUNELEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoLWFkbWFuYWdlclxcYXBwXFxhcGlcXHVzZXJcXGNvbXBhbnlcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xyXG5pbXBvcnQgeyBhdXRoMCB9IGZyb20gXCIuLi8uLi8uLi8uLi9saWIvYXV0aDBcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBQT1NUID0gYXN5bmMgZnVuY3Rpb24gY3JlYXRlQ29tcGFueShyZXE6IFJlcXVlc3QpIHtcclxuXHR0cnkge1xyXG5cdFx0Y29uc3QgeyB0b2tlbjogYWNjZXNzVG9rZW4gfSA9IGF3YWl0IGF1dGgwLmdldEFjY2Vzc1Rva2VuKCk7XHJcblxyXG5cdFx0aWYgKCFhY2Nlc3NUb2tlbikge1xyXG5cdFx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KG5ldyBVUkwoXCIvYXBpL2F1dGgvbG9naW5cIiwgcmVxLnVybCkpO1xyXG5cdFx0fVxyXG5cclxuXHRcdGNvbnN0IGJvZHkgPSBhd2FpdCByZXEuanNvbigpO1xyXG5cclxuXHRcdGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuQVBJX0JBU0VfVVJMfS91c2VyL2NvbXBhbmllcy93aXRoLW93bmVyYCwge1xyXG5cdFx0XHRtZXRob2Q6IFwiUE9TVFwiLFxyXG5cdFx0XHRoZWFkZXJzOiB7XHJcblx0XHRcdFx0QXV0aG9yaXphdGlvbjogYEJlYXJlciAke2FjY2Vzc1Rva2VufWAsXHJcblx0XHRcdFx0XCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcblx0XHRcdH0sXHJcblx0XHRcdGJvZHk6IEpTT04uc3RyaW5naWZ5KGJvZHkpLFxyXG5cdFx0fSk7XHJcblxyXG5cdFx0Y29uc3QganNvbiA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHRcdGlmICghcmVzcG9uc2Uub2spIHtcclxuXHRcdFx0Y29uc29sZS5lcnJvcihcIkJhY2tlbmQgQVBJIGVycm9yOlwiLCB7XHJcblx0XHRcdFx0c3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXHJcblx0XHRcdFx0c3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcclxuXHRcdFx0XHRlcnJvcjoganNvbixcclxuXHRcdFx0XHR1cmw6IGAke3Byb2Nlc3MuZW52LkFQSV9CQVNFX1VSTH0vdXNlci9jb21wYW5pZXMvd2l0aC1vd25lcmAsXHJcblx0XHRcdH0pO1xyXG5cdFx0XHR0aHJvdyBuZXcgRXJyb3IoanNvbi5tZXNzYWdlIHx8IGpzb24uZXJyb3IgfHwgYEJhY2tlbmQgQVBJIGVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuXHRcdH1cclxuXHJcblx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oanNvbik7XHJcblx0fSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuXHRcdGNvbnNvbGUuZXJyb3IoXCJDb21wYW55IGNyZWF0aW9uIGVycm9yOlwiLCBlcnIpO1xyXG5cdFx0cmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IGVyci5tZXNzYWdlIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcblx0fVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IEdFVCA9IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJDb21wYW55KHJlcTogUmVxdWVzdCkge1xyXG5cdHRyeSB7XHJcblx0XHRjb25zdCB7IHRva2VuOiBhY2Nlc3NUb2tlbiB9ID0gYXdhaXQgYXV0aDAuZ2V0QWNjZXNzVG9rZW4oKTtcclxuXHRcdGlmICghYWNjZXNzVG9rZW4pIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobmV3IFVSTChcIi9hcGkvYXV0aC9sb2dpblwiLCByZXEudXJsKSk7XHJcblxyXG5cdFx0Y29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuQVBJX0JBU0VfVVJMfS91c2VyL2NvbXBhbmllcy9tZWAsIHtcclxuXHRcdFx0aGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCB9LFxyXG5cdFx0fSk7XHJcblxyXG5cdFx0Y29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKCk7XHJcblx0XHRpZiAoIXJlcy5vaykgdGhyb3cgbmV3IEVycm9yKGRhdGEubWVzc2FnZSk7XHJcblxyXG5cdFx0cmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGRhdGEpO1xyXG5cdH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuXHRcdGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyB1c2VyIGNvbXBhbnk6XCIsIGVycm9yKTtcclxuXHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBlcnJvci5tZXNzYWdlIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcblx0fVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IFBVVCA9IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZVVzZXJDb21wYW55KHJlcTogUmVxdWVzdCkge1xyXG5cdHRyeSB7XHJcblx0XHRjb25zdCB7IHRva2VuOiBhY2Nlc3NUb2tlbiB9ID0gYXdhaXQgYXV0aDAuZ2V0QWNjZXNzVG9rZW4oKTtcclxuXHRcdGlmICghYWNjZXNzVG9rZW4pIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobmV3IFVSTChcIi9hcGkvYXV0aC9sb2dpblwiLCByZXEudXJsKSk7XHJcblxyXG5cdFx0Y29uc3QgYm9keSA9IGF3YWl0IHJlcS5qc29uKCk7XHJcblx0XHRjb25zdCBjb21wYW55SWQgPSBib2R5LmlkO1xyXG5cclxuXHRcdGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGAke3Byb2Nlc3MuZW52LkFQSV9CQVNFX1VSTH0vdXNlci9jb21wYW5pZXMvJHtjb21wYW55SWR9YCwge1xyXG5cdFx0XHRtZXRob2Q6IFwiUFVUXCIsXHJcblx0XHRcdGhlYWRlcnM6IHtcclxuXHRcdFx0XHRBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcclxuXHRcdFx0XHRcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuXHRcdFx0fSxcclxuXHRcdFx0Ym9keTogSlNPTi5zdHJpbmdpZnkoYm9keSksXHJcblx0XHR9KTtcclxuXHJcblx0XHRjb25zdCBkYXRhID0gYXdhaXQgcmVzLmpzb24oKTtcclxuXHRcdGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoZGF0YS5tZXNzYWdlKTtcclxuXHJcblx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZGF0YSk7XHJcblx0fSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG5cdFx0Y29uc29sZS5lcnJvcihcIkVycm9yIHVwZGF0aW5nIGNvbXBhbnk6XCIsIGVycm9yKTtcclxuXHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBlcnJvci5tZXNzYWdlIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcblx0fVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IERFTEVURSA9IGFzeW5jIGZ1bmN0aW9uIGRlbGV0ZVVzZXJDb21wYW55KHJlcTogUmVxdWVzdCkge1xyXG5cdHRyeSB7XHJcblx0XHRjb25zdCB7IHRva2VuOiBhY2Nlc3NUb2tlbiB9ID0gYXdhaXQgYXV0aDAuZ2V0QWNjZXNzVG9rZW4oKTtcclxuXHRcdGlmICghYWNjZXNzVG9rZW4pIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobmV3IFVSTChcIi9hcGkvYXV0aC9sb2dpblwiLCByZXEudXJsKSk7XHJcblxyXG5cdFx0Y29uc3QgeyBpZCB9ID0gYXdhaXQgcmVxLmpzb24oKTsgLy8gZXhwZWN0IHsgaWQ6IHN0cmluZyB9XHJcblxyXG5cdFx0Y29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuQVBJX0JBU0VfVVJMfS91c2VyL2NvbXBhbmllcy8ke2lkfWAsIHtcclxuXHRcdFx0bWV0aG9kOiBcIkRFTEVURVwiLFxyXG5cdFx0XHRoZWFkZXJzOiB7XHJcblx0XHRcdFx0QXV0aG9yaXphdGlvbjogYEJlYXJlciAke2FjY2Vzc1Rva2VufWAsXHJcblx0XHRcdFx0XCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcblx0XHRcdH0sXHJcblx0XHR9KTtcclxuXHJcblx0XHRpZiAoIXJlcy5vaykge1xyXG5cdFx0XHRjb25zdCBlcnJvciA9IGF3YWl0IHJlcy5qc29uKCk7XHJcblx0XHRcdHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIGRlbGV0ZSBjb21wYW55XCIpO1xyXG5cdFx0fVxyXG5cclxuXHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHN1Y2Nlc3M6IHRydWUgfSk7XHJcblx0fSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG5cdFx0Y29uc29sZS5lcnJvcihcIkVycm9yIGRlbGV0aW5nIGNvbXBhbnk6XCIsIGVycm9yKTtcclxuXHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBlcnJvci5tZXNzYWdlIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcblx0fVxyXG59O1xyXG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiYXV0aDAiLCJQT1NUIiwiY3JlYXRlQ29tcGFueSIsInJlcSIsInRva2VuIiwiYWNjZXNzVG9rZW4iLCJnZXRBY2Nlc3NUb2tlbiIsInJlZGlyZWN0IiwiVVJMIiwidXJsIiwiYm9keSIsImpzb24iLCJyZXNwb25zZSIsImZldGNoIiwicHJvY2VzcyIsImVudiIsIkFQSV9CQVNFX1VSTCIsIm1ldGhvZCIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwiSlNPTiIsInN0cmluZ2lmeSIsIm9rIiwiY29uc29sZSIsImVycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsIkVycm9yIiwibWVzc2FnZSIsImVyciIsIkdFVCIsImdldFVzZXJDb21wYW55IiwicmVzIiwiZGF0YSIsIlBVVCIsInVwZGF0ZVVzZXJDb21wYW55IiwiY29tcGFueUlkIiwiaWQiLCJERUxFVEUiLCJkZWxldGVVc2VyQ29tcGFueSIsInN1Y2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/company/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcompany%2Froute&page=%2Fapi%2Fuser%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcompany%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcompany%2Froute&page=%2Fapi%2Fuser%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcompany%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_company_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/company/route.ts */ \"(rsc)/./app/api/user/company/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/company/route\",\n        pathname: \"/api/user/company\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/company/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\api\\\\user\\\\company\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_company_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcompany%2Froute&page=%2Fapi%2Fuser%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcompany%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcompany%2Froute&page=%2Fapi%2Fuser%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcompany%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();