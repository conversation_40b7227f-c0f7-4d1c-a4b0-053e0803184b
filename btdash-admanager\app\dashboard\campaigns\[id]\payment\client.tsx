"use client";

import { PaymentRetryInterface } from "@/components/billing/payment-retry-interface";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Campaign {
	id: number;
	name: string;
	total_budget: number | string;
	start_date: string;
	end_date: string;
	status: string;
	failure_reason?: string;
	last_payment_attempt?: string;
}

interface PaymentTransaction {
	id: number;
	amount: number;
	status: string;
	payment_method: string;
	description: string;
	created_at: string;
	failure_reason?: string;
}

interface CampaignPaymentClientProps {
	campaignId: string;
}

export default function CampaignPaymentClient({ campaignId }: CampaignPaymentClientProps) {
	const router = useRouter();
	const [campaign, setCampaign] = useState<Campaign | null>(null);
	const [paymentHistory, setPaymentHistory] = useState<PaymentTransaction[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchData = async () => {
			try {
				setLoading(true);
				setError(null);

				// Fetch campaign and payment history in parallel
				const [campaignRes, paymentHistoryRes] = await Promise.all([
					fetch(`/api/user/campaigns/${campaignId}`),
					fetch(`/api/campaigns/${campaignId}/payment-history`),
				]);

				// Handle campaign response
				if (!campaignRes.ok) {
					throw new Error(`Failed to fetch campaign: ${campaignRes.status}`);
				}
				const campaignData = await campaignRes.json();

				if (!campaignData.success) {
					throw new Error(campaignData.message || "Failed to fetch campaign");
				}

				const fetchedCampaign = campaignData.data;
				setCampaign(fetchedCampaign);

				// Check if campaign requires payment
				const requiresPayment = ["approved", "payment_failed", "payment_error"].includes(
					fetchedCampaign.status
				);

				if (!requiresPayment) {
					router.push(`/dashboard/campaigns/${campaignId}`);
					return;
				}

				// Handle payment history response
				if (paymentHistoryRes.ok) {
					const paymentData = await paymentHistoryRes.json();
					if (paymentData.success) {
						setPaymentHistory(paymentData.data || []);
					}
				}
				// Don't throw error for payment history failure - it's not critical
			} catch (error) {
				console.error("Error fetching campaign data:", error);
				setError(error instanceof Error ? error.message : "Failed to load campaign data");

				// If campaign fetch fails, redirect to campaigns list
				if (error instanceof Error && error.message.includes("Failed to fetch campaign")) {
					toast.error("Campaign not found");
					router.push("/dashboard/campaigns");
				}
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [campaignId, router]);

	if (loading) {
		return (
			<div className="container mx-auto py-6 space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold tracking-tight">Campaign Payment</h1>
						<p className="text-muted-foreground">Loading campaign details...</p>
					</div>
				</div>
				<div className="flex items-center justify-center py-12">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto py-6 space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold tracking-tight">Campaign Payment</h1>
						<p className="text-muted-foreground text-red-600">Error loading campaign</p>
					</div>
				</div>
				<div className="flex flex-col items-center justify-center py-12 space-y-4">
					<p className="text-red-600">{error}</p>
					<button
						onClick={() => window.location.reload()}
						className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
					>
						Retry
					</button>
				</div>
			</div>
		);
	}

	if (!campaign) {
		return (
			<div className="container mx-auto py-6 space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold tracking-tight">Campaign Payment</h1>
						<p className="text-muted-foreground">Campaign not found</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6 space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Campaign Payment</h1>
					<p className="text-muted-foreground">
						Complete payment for "{campaign.name}" to activate your campaign
					</p>
				</div>
			</div>

			<PaymentRetryInterface campaign={campaign} paymentHistory={paymentHistory} />
		</div>
	);
}
