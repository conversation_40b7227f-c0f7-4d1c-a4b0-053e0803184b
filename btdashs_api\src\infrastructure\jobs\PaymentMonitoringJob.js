const cron = require('node-cron');
const PaymentMonitoringService = require('../../application/services/PaymentMonitoringService');
const logger = require('../../../logger');

class PaymentMonitoringJob {
	constructor() {
		this.monitoringService = new PaymentMonitoringService();
		this.isRunning = false;
		this.jobs = new Map();
	}

	/**
	 * Start all monitoring jobs
	 */
	start() {
		logger.info("Starting payment monitoring jobs");

		// Real-time monitoring (every 5 minutes)
		this.scheduleJob('realtime', '*/5 * * * *', async () => {
			await this.runMonitoringCheck('5m', true);
		});

		// Hourly monitoring (every hour)
		this.scheduleJob('hourly', '0 * * * *', async () => {
			await this.runMonitoringCheck('1h', true);
		});

		// Daily monitoring (every day at 9 AM)
		this.scheduleJob('daily', '0 9 * * *', async () => {
			await this.runMonitoringCheck('24h', true);
		});

		// Weekly monitoring (every Monday at 9 AM)
		this.scheduleJob('weekly', '0 9 * * 1', async () => {
			await this.runMonitoringCheck('7d', true);
		});

		// Health check (every minute)
		this.scheduleJob('health', '* * * * *', async () => {
			await this.runHealthCheck();
		});

		this.isRunning = true;
		logger.info("Payment monitoring jobs started successfully");
	}

	/**
	 * Stop all monitoring jobs
	 */
	stop() {
		logger.info("Stopping payment monitoring jobs");

		for (const [name, job] of this.jobs) {
			job.destroy();
			logger.info(`Stopped monitoring job: ${name}`);
		}

		this.jobs.clear();
		this.isRunning = false;
		logger.info("Payment monitoring jobs stopped");
	}

	/**
	 * Schedule a monitoring job
	 * @param {string} name - Job name
	 * @param {string} schedule - Cron schedule
	 * @param {Function} task - Task function
	 */
	scheduleJob(name, schedule, task) {
		const job = cron.schedule(schedule, async () => {
			try {
				logger.debug(`Running monitoring job: ${name}`);
				await task();
				logger.debug(`Completed monitoring job: ${name}`);
			} catch (error) {
				logger.error(`Monitoring job failed: ${name}`, {
					error: error.message,
					stack: error.stack,
				});
			}
		}, {
			scheduled: false,
			timezone: process.env.TZ || 'UTC',
		});

		this.jobs.set(name, job);
		job.start();
		
		logger.info(`Scheduled monitoring job: ${name} (${schedule})`);
	}

	/**
	 * Run monitoring check
	 * @param {string} timeWindow - Time window for monitoring
	 * @param {boolean} includeWebhooks - Include webhook monitoring
	 */
	async runMonitoringCheck(timeWindow, includeWebhooks = true) {
		try {
			const result = await this.monitoringService.monitorPaymentHealth({
				timeWindow,
				includeWebhooks,
			});

			logger.info("Scheduled monitoring check completed", {
				timeWindow,
				status: result.status,
				alertCount: result.alerts.length,
			});

			// Log critical alerts separately
			const criticalAlerts = result.alerts.filter(alert => alert.severity === 'critical');
			if (criticalAlerts.length > 0) {
				logger.error("Critical payment alerts detected", {
					timeWindow,
					criticalAlerts: criticalAlerts.map(alert => ({
						type: alert.type,
						message: alert.message,
						value: alert.value,
					})),
				});
			}

			return result;

		} catch (error) {
			logger.error("Scheduled monitoring check failed", {
				timeWindow,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Run health check
	 */
	async runHealthCheck() {
		try {
			const health = await this.monitoringService.getSystemHealth();
			
			// Log unhealthy components
			if (health.database.status === 'unhealthy') {
				logger.error("Database health check failed", {
					error: health.database.error,
				});
			}

			if (health.stripe.status === 'unhealthy') {
				logger.error("Stripe health check failed", {
					error: health.stripe.error,
				});
			}

			return health;

		} catch (error) {
			logger.error("Health check failed", {
				error: error.message,
			});
		}
	}

	/**
	 * Get job status
	 * @returns {Object} Job status information
	 */
	getStatus() {
		const jobStatuses = {};
		
		for (const [name, job] of this.jobs) {
			jobStatuses[name] = {
				running: job.running,
				scheduled: job.scheduled,
			};
		}

		return {
			isRunning: this.isRunning,
			jobCount: this.jobs.size,
			jobs: jobStatuses,
		};
	}

	/**
	 * Restart specific job
	 * @param {string} jobName - Name of job to restart
	 */
	restartJob(jobName) {
		const job = this.jobs.get(jobName);
		if (!job) {
			throw new Error(`Job not found: ${jobName}`);
		}

		job.stop();
		job.start();
		
		logger.info(`Restarted monitoring job: ${jobName}`);
	}

	/**
	 * Run job manually
	 * @param {string} jobName - Name of job to run
	 */
	async runJobManually(jobName) {
		logger.info(`Manually triggering monitoring job: ${jobName}`);

		switch (jobName) {
			case 'realtime':
				return await this.runMonitoringCheck('5m', true);
			case 'hourly':
				return await this.runMonitoringCheck('1h', true);
			case 'daily':
				return await this.runMonitoringCheck('24h', true);
			case 'weekly':
				return await this.runMonitoringCheck('7d', true);
			case 'health':
				return await this.runHealthCheck();
			default:
				throw new Error(`Unknown job: ${jobName}`);
		}
	}

	/**
	 * Update job schedule
	 * @param {string} jobName - Name of job to update
	 * @param {string} newSchedule - New cron schedule
	 */
	updateJobSchedule(jobName, newSchedule) {
		const job = this.jobs.get(jobName);
		if (!job) {
			throw new Error(`Job not found: ${jobName}`);
		}

		// Stop current job
		job.destroy();
		this.jobs.delete(jobName);

		// Create new job with updated schedule
		const taskMap = {
			'realtime': () => this.runMonitoringCheck('5m', true),
			'hourly': () => this.runMonitoringCheck('1h', true),
			'daily': () => this.runMonitoringCheck('24h', true),
			'weekly': () => this.runMonitoringCheck('7d', true),
			'health': () => this.runHealthCheck(),
		};

		const task = taskMap[jobName];
		if (!task) {
			throw new Error(`No task defined for job: ${jobName}`);
		}

		this.scheduleJob(jobName, newSchedule, task);
		
		logger.info(`Updated schedule for monitoring job: ${jobName} (${newSchedule})`);
	}

	/**
	 * Get monitoring statistics
	 * @returns {Promise<Object>} Monitoring statistics
	 */
	async getMonitoringStatistics() {
		try {
			const [realtimeResult, hourlyResult] = await Promise.all([
				this.runMonitoringCheck('5m', true),
				this.runMonitoringCheck('1h', true),
			]);

			return {
				realtime: realtimeResult,
				hourly: hourlyResult,
				jobStatus: this.getStatus(),
			};

		} catch (error) {
			logger.error("Failed to get monitoring statistics", {
				error: error.message,
			});
			throw error;
		}
	}
}

// Create singleton instance
const paymentMonitoringJob = new PaymentMonitoringJob();

// Graceful shutdown handling
process.on('SIGTERM', () => {
	logger.info("Received SIGTERM, stopping monitoring jobs");
	paymentMonitoringJob.stop();
});

process.on('SIGINT', () => {
	logger.info("Received SIGINT, stopping monitoring jobs");
	paymentMonitoringJob.stop();
});

module.exports = paymentMonitoringJob;
