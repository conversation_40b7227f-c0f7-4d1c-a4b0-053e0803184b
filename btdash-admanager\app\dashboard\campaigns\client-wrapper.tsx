// app/dashboard/campaigns/client-wrapper.tsx
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import type { AdCampaign } from "@/lib/db/models";
import { ArrowRight, BarChart3, Edit, Plus } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface CampaignWithStats extends AdCampaign {
	impressions: number;
	clicks: number;
	ctr: number;
	spend: number;
	ad?: {
		image_url: string;
		target_url: string;
		max_impressions?: number;
		max_clicks?: number;
	};
}

export default function CampaignsClientWrapper({
	campaigns,
	success,
}: {
	campaigns: CampaignWithStats[];
	success: boolean;
}) {
	const searchParams = useSearchParams();
	const { toast } = useToast();
	const router = useRouter();
	const [activeTab, setActiveTab] = useState("all");

	// Filter campaigns by status
	const activeCampaigns = campaigns.filter((c) => c.status === "active");
	const pendingCampaigns = campaigns.filter((c) => c.status === "pending");
	const approvedCampaigns = campaigns.filter((c) => c.status === "approved");
	const rejectedCampaigns = campaigns.filter((c) => c.status === "rejected");
	const completedCampaigns = campaigns.filter((c) => c.status === "completed");
	const failedPaymentCampaigns = campaigns.filter(
		(c) => c.status === "payment_failed" || c.status === "payment_error"
	);

	useEffect(() => {
		if (success) {
			toast({
				title: "Payment successful!",
				description: "Your campaign has been submitted for review.",
				duration: 5000,
			});
			// Clear the success param from the URL
			const newParams = new URLSearchParams(searchParams.toString());
			newParams.delete("success");
			router.replace(`/dashboard/campaigns?${newParams.toString()}`, { scroll: false });
		}
	}, [success, toast, searchParams, router]);

	const renderBudgetInfo = (campaign: CampaignWithStats) => {
		if (campaign.budget_cpc) {
			return `$${campaign.budget_cpc} CPC`;
		}
		if (campaign.budget_cpm) {
			return `$${campaign.budget_cpm} CPM`;
		}
		if (campaign.total_budget) {
			return `$${campaign.total_budget}`;
		}
		return "No budget";
	};

	const renderCampaignCard = (campaign: CampaignWithStats) => (
		<Card key={campaign.id} className="hover:shadow-sm transition-shadow">
			<CardContent className="p-4">
				<div className="flex gap-4">
					<div className="relative w-24 h-16 flex-shrink-0 rounded-md overflow-hidden border">
						<Image
							src={campaign.ad?.image_url || "/placeholder.svg"}
							alt="Ad creative"
							fill
							className="object-cover"
						/>
					</div>

					<div className="flex-1 min-w-0">
						<div className="flex items-start justify-between gap-2">
							<div className="min-w-0">
								<CardTitle className="text-base truncate">{campaign.name}</CardTitle>
								<CardDescription className="text-xs">
									{new Date(campaign.start_date).toLocaleDateString()} -{" "}
									{new Date(campaign.end_date).toLocaleDateString()}
								</CardDescription>
							</div>

							<div className="flex flex-col items-end gap-1">
								{/* Use the new status badge component */}
								{(() => {
									const CampaignStatusBadge =
										require("@/components/campaigns/campaign-status-badge").CampaignStatusBadge;
									return <CampaignStatusBadge status={campaign.status} size="sm" />;
								})()}
								<span className="text-xs text-muted-foreground">{renderBudgetInfo(campaign)}</span>
							</div>
						</div>

						<div className="mt-2 flex items-center justify-between">
							<div className="flex gap-2">
								<span className="text-xs font-medium">{campaign.impressions || "n/a"} impressions</span>
								<span className="text-xs font-medium">{campaign.clicks || "n/a"} clicks</span>
								<span className="text-xs font-medium">{campaign.ctr || "n/a"}% CTR</span>
							</div>

							<div className="flex gap-1">
								<Link href={`/dashboard/campaigns/${campaign.id}`}>
									<Button variant="ghost" size="sm" className="h-8 px-2">
										<ArrowRight className="h-3.5 w-3.5" />
									</Button>
								</Link>
								{campaign.status === "active" && (
									<Link href={`/dashboard/campaigns/${campaign.id}/analytics`}>
										<Button variant="ghost" size="sm" className="h-8 px-2">
											<BarChart3 className="h-3.5 w-3.5" />
										</Button>
									</Link>
								)}
								{campaign.status === "pending" && (
									<Link href={`/dashboard/campaigns/${campaign.id}/edit`}>
										<Button variant="ghost" size="sm" className="h-8 px-2">
											<Edit className="h-3.5 w-3.5" />
										</Button>
									</Link>
								)}
								{(campaign.status === "approved" ||
									campaign.status === "payment_failed" ||
									campaign.status === "payment_error") && (
									<Link href={`/dashboard/campaigns/${campaign.id}/payment`}>
										<Button variant="ghost" size="sm" className="h-8 px-2">
											<CreditCard className="h-3.5 w-3.5" />
										</Button>
									</Link>
								)}
							</div>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);

	// Calculate dashboard statistics
	const totalCampaigns = campaigns.length;
	const totalImpressions = campaigns.reduce((sum, c) => sum + (c.impressions || 0), 0);
	const totalClicks = campaigns.reduce((sum, c) => sum + (c.clicks || 0), 0);
	const totalSpend = campaigns.reduce((sum, c) => sum + (c.spend || 0), 0);
	const avgCTR = totalImpressions > 0 ? ((totalClicks / totalImpressions) * 100).toFixed(2) : "0.00";

	return (
		<div className="space-y-6">
			{/* Payment Failure Dashboard */}
			{failedPaymentCampaigns.length > 0 && (
				<PaymentFailureDashboard
					failedCampaigns={failedPaymentCampaigns.map((campaign) => ({
						id: campaign.id,
						name: campaign.name,
						total_budget: campaign.total_budget || 0,
						status: campaign.status,
						failure_reason: campaign.failure_reason,
						last_payment_attempt: campaign.last_payment_attempt,
						retry_count: campaign.retry_count || 0,
					}))}
				/>
			)}

			{/* Dashboard Statistics */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card>
					<CardContent className="p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground">Total Campaigns</p>
								<p className="text-2xl font-bold">{totalCampaigns}</p>
							</div>
							<BarChart3 className="h-8 w-8 text-muted-foreground" />
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground">Total Impressions</p>
								<p className="text-2xl font-bold">{totalImpressions.toLocaleString()}</p>
							</div>
							<ArrowRight className="h-8 w-8 text-muted-foreground" />
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground">Total Clicks</p>
								<p className="text-2xl font-bold">{totalClicks.toLocaleString()}</p>
							</div>
							<Edit className="h-8 w-8 text-muted-foreground" />
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground">Average CTR</p>
								<p className="text-2xl font-bold">{avgCTR}%</p>
							</div>
							<Plus className="h-8 w-8 text-muted-foreground" />
						</div>
					</CardContent>
				</Card>
			</div>

			<div className="flex items-center justify-between gap-4">
				<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
					<div className="flex items-center justify-between">
						<TabsList>
							<TabsTrigger value="all">All ({campaigns.length})</TabsTrigger>
							<TabsTrigger value="active">Active ({activeCampaigns.length})</TabsTrigger>
							<TabsTrigger value="pending">Pending ({pendingCampaigns.length})</TabsTrigger>
							<TabsTrigger value="approved">Approved ({approvedCampaigns.length})</TabsTrigger>
							<TabsTrigger value="rejected">Rejected ({rejectedCampaigns.length})</TabsTrigger>
							<TabsTrigger value="completed">Completed ({completedCampaigns.length})</TabsTrigger>
						</TabsList>

						<Link href="/dashboard/campaigns/create">
							<Button size="sm" className="h-8 gap-1">
								<Plus className="h-3.5 w-3.5" />
								<span className="sr-only sm:not-sr-only sm:whitespace-nowrap">New Campaign</span>
							</Button>
						</Link>
					</div>

					<TabsContent value="all" className="space-y-3 mt-4">
						{campaigns.length > 0 ? (
							campaigns.map(renderCampaignCard)
						) : (
							<div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
								<p className="text-muted-foreground mb-4">You don't have any campaigns yet.</p>
								<Link href="/dashboard/campaigns/create">
									<Button size="sm">
										<Plus className="mr-2 h-4 w-4" />
										Create New Campaign
									</Button>
								</Link>
							</div>
						)}
					</TabsContent>

					<TabsContent value="active" className="space-y-3 mt-4">
						{activeCampaigns.length > 0 ? (
							activeCampaigns.map(renderCampaignCard)
						) : (
							<div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
								<p className="text-muted-foreground mb-4">No active campaigns.</p>
								<Link href="/dashboard/campaigns/create">
									<Button size="sm">
										<Plus className="mr-2 h-4 w-4" />
										Create New Campaign
									</Button>
								</Link>
							</div>
						)}
					</TabsContent>

					<TabsContent value="pending" className="space-y-3 mt-4">
						{pendingCampaigns.length > 0 ? (
							pendingCampaigns.map(renderCampaignCard)
						) : (
							<div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
								<p className="text-muted-foreground mb-4">No pending campaigns.</p>
								<Link href="/dashboard/campaigns/create">
									<Button size="sm">
										<Plus className="mr-2 h-4 w-4" />
										Create New Campaign
									</Button>
								</Link>
							</div>
						)}
					</TabsContent>

					<TabsContent value="approved" className="space-y-3 mt-4">
						{approvedCampaigns.length > 0 ? (
							approvedCampaigns.map(renderCampaignCard)
						) : (
							<div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
								<p className="text-muted-foreground mb-4">No approved campaigns.</p>
								<Link href="/dashboard/campaigns/create">
									<Button size="sm">
										<Plus className="mr-2 h-4 w-4" />
										Create New Campaign
									</Button>
								</Link>
							</div>
						)}
					</TabsContent>

					<TabsContent value="rejected" className="space-y-3 mt-4">
						{rejectedCampaigns.length > 0 ? (
							rejectedCampaigns.map(renderCampaignCard)
						) : (
							<div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
								<p className="text-muted-foreground mb-4">No rejected campaigns.</p>
								<Link href="/dashboard/campaigns/create">
									<Button size="sm">
										<Plus className="mr-2 h-4 w-4" />
										Create New Campaign
									</Button>
								</Link>
							</div>
						)}
					</TabsContent>

					<TabsContent value="completed" className="space-y-3 mt-4">
						{completedCampaigns.length > 0 ? (
							completedCampaigns.map(renderCampaignCard)
						) : (
							<div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
								<p className="text-muted-foreground mb-4">No completed campaigns.</p>
								<Link href="/dashboard/campaigns/create">
									<Button size="sm">
										<Plus className="mr-2 h-4 w-4" />
										Create New Campaign
									</Button>
								</Link>
							</div>
						)}
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
}
