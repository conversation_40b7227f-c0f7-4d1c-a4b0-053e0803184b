const { asyncHandler } = require("../../middleware/errorHandler");
const PaymentReconciliationService = require("../../application/services/PaymentReconciliationService");
const { sendSuccess, sendError, sendUnauthorized, sendForbidden } = require("../../utils/responseWrapper");
const { isAdminUser } = require("../../application/services/AdminService");
const logger = require("../../../logger");

/**
 * Run payment reconciliation
 */
const runReconciliation = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { startDate, endDate, includeSuccessful = true, includeFailed = true } = req.body;

	try {
		const reconciliationService = new PaymentReconciliationService();

		const options = {
			includeSuccessful,
			includeFailed,
		};

		if (startDate) {
			options.startDate = new Date(startDate);
		}
		if (endDate) {
			options.endDate = new Date(endDate);
		}

		const report = await reconciliationService.runReconciliation(options);

		logger.info("Payment reconciliation completed", {
			reconciliationId: report.reconciliation_id,
			adminUserId: admin.user_id,
			totalDiscrepancies: report.summary.total_discrepancies,
		});

		return sendSuccess(res, report, "Payment reconciliation completed successfully");
	} catch (error) {
		logger.error("Payment reconciliation failed", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to run payment reconciliation", error.message, 500);
	}
});

/**
 * Get reconciliation summary statistics
 */
const getReconciliationStats = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { days = 30 } = req.query;

	try {
		const reconciliationService = new PaymentReconciliationService();
		const endDate = new Date();
		const startDate = new Date(endDate.getTime() - parseInt(days) * 24 * 60 * 60 * 1000);

		// Run live reconciliation for the period
		const report = await reconciliationService.runReconciliation({
			startDate,
			endDate,
			includeSuccessful: true,
			includeFailed: true,
		});

		// Generate statistics from the live report
		const stats = {
			period: {
				start_date: startDate.toISOString(),
				end_date: endDate.toISOString(),
				days: parseInt(days),
			},
			reconciliation_id: report.reconciliation_id,
			timestamp: report.timestamp,
			total_stripe_payments: report.total_stripe_payments,
			total_db_payments: report.total_db_payments,
			summary: report.summary,
			discrepancy_breakdown: {
				missing_in_db: report.discrepancies.filter((d) => d.type === "missing_in_db").length,
				missing_in_stripe: report.discrepancies.filter((d) => d.type === "missing_in_stripe").length,
				amount_mismatches: report.discrepancies.filter((d) => d.type === "amount_mismatch").length,
				status_mismatches: report.discrepancies.filter((d) => d.type === "status_mismatch").length,
			},
			critical_discrepancies: report.discrepancies.filter(
				(d) => d.type === "missing_in_db" || d.type === "amount_mismatch"
			).length,
		};

		return sendSuccess(res, stats, "Reconciliation statistics retrieved successfully");
	} catch (error) {
		logger.error("Failed to get reconciliation statistics", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to retrieve reconciliation statistics", error.message, 500);
	}
});

/**
 * Quick reconciliation check (last 24 hours)
 */
const quickReconciliationCheck = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const reconciliationService = new PaymentReconciliationService();

		// Run reconciliation for last 24 hours
		const report = await reconciliationService.runReconciliation({
			startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
			endDate: new Date(),
			includeSuccessful: true,
			includeFailed: true,
		});

		// Return only summary for quick check
		const quickSummary = {
			reconciliation_id: report.reconciliation_id,
			period: report.period,
			summary: report.summary,
			has_discrepancies: report.discrepancies.length > 0,
			critical_discrepancies: report.discrepancies.filter(
				(d) => d.type === "missing_in_db" || d.type === "amount_mismatch"
			).length,
		};

		return sendSuccess(res, quickSummary, "Quick reconciliation check completed");
	} catch (error) {
		logger.error("Quick reconciliation check failed", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to run quick reconciliation check", error.message, 500);
	}
});

module.exports = {
	runReconciliation,
	getReconciliationStats,
	quickReconciliationCheck,
};
