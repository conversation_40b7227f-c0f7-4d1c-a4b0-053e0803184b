const { asyncHand<PERSON> } = require("../../utils/asyncHandler");
const PaymentReconciliationService = require("../../application/services/PaymentReconciliationService");
const { sendSuccess, sendError, sendUnauthorized, sendForbidden } = require("../../utils/responseHelpers");
const { isAdminUser } = require("../../application/services/AdminService");
const logger = require("../../../logger");

/**
 * Run payment reconciliation
 */
const runReconciliation = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const {
		startDate,
		endDate,
		includeSuccessful = true,
		includeFailed = true,
	} = req.body;

	try {
		const reconciliationService = new PaymentReconciliationService();
		
		const options = {
			includeSuccessful,
			includeFailed,
		};

		if (startDate) {
			options.startDate = new Date(startDate);
		}
		if (endDate) {
			options.endDate = new Date(endDate);
		}

		const report = await reconciliationService.runReconciliation(options);

		logger.info("Payment reconciliation completed", {
			reconciliationId: report.reconciliation_id,
			adminUserId: admin.user_id,
			totalDiscrepancies: report.summary.total_discrepancies,
		});

		return sendSuccess(res, report, "Payment reconciliation completed successfully");

	} catch (error) {
		logger.error("Payment reconciliation failed", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to run payment reconciliation", error.message, 500);
	}
});

/**
 * Get reconciliation history
 */
const getReconciliationHistory = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const {
		limit = 20,
		offset = 0,
		startDate,
		endDate,
	} = req.query;

	try {
		const reconciliationService = new PaymentReconciliationService();
		
		const options = {
			limit: parseInt(limit),
			offset: parseInt(offset),
		};

		if (startDate) {
			options.startDate = new Date(startDate);
		}
		if (endDate) {
			options.endDate = new Date(endDate);
		}

		const reports = await reconciliationService.getReconciliationHistory(options);

		return sendSuccess(res, { reports }, "Reconciliation history retrieved successfully");

	} catch (error) {
		logger.error("Failed to get reconciliation history", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to retrieve reconciliation history", error.message, 500);
	}
});

/**
 * Get specific reconciliation report
 */
const getReconciliationReport = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { reconciliationId } = req.params;

	try {
		const reconciliationService = new PaymentReconciliationService();
		const report = await reconciliationService.getReconciliationReport(reconciliationId);

		return sendSuccess(res, report, "Reconciliation report retrieved successfully");

	} catch (error) {
		if (error.message.includes('not found')) {
			return sendError(res, "Reconciliation report not found", error.message, 404);
		}

		logger.error("Failed to get reconciliation report", {
			error: error.message,
			reconciliationId,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to retrieve reconciliation report", error.message, 500);
	}
});

/**
 * Get reconciliation summary statistics
 */
const getReconciliationStats = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { days = 30 } = req.query;

	try {
		const reconciliationService = new PaymentReconciliationService();
		const endDate = new Date();
		const startDate = new Date(endDate.getTime() - (parseInt(days) * 24 * 60 * 60 * 1000));

		const reports = await reconciliationService.getReconciliationHistory({
			startDate,
			endDate,
			limit: 1000, // Get all reports in period
		});

		// Calculate aggregate statistics
		const stats = {
			period: {
				start_date: startDate.toISOString(),
				end_date: endDate.toISOString(),
				days: parseInt(days),
			},
			total_reconciliations: reports.length,
			total_payments_checked: reports.reduce((sum, r) => sum + r.total_stripe_payments, 0),
			total_discrepancies: reports.reduce((sum, r) => sum + r.total_discrepancies, 0),
			average_match_rate: reports.length > 0 
				? (reports.reduce((sum, r) => sum + r.match_rate, 0) / reports.length).toFixed(2) + '%'
				: '0%',
			recent_reports: reports.slice(0, 5).map(report => ({
				reconciliation_id: report.reconciliation_id,
				created_at: report.created_at,
				match_rate: report.match_rate + '%',
				total_discrepancies: report.total_discrepancies,
			})),
		};

		// Categorize discrepancy types from recent reports
		const discrepancyTypes = {
			missing_in_db: 0,
			missing_in_stripe: 0,
			amount_mismatches: 0,
			status_mismatches: 0,
		};

		for (const report of reports.slice(0, 10)) { // Check last 10 reports
			try {
				const reportData = JSON.parse(report.report_data);
				for (const discrepancy of reportData.discrepancies || []) {
					switch (discrepancy.type) {
						case 'missing_in_db':
							discrepancyTypes.missing_in_db++;
							break;
						case 'missing_in_stripe':
							discrepancyTypes.missing_in_stripe++;
							break;
						case 'amount_mismatch':
							discrepancyTypes.amount_mismatches++;
							break;
						case 'status_mismatch':
							discrepancyTypes.status_mismatches++;
							break;
					}
				}
			} catch (parseError) {
				// Skip reports with invalid JSON
				continue;
			}
		}

		stats.discrepancy_breakdown = discrepancyTypes;

		return sendSuccess(res, stats, "Reconciliation statistics retrieved successfully");

	} catch (error) {
		logger.error("Failed to get reconciliation statistics", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to retrieve reconciliation statistics", error.message, 500);
	}
});

/**
 * Export reconciliation report as CSV
 */
const exportReconciliationReport = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	const { reconciliationId } = req.params;

	try {
		const reconciliationService = new PaymentReconciliationService();
		const report = await reconciliationService.getReconciliationReport(reconciliationId);

		// Generate CSV content
		const csvHeaders = [
			'Payment Intent ID',
			'Type',
			'Stripe Amount',
			'DB Amount',
			'Stripe Status',
			'DB Status',
			'Details',
		];

		const csvRows = [csvHeaders.join(',')];

		for (const discrepancy of report.report_data.discrepancies) {
			const row = [
				discrepancy.payment_intent_id,
				discrepancy.type,
				discrepancy.stripe_payment?.amount || 'N/A',
				discrepancy.db_payment?.amount || 'N/A',
				discrepancy.stripe_payment?.status || 'N/A',
				discrepancy.db_payment?.status || 'N/A',
				`"${discrepancy.details}"`,
			];
			csvRows.push(row.join(','));
		}

		const csvContent = csvRows.join('\n');
		const filename = `reconciliation_${reconciliationId}_${new Date().toISOString().split('T')[0]}.csv`;

		res.setHeader('Content-Type', 'text/csv');
		res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
		res.send(csvContent);

		logger.info("Reconciliation report exported", {
			reconciliationId,
			adminUserId: admin.user_id,
			filename,
		});

	} catch (error) {
		if (error.message.includes('not found')) {
			return sendError(res, "Reconciliation report not found", error.message, 404);
		}

		logger.error("Failed to export reconciliation report", {
			error: error.message,
			reconciliationId,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to export reconciliation report", error.message, 500);
	}
});

/**
 * Quick reconciliation check (last 24 hours)
 */
const quickReconciliationCheck = asyncHandler(async (req, res) => {
	const auth0_id = req.auth?.sub;
	if (!auth0_id) {
		return sendUnauthorized(res, "Authentication required");
	}

	const admin = await isAdminUser(auth0_id);
	if (!admin) {
		return sendForbidden(res, "Admin access required");
	}

	try {
		const reconciliationService = new PaymentReconciliationService();
		
		// Run reconciliation for last 24 hours
		const report = await reconciliationService.runReconciliation({
			startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
			endDate: new Date(),
			includeSuccessful: true,
			includeFailed: true,
		});

		// Return only summary for quick check
		const quickSummary = {
			reconciliation_id: report.reconciliation_id,
			period: report.period,
			summary: report.summary,
			has_discrepancies: report.discrepancies.length > 0,
			critical_discrepancies: report.discrepancies.filter(d => 
				d.type === 'missing_in_db' || d.type === 'amount_mismatch'
			).length,
		};

		return sendSuccess(res, quickSummary, "Quick reconciliation check completed");

	} catch (error) {
		logger.error("Quick reconciliation check failed", {
			error: error.message,
			adminUserId: admin.user_id,
		});
		return sendError(res, "Failed to run quick reconciliation check", error.message, 500);
	}
});

module.exports = {
	runReconciliation,
	getReconciliationHistory,
	getReconciliationReport,
	getReconciliationStats,
	exportReconciliationReport,
	quickReconciliationCheck,
};
