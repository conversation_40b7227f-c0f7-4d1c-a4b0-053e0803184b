// Ad Error Logging Utility
// Comprehensive logging for ad serving failures and fallback scenarios

export interface AdErrorLog {
  timestamp: string;
  slotId: number;
  errorType: 'paid_ad_failure' | 'adsense_failure' | 'complete_failure' | 'timeout' | 'network_error';
  errorMessage: string;
  userAgent?: string;
  url?: string;
  retryCount?: number;
  fallbackUsed?: 'google' | 'placeholder' | 'none';
  sessionId?: string;
}

export interface AdPerformanceMetrics {
  slotId: number;
  paidAdSuccess: boolean;
  adsenseSuccess: boolean;
  loadTime: number;
  fallbackUsed: boolean;
  timestamp: string;
}

class AdErrorLogger {
  private static instance: AdErrorLogger;
  private errorQueue: AdErrorLog[] = [];
  private metricsQueue: AdPerformanceMetrics[] = [];
  private isOnline = true;

  private constructor() {
    // Monitor online status
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.isOnline = true;
        this.flushQueues();
      });
      window.addEventListener('offline', () => {
        this.isOnline = false;
      });
    }

    // Flush queues periodically
    setInterval(() => {
      if (this.isOnline) {
        this.flushQueues();
      }
    }, 30000); // Every 30 seconds
  }

  public static getInstance(): AdErrorLogger {
    if (!AdErrorLogger.instance) {
      AdErrorLogger.instance = new AdErrorLogger();
    }
    return AdErrorLogger.instance;
  }

  public logAdError(error: Omit<AdErrorLog, 'timestamp' | 'url' | 'userAgent'>): void {
    const errorLog: AdErrorLog = {
      ...error,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Ad Error:', errorLog);
    }

    // Add to queue for batch sending
    this.errorQueue.push(errorLog);

    // Send immediately for critical errors
    if (error.errorType === 'complete_failure') {
      this.flushQueues();
    }

    // Limit queue size
    if (this.errorQueue.length > 100) {
      this.errorQueue = this.errorQueue.slice(-50); // Keep last 50 errors
    }
  }

  public logAdPerformance(metrics: Omit<AdPerformanceMetrics, 'timestamp'>): void {
    const performanceLog: AdPerformanceMetrics = {
      ...metrics,
      timestamp: new Date().toISOString(),
    };

    this.metricsQueue.push(performanceLog);

    // Limit queue size
    if (this.metricsQueue.length > 100) {
      this.metricsQueue = this.metricsQueue.slice(-50);
    }
  }

  private async flushQueues(): Promise<void> {
    if (!this.isOnline || (this.errorQueue.length === 0 && this.metricsQueue.length === 0)) {
      return;
    }

    try {
      // Send error logs
      if (this.errorQueue.length > 0) {
        await this.sendErrorLogs([...this.errorQueue]);
        this.errorQueue = [];
      }

      // Send performance metrics
      if (this.metricsQueue.length > 0) {
        await this.sendPerformanceMetrics([...this.metricsQueue]);
        this.metricsQueue = [];
      }
    } catch (error) {
      console.error('Failed to flush ad logs:', error);
      // Keep logs in queue for retry
    }
  }

  private async sendErrorLogs(logs: AdErrorLog[]): Promise<void> {
    try {
      const response = await fetch('/api/ads/error-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ errors: logs }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send error logs: ${response.status}`);
      }
    } catch (error) {
      console.error('Error sending ad error logs:', error);
      throw error;
    }
  }

  private async sendPerformanceMetrics(metrics: AdPerformanceMetrics[]): Promise<void> {
    try {
      const response = await fetch('/api/ads/performance-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ metrics }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send performance metrics: ${response.status}`);
      }
    } catch (error) {
      console.error('Error sending ad performance metrics:', error);
      throw error;
    }
  }

  // Public method to manually flush queues (useful for page unload)
  public async flush(): Promise<void> {
    await this.flushQueues();
  }
}

// Singleton instance
export const adErrorLogger = AdErrorLogger.getInstance();

// Convenience functions
export function logAdError(error: Omit<AdErrorLog, 'timestamp' | 'url' | 'userAgent'>): void {
  adErrorLogger.logAdError(error);
}

export function logAdPerformance(metrics: Omit<AdPerformanceMetrics, 'timestamp'>): void {
  adErrorLogger.logAdPerformance(metrics);
}

// Auto-flush on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    adErrorLogger.flush().catch(console.error);
  });

  // Also flush on visibility change (mobile browsers)
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      adErrorLogger.flush().catch(console.error);
    }
  });
}

// Error boundary for ad components
export function withAdErrorBoundary<T extends object>(
  Component: React.ComponentType<T>,
  slotId: number
): React.ComponentType<T> {
  return function AdErrorBoundaryWrapper(props: T) {
    try {
      return React.createElement(Component, props);
    } catch (error) {
      logAdError({
        slotId,
        errorType: 'complete_failure',
        errorMessage: error instanceof Error ? error.message : 'Unknown component error',
        fallbackUsed: 'placeholder',
      });

      // Return fallback UI
      return React.createElement('div', {
        style: {
          width: '300px',
          height: '250px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f8fafc',
          border: '2px dashed #e2e8f0',
          borderRadius: '8px',
          color: '#64748b',
          fontSize: '14px',
          textAlign: 'center',
        },
        children: 'Advertisement Error',
      });
    }
  };
}
