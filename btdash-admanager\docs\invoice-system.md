# Invoice System Documentation

## Overview

The BTDash Ad Manager now includes a comprehensive invoice system that replaces the previous approach of using payment intent IDs as invoice references. This system provides proper invoice generation, PDF creation, and download functionality.

## Architecture

### Database Schema

The invoice system uses three main tables:

1. **`dtm_ads.invoices`** - Main invoice records
2. **`dtm_ads.invoice_line_items`** - Detailed line items for each invoice
3. **`dtm_ads.billing_transactions`** - Updated to reference proper invoices

### Key Features

- ✅ **Proper Invoice Numbers**: Human-readable format (INV-2024-0001)
- ✅ **Stripe Integration**: Full integration with Stripe's invoice API
- ✅ **PDF Generation**: Automatic PDF creation with professional formatting
- ✅ **Line Item Support**: Detailed billing breakdown
- ✅ **Download Functionality**: Direct PDF download from billing page
- ✅ **Campaign Integration**: Automatic invoice creation on campaign approval

## API Endpoints

### User Endpoints (Authenticated)

```
GET /api/invoices                    # Get user's invoices
GET /api/invoices/:id                # Get specific invoice details
GET /api/invoices/:id/pdf            # Generate PDF for invoice
GET /api/invoices/:id/download       # Download invoice PDF
```

### Internal Endpoints (Internal API Key Required)

```
POST /api/invoices/create            # Create new invoice
POST /api/invoices/:id/finalize      # Finalize draft invoice
POST /api/invoices/mark-paid         # Mark invoice as paid (webhook)
POST /api/invoices/associate-payment # Associate payment intent
```

### Admin Endpoints

```
GET /api/invoices/admin/stats        # Get invoice statistics
```

## Frontend Integration

### Billing Page Updates

The billing page (`/dashboard/billing`) now includes:

- **Invoice Tab**: Displays actual invoices instead of transactions
- **Download Buttons**: Direct PDF download functionality
- **Status Indicators**: Clear invoice status (Draft, Open, Paid, etc.)
- **Proper Invoice Numbers**: Human-readable invoice references

### API Routes

Frontend API routes handle authentication and proxy requests:

```
GET /api/user/invoices               # User's invoices
GET /api/user/invoices/[id]          # Invoice details
GET /api/user/invoices/[id]/download # PDF download
```

## Workflow Integration

### Campaign Approval Process

1. **Admin Approves Campaign**
   - Campaign status → "approved"
   - Invoice automatically created
   - Billing transaction created (backward compatibility)

2. **User Makes Payment**
   - Payment intent created with campaign metadata
   - Invoice associated with payment intent
   - Payment processed through Stripe

3. **Payment Success**
   - Invoice marked as "paid"
   - Campaign activated
   - PDF automatically generated

### Invoice Lifecycle

```
Draft → Open → Paid
  ↓      ↓      ↓
Created  Finalized  Payment Received
```

## PDF Generation

### Features

- **Professional Layout**: Company branding and formatting
- **Detailed Information**: Customer details, line items, totals
- **Payment Status**: Clear indication of payment status
- **Automatic Generation**: PDFs created on first access
- **Caching**: Generated PDFs stored for future downloads

### File Storage

PDFs are stored in: `uploads/invoices/invoice-{invoice-number}.pdf`

## Database Migration

### Required Steps

1. **Run Migration Script**:
   ```bash
   psql -d your_database -f database_invoice_system.sql
   ```

2. **Install Dependencies**:
   ```bash
   cd btdashs_api
   npm install pdfkit stripe
   ```

3. **Update Environment Variables**:
   ```env
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

### Migration Notes

- Maintains backward compatibility with existing billing_transactions
- Adds proper foreign key relationships
- Includes invoice numbering sequence
- Supports historical data migration

## Configuration

### Required Environment Variables

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_CURRENCY=usd

# API Configuration
API_BASE_URL=http://localhost:3001
INTERNAL_API_KEY=your-internal-key
```

### File Permissions

Ensure the uploads directory is writable:
```bash
mkdir -p uploads/invoices
chmod 755 uploads/invoices
```

## Testing

### Test Invoice Creation

```javascript
// Create test invoice
const invoiceService = new InvoiceService();
const invoice = await invoiceService.createInvoice({
  advertiserId: 1,
  customerId: 'cus_test123',
  campaignId: 1,
  amount: 100.00,
  currency: 'USD',
  description: 'Test Campaign Payment',
  lineItems: [{
    description: 'Test Campaign - Total Budget',
    quantity: 1,
    unitPrice: 100.00
  }]
});
```

### Test PDF Generation

```javascript
// Generate PDF
const pdfService = new PDFInvoiceService();
const pdfPath = await pdfService.generateInvoicePDF(invoiceId);
```

## Error Handling

### Common Issues

1. **PDF Generation Fails**
   - Check file permissions on uploads directory
   - Verify PDFKit dependency is installed
   - Check disk space availability

2. **Invoice Creation Fails**
   - Verify database schema is up to date
   - Check foreign key constraints
   - Validate input data

3. **Stripe Integration Issues**
   - Verify API keys are correct
   - Check webhook configuration
   - Monitor Stripe dashboard for errors

### Logging

All invoice operations are logged with appropriate detail levels:
- Info: Successful operations
- Warn: Non-critical issues (e.g., invoice update failures)
- Error: Critical failures requiring attention

## Security Considerations

### Access Control

- **User Invoices**: Users can only access their own invoices
- **Admin Functions**: Require admin authentication
- **Internal APIs**: Protected by internal API key
- **PDF Downloads**: Authenticated and authorized

### Data Protection

- Invoice data includes sensitive financial information
- PDFs contain customer details and payment information
- Proper access controls prevent unauthorized access
- File storage should be secured and backed up

## Monitoring

### Key Metrics

- Invoice creation success rate
- PDF generation performance
- Download frequency
- Payment processing time

### Alerts

Set up monitoring for:
- Failed invoice creations
- PDF generation errors
- Webhook processing failures
- Disk space for PDF storage

## Future Enhancements

### Planned Features

- **Tax Calculation**: Automatic tax computation based on location
- **Multi-Currency**: Support for multiple currencies
- **Invoice Templates**: Customizable invoice layouts
- **Batch Operations**: Bulk invoice processing
- **Email Delivery**: Automatic invoice email sending

### Integration Opportunities

- **Accounting Systems**: QuickBooks, Xero integration
- **Payment Gateways**: Additional payment method support
- **Reporting**: Advanced financial reporting
- **Automation**: Recurring billing support
