# BTDash Data Relationships - Corrected

## Overview

This document clarifies the correct data relationships in the BTDash ecosystem, specifically addressing the confusion between advertiser IDs and manager IDs in the invoice system.

## ✅ **Corrected Relationships**

### **Campaign Structure**
```
ad_campaigns table:
├── advertiser_id → dtm_base.companies.id (Company that owns the campaign)
├── manager_id → dtm_base.users.id (User who manages the campaign)
├── name, budget, dates, etc.
└── targeting (JSON)
```

### **Invoice Structure** 
```
invoices table:
├── manager_id → dtm_base.users.id (User who will pay - campaign manager)
├── advertiser_id → dtm_base.companies.id (Company being advertised)
├── campaign_id → dtm_ads.ad_campaigns.id
├── stripe_invoice_id, amounts, etc.
└── PDF generation fields
```

### **Billing Transactions**
```
billing_transactions table:
├── advertiser_id → dtm_base.users.id (User who pays - should be manager_id)
├── campaign_id → dtm_ads.ad_campaigns.id
├── payment_intent_reference (Stripe payment intent ID)
└── amount, status, etc.
```

## 🔧 **Key Corrections Made**

### 1. **Database Schema Updates**
- **invoices table**: Added `manager_id` as primary user reference
- **Foreign Keys**: 
  - `manager_id` → `dtm_base.users.id` (who pays)
  - `advertiser_id` → `dtm_base.companies.id` (company being advertised)

### 2. **Service Layer Corrections**
- **InvoiceService**: Updated to use `managerId` for user operations
- **AdminService**: Fixed campaign approval to use correct IDs
- **PDFInvoiceService**: Updated to show company name and manager contact

### 3. **Controller Updates**
- **invoiceController**: All user authorization uses `manager_id`
- **billingController**: Billing transactions use `manager_id` for user operations

## 📊 **Data Flow Example**

### Campaign Creation:
```javascript
// Frontend sends:
{
  advertiser_id: companyId,  // Company ID (e.g., 123)
  manager_id: userId,        // User ID (e.g., 456)
  name: "Summer Campaign",
  total_budget: 1000.00
}

// Database stores:
ad_campaigns: {
  id: 789,
  advertiser_id: 123,  // → dtm_base.companies.id
  manager_id: 456,     // → dtm_base.users.id
  name: "Summer Campaign",
  total_budget: 1000.00
}
```

### Invoice Creation (on approval):
```javascript
// AdminService creates:
invoices: {
  id: 101,
  manager_id: 456,      // User who pays (campaign manager)
  advertiser_id: 123,   // Company being advertised
  campaign_id: 789,     // Campaign reference
  total_amount: 1000.00,
  stripe_invoice_id: "in_..."
}
```

### Billing Transaction:
```javascript
// Payment processing creates:
billing_transactions: {
  id: 202,
  advertiser_id: 456,   // User who pays (manager_id from campaign)
  campaign_id: 789,     // Campaign reference
  amount: 1000.00,
  payment_intent_reference: "pi_..."
}
```

## 🎯 **User Experience**

### **From User Perspective:**
1. **User** creates campaigns for their **Company**
2. **User** pays for campaigns (invoices sent to user)
3. **Company** gets advertised (ads show company info)
4. **User** manages campaign performance and billing

### **Invoice Display:**
- **Bill To**: Company name (if available) or User name
- **Contact**: User email (campaign manager)
- **Website**: Company website (if available)
- **Payment**: Charged to user's Stripe customer account

## 🔍 **Authorization Logic**

### **Invoice Access:**
```javascript
// User can only access invoices where they are the manager
const invoice = await db("dtm_ads.invoices")
  .where({ id: invoiceId, manager_id: userId })
  .first();
```

### **Campaign Access:**
```javascript
// User can only access campaigns where they are the manager
const campaign = await db("dtm_ads.ad_campaigns")
  .where({ id: campaignId, manager_id: userId })
  .first();
```

## 📋 **Migration Notes**

### **Required Database Changes:**
1. **Add manager_id column** to invoices table
2. **Update foreign key constraints** to reference correct tables
3. **Migrate existing data** if any invoices exist
4. **Update indexes** for performance

### **Backward Compatibility:**
- Billing transactions table maintains existing structure
- Old invoice references preserved during migration
- API endpoints maintain same interface

## 🚨 **Important Distinctions**

| Field | References | Purpose | Example |
|-------|------------|---------|---------|
| `campaign.advertiser_id` | `companies.id` | Company being advertised | "Acme Corp" |
| `campaign.manager_id` | `users.id` | User managing campaign | "<EMAIL>" |
| `invoice.manager_id` | `users.id` | User who pays invoice | "<EMAIL>" |
| `invoice.advertiser_id` | `companies.id` | Company being advertised | "Acme Corp" |
| `billing_transactions.advertiser_id` | `users.id` | User who pays (legacy naming) | "<EMAIL>" |

## ✅ **Verification Checklist**

- [ ] Invoice creation uses correct manager_id and advertiser_id
- [ ] User authorization checks manager_id (not advertiser_id)
- [ ] PDF generation shows company name and user contact
- [ ] Billing transactions reference correct user for payments
- [ ] Campaign approval creates invoices with proper relationships
- [ ] Frontend displays correct company and user information

## 🔄 **Next Steps**

1. **Run database migration** to update schema
2. **Test invoice creation** end-to-end
3. **Verify PDF generation** shows correct information
4. **Test user authorization** for invoice access
5. **Validate payment processing** uses correct user references

This correction ensures that:
- **Users** manage and pay for campaigns
- **Companies** are the entities being advertised
- **Invoices** are properly associated with both user and company
- **Authorization** works correctly for user access control
